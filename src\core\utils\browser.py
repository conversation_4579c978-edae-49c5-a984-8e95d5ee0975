import os
import json
import time
import logging
from urllib.parse import quote, urle<PERSON>de
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait

# Setup logger
logger = logging.getLogger('zpammer.browser')

class BrowserManager:
    """
    Manages browser instances for automation tasks.

    This class handles:
    - Launching Chrome with specific profiles
    - Connecting to existing Chrome instances
    - Managing browser sessions
    """

    def __init__(self, config_path=None):
        """
        Initialize the browser manager.

        Args:
            config_path (str, optional): Path to the configuration file.
        """
        self.config = self._load_config(config_path)
        self.active_drivers = {}  # Dictionary to store active WebDriver instances

    def _load_config(self, config_path=None):
        """
        Load configuration from file.

        Args:
            config_path (str, optional): Path to the configuration file.

        Returns:
            dict: Configuration dictionary.
        """
        if config_path is None:
            # Try multiple possible locations for config.json
            possible_paths = [
                os.path.join("data", "config.json"),
                os.path.join("Files", "config.json"),
                "config.json",
                os.path.join("..", "Files", "config.json")
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break
            else:
                # If no config file found, use the first path as default
                config_path = possible_paths[0]
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(config_path), exist_ok=True)

        try:
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                logger.info(f"Loaded configuration from {config_path}")
                return config
            else:
                logger.warning(f"Configuration file {config_path} not found. Using default configuration.")
                # Return default configuration
                default_config = {
                    "browser": {
                        "chrome_path": "",
                        "user_data_path": "",
                        "chromedriver_path": "",
                        "headless_mode": False,
                        "auto_close": True,
                    }
                }

                # Save default configuration
                try:
                    os.makedirs(os.path.dirname(config_path), exist_ok=True)
                    with open(config_path, "w", encoding="utf-8") as f:
                        json.dump(default_config, f, indent=4)
                    logger.info(f"Created default configuration at {config_path}")
                except Exception as e:
                    logger.error(f"Failed to create default configuration: {e}")

                return default_config
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Return default configuration
            return {
                "browser": {
                    "chrome_path": "",
                    "user_data_path": "",
                    "chromedriver_path": "",
                    "headless_mode": False,
                    "auto_close": True,
                }
            }

    def launch_nstbrowser_executable(self):
        """
        Assumes NstBrowser is already running and returns True without checking.

        Returns:
            bool: Always returns True since we assume NstBrowser is running.
        """
        logger.info("Assuming NstBrowser is already running.")
        return True

    def get_debugger_port(self, url, max_retries=10, retry_interval=5):
        """
        Get debugger port from the browser launcher API.

        Args:
            url (str): URL to the browser launcher API.
            max_retries (int, optional): Maximum number of retries.
            retry_interval (int, optional): Interval between retries in seconds.

        Returns:
            int or None: Debugger port if successful, None otherwise.
        """
        # Try to launch NstBrowser if using it
        if "nstbrowser" in self.config and self.config["nstbrowser"].get("executable_path"):
            self.launch_nstbrowser_executable()

        retries = 0
        while retries < max_retries:
            try:
                logger.info(f"Attempting to retrieve debugger port (Attempt {retries + 1}/{max_retries})...")
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                res = response.json()
                if 'data' not in res or 'port' not in res['data']:
                    raise ValueError(f"Unexpected response structure: {res}")
                return res['data']['port']
            except Exception as e:
                logger.warning(f"Error getting debugger port: {e}. Retrying in {retry_interval} seconds...")
                time.sleep(retry_interval)
                retries += 1

        logger.error("Failed to retrieve debugger port after maximum retries.")
        return None

    def get_browser(self, profile_id):
        """
        Get an existing browser instance for a specific profile or launch a new one if needed.
        This function assumes NstBrowser is already running and tries to connect to it.

        Args:
            profile_id (str): Profile ID to use.

        Returns:
            webdriver.Chrome or None: WebDriver instance if successful, None otherwise.
        """
        # First check if we already have an active driver for this profile
        if profile_id in self.active_drivers:
            logger.info(f"Using existing browser for profile {profile_id}")
            return self.active_drivers[profile_id]

        # Try to connect to the browser instance (assuming NstBrowser is already running)
        try:
            # Prepare API request for NSTBrowser
            host = self.config["nstbrowser"].get("host", "127.0.0.1")
            port = self.config["nstbrowser"].get("port", 8848)
            api_key = self.config["nstbrowser"].get("api_key", "fc63ee6b-0785-4b2a-a179-d6ae22c88479")

            # Create URL to check if profile is running
            query = urlencode({'x-api-key': api_key})
            url = f'http://{host}:{port}/devtool/check/{profile_id}?{query}'

            # Send request to check if profile is running
            response = requests.get(url, timeout=10)
            if response.status_code == 200 and response.json().get('running', False):
                # Profile is running, get debugger port
                debugger_port = response.json().get('port')
                if debugger_port:
                    # Connect to browser
                    debugger_address = f'{host}:{debugger_port}'
                    logger.info(f"Connecting to existing Chrome at {debugger_address}")

                    options = Options()
                    options.debugger_address = debugger_address
                    driver = webdriver.Chrome(options=options)
                    self.active_drivers[profile_id] = driver
                    logger.info(f"Successfully connected to existing Chrome for profile {profile_id}")
                    return driver

            # If profile is not running, launch it
            logger.info(f"Profile {profile_id} is not running, launching it...")
            return self.launch_browser(profile_id)
        except Exception as e:
            logger.error(f"Error connecting to profile {profile_id}: {e}")
            # Try launching a new browser instance as fallback
            logger.info(f"Attempting to launch profile {profile_id}")
            return self.launch_browser(profile_id)

    def launch_browser(self, profile_id, headless=None, max_retries=3):
        """
        Launch a new Chrome browser instance with the specified profile.

        Args:
            profile_id (str): Profile ID to use.
            headless (bool, optional): Whether to run in headless mode.
                If None, use the value from the configuration.
            max_retries (int, optional): Maximum number of retries for launching the browser.

        Returns:
            webdriver.Chrome or None: WebDriver instance if successful, None otherwise.
        """
        if profile_id in self.active_drivers:
            logger.warning(f"Browser for profile {profile_id} is already running.")
            return self.active_drivers[profile_id]

        # First, kill any existing NstChrome processes to ensure a clean start
        self.kill_nstchrome_processes()
        time.sleep(2)  # Give time for processes to fully terminate

        # Use headless mode from configuration if not specified
        if headless is None:
            # Check if we should use NSTBrowser or regular Chrome
            if "nstbrowser" in self.config and self.config["nstbrowser"].get("api_key"):
                headless = self.config["nstbrowser"].get("headless_mode", False)
            else:
                headless = self.config["browser"].get("headless_mode", False)

        # Check if we should use NSTBrowser
        if "nstbrowser" in self.config and self.config["nstbrowser"].get("api_key"):
            # Prepare API request for NSTBrowser
            host = self.config["nstbrowser"].get("host", "127.0.0.1")
            port = self.config["nstbrowser"].get("port", 8848)
            api_key = self.config["nstbrowser"].get("api_key", "fc63ee6b-0785-4b2a-a179-d6ae22c88479")

            # Log API settings for debugging
            logger.info(f"Using NSTBrowser API - Host: {host}, Port: {port}, API Key: {api_key[:5]}...")

            # Check if NSTBrowser API is accessible
            try:
                health_url = f'http://{host}:{port}/health'
                health_response = requests.get(health_url, timeout=5)
                if health_response.status_code == 200:
                    logger.info("NSTBrowser API is accessible")
                else:
                    logger.warning(f"NSTBrowser API health check failed with status code: {health_response.status_code}")
            except Exception as e:
                logger.warning(f"NSTBrowser API health check failed: {e}")
                logger.info("Proceeding with launch attempt anyway...")

            # Use headless mode from config
            # Log the headless mode setting for debugging
            headless_mode = self.config["nstbrowser"].get("headless_mode", False)
            logger.info(f"Launching browser with headless mode: {headless_mode}")

            config_dict = {
                'headless': headless_mode,
                'autoClose': self.config["nstbrowser"].get("auto_close", True),
            }
            query = urlencode({'x-api-key': api_key, 'config': quote(json.dumps(config_dict))})
            url = f'http://{host}:{port}/devtool/launch/{profile_id}?{query}'

            # Attempt to launch browser with retries
            for attempt in range(max_retries):
                try:
                    logger.info(f"Attempt {attempt + 1}/{max_retries} to launch browser for profile {profile_id}")

                    # Get debugger port
                    port = self.get_debugger_port(url)
                    if not port:
                        logger.error(f"Failed to retrieve debugger port for profile {profile_id} on attempt {attempt + 1}")
                        if attempt < max_retries - 1:
                            logger.info("Killing NstChrome processes and retrying...")
                            self.kill_nstchrome_processes()
                            time.sleep(3)  # Wait longer between retries
                            continue
                        else:
                            return None

                    # Connect to browser
                    debugger_address = f'{host}:{port}'
                    logger.info(f"Connecting to Chrome at {debugger_address}")

                    options = Options()
                    options.debugger_address = debugger_address
                    driver = webdriver.Chrome(options=options)

                    # Test the connection by executing a simple JavaScript
                    driver.execute_script("return document.readyState")

                    # If we get here, the connection is successful
                    self.active_drivers[profile_id] = driver
                    logger.info(f"Successfully connected to Chrome for profile {profile_id}")
                    return driver

                except Exception as e:
                    logger.error(f"Failed to connect to Chrome on attempt {attempt + 1}: {e}")
                    if attempt < max_retries - 1:
                        logger.info("Killing NstChrome processes and retrying...")
                        self.kill_nstchrome_processes()
                        time.sleep(3)  # Wait longer between retries
                    else:
                        logger.error(f"Failed to launch browser after {max_retries} attempts")
                        return None

            return None  # Should not reach here, but just in case
        else:
            logger.error("NSTBrowser API key not configured")
            return None

    def get_browser_for_profile(self, profile_id, max_retries=3):
        """
        Get a browser instance for a specific profile ID.
        This is a convenience method that attempts to launch a browser with the specified profile.

        Args:
            profile_id (str): Profile ID to use.
            max_retries (int, optional): Maximum number of retries for launching the browser.

        Returns:
            webdriver.Chrome or None: WebDriver instance if successful, None otherwise.
        """
        logger.info(f"Getting browser instance for profile {profile_id}")

        # First check if we already have an active driver for this profile
        if profile_id in self.active_drivers:
            logger.info(f"Using existing browser for profile {profile_id}")
            return self.active_drivers[profile_id]

        # Try to launch the browser with retries
        for attempt in range(max_retries):
            try:
                logger.info(f"Attempting to launch browser with max retries")
                driver = self.launch_browser(profile_id)
                if driver:
                    logger.info(f"Successfully got browser instance for profile {profile_id}")
                    return driver
                else:
                    logger.warning(f"Failed to get browser instance on attempt {attempt + 1}/{max_retries}")
                    time.sleep(2)  # Wait before retrying
            except Exception as e:
                logger.error(f"Error getting browser instance on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retrying

        logger.error(f"Failed to get browser instance for profile {profile_id} after {max_retries} attempts")
        return None

    def close_browser(self, driver):
        """
        Close a browser instance.

        Args:
            driver (webdriver.Chrome): WebDriver instance to close.

        Returns:
            bool: True if successful, False otherwise.
        """
        if not driver:
            logger.warning("No driver provided to close")
            return False

        try:
            # Find the profile ID for this driver
            profile_id = None
            for pid, drv in self.active_drivers.items():
                if drv == driver:
                    profile_id = pid
                    break

            # Close the browser
            driver.quit()

            # Remove from active drivers if found
            if profile_id and profile_id in self.active_drivers:
                del self.active_drivers[profile_id]
                logger.info(f"Closed browser for profile {profile_id}")
            else:
                logger.info("Closed browser (unknown profile)")

            return True
        except Exception as e:
            logger.error(f"Failed to close browser: {e}")
            return False

    def get_current_driver(self):
        """
        Get the current active driver instance.

        Returns:
            webdriver.Chrome or None: The current active WebDriver instance, or None if no active driver.
        """
        if not self.active_drivers:
            logger.warning("No active browser instances found")
            return None

        # Return the first active driver (most recent one)
        return next(iter(self.active_drivers.values()))

    def close_all_browsers(self):
        """
        Close all active browser instances and clean up resources.

        Returns:
            bool: True if all browsers were closed successfully, False otherwise.
        """
        success = True
        # Make a copy of the drivers to avoid modification during iteration
        drivers = list(self.active_drivers.values())

        if not drivers:
            logger.info("No active browser instances to close")
            return True

        logger.info(f"Closing {len(drivers)} active browser instances")

        # First try to close browsers gracefully
        for driver in drivers:
            try:
                if not self.close_browser(driver):
                    success = False
                    logger.warning(f"Failed to close browser gracefully")
            except Exception as e:
                success = False
                logger.error(f"Error closing browser: {e}")

        # If there are still active drivers, force quit them
        if self.active_drivers:
            logger.warning(f"Forcing quit for {len(self.active_drivers)} remaining browser instances")
            for profile_id, driver in list(self.active_drivers.items()):
                try:
                    driver.quit()
                except Exception:
                    pass
                finally:
                    # Remove from active drivers regardless of success
                    if profile_id in self.active_drivers:
                        del self.active_drivers[profile_id]

        # Finally, kill any remaining NstChrome processes
        self.kill_nstchrome_processes()

        return success

    def is_logged_in(self, driver):
        """
        Check if the browser is logged in to Facebook.

        Args:
            driver (webdriver.Chrome): WebDriver instance.

        Returns:
            bool: True if logged in, False otherwise.
        """
        try:
            driver.get("https://www.facebook.com/")
            WebDriverWait(driver, 10).until(lambda d: d.execute_script("return document.readyState") == "complete")

            # Check for login indicators
            login_keywords = ["تسجيل الدخول", "Log in", "Se connecter"]
            for keyword in login_keywords:
                try:
                    driver.find_element(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                    return False
                except:
                    continue

            return True
        except Exception as e:
            logger.error(f"Error during login check: {e}")
            return False

    def kill_nstchrome_processes(self):
        """
        Kill all NstChrome processes using multiple methods for redundancy.

        Returns:
            bool: True if at least one method was successful, False if all methods failed.
        """
        success = False

        # Method 1: Using os.system (original method)
        try:
            if os.name == 'nt':  # Windows
                os.system("taskkill /F /IM nstchrome.exe > NUL 2>&1")
                logger.info("Attempted to kill NstChrome processes using os.system")
                success = True
        except Exception as e:
            logger.warning(f"Error killing NstChrome processes using os.system: {e}")

        # Method 2: Using subprocess (more robust with error handling)
        try:
            import subprocess
            if os.name == 'nt':  # Windows
                subprocess.run("taskkill /F /IM nstchrome.exe",
                               shell=True,
                               stdout=subprocess.DEVNULL,
                               stderr=subprocess.DEVNULL)
                logger.info("Attempted to kill NstChrome processes using subprocess")
                success = True
        except Exception as e:
            logger.warning(f"Error killing NstChrome processes using subprocess: {e}")

        # Method 3: Using psutil (most reliable but requires additional dependency)
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'nstchrome' in proc.info['name'].lower():
                        proc.kill()
                        logger.info(f"Killed NstChrome process with PID {proc.info['pid']} using psutil")
                        success = True
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
                    logger.warning(f"Error accessing process: {e}")
        except ImportError:
            logger.warning("psutil not available, skipping this method")
        except Exception as e:
            logger.warning(f"Error killing NstChrome processes using psutil: {e}")

        if success:
            logger.info("Successfully killed NstChrome processes")
        else:
            logger.warning("Failed to kill NstChrome processes with all methods")

        return success

    def kill_chrome_processes(self):
        """
        Kill all Chrome processes.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            import subprocess
            if os.name == 'nt':  # Windows
                subprocess.run("taskkill /F /IM chrome.exe", shell=True,
                               stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                subprocess.run("taskkill /F /IM chromedriver.exe", shell=True,
                               stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

                # Also kill NstChrome processes
                self.kill_nstchrome_processes()

                logger.info("Killed all Chrome processes")
                return True
            else:
                # Not implemented for non-Windows platforms
                logger.warning("kill_chrome_processes not implemented for non-Windows platforms")
                return False
        except Exception as e:
            logger.error(f"Failed to kill Chrome processes: {e}")
            return False
