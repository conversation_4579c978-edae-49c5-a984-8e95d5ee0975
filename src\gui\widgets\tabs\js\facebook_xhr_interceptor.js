/**
 * Facebook XHR Interceptor
 * 
 * This script extracts Facebook posts and comments by intercepting XHR requests.
 * It captures GraphQL responses to extract detailed post and comment data.
 */

function initFacebookXHRInterceptor() {
    'use strict';
    
    // Store extracted content
    const allContent = [];
    const seenPostIds = new Set();
    const seenCommentIds = new Set();
    
    // Configuration
    const config = {
        scrollDelay: 1000,
        scrollStep: 800,
        maxScrolls: 50,
        waitAfterClick: 1000
    };
    
    // Helper function to extract email from text
    function getEmailFromText(text) {
        if (!text) return '';
        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
        const email = text.match(emailRegex)?.[0];
        return email || '';
    }
    
    // Format top level comments
    function formatTopLevelComments(postId, topLevelComments = []) {
        if (!topLevelComments || !Array.isArray(topLevelComments)) return [];
        
        return topLevelComments.map((c) => {
            const text = c?.comment?.body?.text;
            const commentId = c?.comment?.id;
            const authorName = c?.comment?.author?.name;
            const authorId = c?.comment?.author?.id;
            const authorUrl = c?.comment?.author?.url;
            
            return {
                id: commentId,
                commentId,
                postId,
                commentText: text || '',
                commentAuthorName: authorName || '',
                commentAuthorId: authorId || '',
                commentAuthorUrl: authorUrl || '',
                email: getEmailFromText(text),
                firstName: authorName?.split(' ')?.[0] || '',
                lastName: authorName?.split(' ')?.[1] || ''
            };
        });
    }
    
    // Parse first level JSON
    function parseFirstLevelJson(json) {
        try {
            const actor = json?.data?.node?.group_feed?.edges?.[0]?.node?.comet_sections?.content
                ?.story?.comet_sections?.context_layout?.story?.comet_sections
                ?.actor_photo?.story?.actors?.[0];
            
            const postText = json?.data?.node?.group_feed?.edges?.[0]?.node?.comet_sections?.content
                ?.story?.comet_sections?.message_container?.story?.message?.text;
                
            const postId = json?.data?.node?.group_feed?.edges?.[0]?.node?.comet_sections?.feedback
                ?.story?.post_id;
            
            // If no post ID, return empty result
            if (!postId) return { post: null, topLevelComments: [] };
            
            const post = {
                id: postId,
                postId,
                postText: postText || '',
                postAuthor: actor?.name || '',
                postAuthorId: actor?.id || '',
                postAuthorUrl: actor?.url || '',
                email: getEmailFromText(postText),
                firstName: actor?.name?.split(' ')?.[0] || '',
                lastName: actor?.name?.split(' ')?.[1] || ''
            };
            
            const topLevelComments = formatTopLevelComments(
                postId,
                json?.data?.node?.group_feed?.edges?.[0]?.node?.comet_sections?.feedback
                    ?.story?.feedback_context?.interesting_top_level_comments
            );
            
            return {
                post,
                topLevelComments
            };
        } catch (e) {
            console.error('Error parsing first level JSON:', e);
            return { post: null, topLevelComments: [] };
        }
    }
    
    // Parse second level JSON
    function parseSecondLevelJson(json) {
        try {
            const actor = json?.data?.node?.comet_sections?.content?.story?.comet_sections
                ?.context_layout?.story?.comet_sections?.actor_photo?.story?.actors?.[0];
            
            const posterName = actor?.name;
            const postText = json?.data?.node?.comet_sections?.content?.story?.comet_sections
                ?.message_container?.story?.message?.text;
            const id = actor?.id;
            const postId = json?.data?.node?.comet_sections?.feedback?.story?.post_id;
            const url = actor?.url;
            
            // If no post ID, return empty result
            if (!postId) return { post: null, topLevelComments: [] };
            
            const post = {
                id: postId,
                postId,
                postText: postText || '',
                postAuthor: posterName || '',
                postAuthorId: id || '',
                postAuthorUrl: url || '',
                email: getEmailFromText(postText),
                firstName: posterName?.split(' ')?.[0] || '',
                lastName: posterName?.split(' ')?.[1] || ''
            };
            
            const topLevelComments = formatTopLevelComments(
                postId,
                json?.data?.node?.comet_sections?.feedback?.story?.feedback_context
                    ?.interesting_top_level_comments
            );
            
            return {
                post,
                topLevelComments
            };
        } catch (e) {
            console.error('Error parsing second level JSON:', e);
            return { post: null, topLevelComments: [] };
        }
    }
    
    // Parse third level JSON
    function parseThirdLevelJson(json) {
        try {
            const actor = json?.data?.node?.comet_sections?.content?.story?.comet_sections
                ?.context_layout?.story?.comet_sections?.actor_photo?.story?.actors?.[0];
                
            const posterName = actor?.name;
            const postText = json?.data?.node?.comet_sections?.content?.story?.comet_sections
                ?.message_container?.story?.message?.text;
            const posterId = actor?.id;
            const postId = json?.data?.node?.comet_sections?.feedback?.story?.post_id;
            const url = actor?.url;
            
            // If no post ID, return empty result
            if (!postId) return { post: null, topLevelComments: [] };
            
            const post = {
                id: postId,
                postId,
                postText: postText || '',
                postAuthor: posterName || '',
                postAuthorId: posterId || '',
                postAuthorUrl: url || '',
                email: getEmailFromText(postText),
                firstName: posterName?.split(' ')?.[0] || '',
                lastName: posterName?.split(' ')?.[1] || ''
            };
            
            const topLevelComments = formatTopLevelComments(
                postId,
                json?.data?.node?.comet_sections?.feedback?.story?.feedback_context
                    ?.interesting_top_level_comments
            );
            
            return {
                post,
                topLevelComments
            };
        } catch (e) {
            console.error('Error parsing third level JSON:', e);
            return { post: null, topLevelComments: [] };
        }
    }
    
    // Add post to allContent
    function addPostToAllContent(post) {
        if (!post || !post.postId || seenPostIds.has(post.postId)) return false;
        
        seenPostIds.add(post.postId);
        allContent.push(post);
        console.log(`Added post: ${post.postId} by ${post.postAuthor}`);
        return true;
    }
    
    // Add comments to allContent
    function addCommentsToAllContent(comments = []) {
        if (!comments || !Array.isArray(comments)) return 0;
        
        let addedCount = 0;
        comments.forEach((comment) => {
            if (!comment || !comment.commentId || seenCommentIds.has(comment.commentId)) return;
            
            seenCommentIds.add(comment.commentId);
            allContent.push(comment);
            addedCount++;
        });
        
        if (addedCount > 0) {
            console.log(`Added ${addedCount} comments`);
        }
        
        return addedCount;
    }
    
    // Function to intercept XHR requests
    function interceptXHRRequests() {
        console.log('Setting up XHR interception...');
        
        // Save the original open method
        const originalOpen = XMLHttpRequest.prototype.open;
        
        // Override the open method
        XMLHttpRequest.prototype.open = function(method, url, async) {
            // Only intercept GraphQL requests
            if (url.includes('graphql')) {
                // Save the original send method
                const originalSend = this.send;
                
                // Override the send method to capture request body
                this.send = function(body) {
                    // Store the request body
                    const requestBody = body;
                    
                    // Add load event listener to capture response
                    this.addEventListener('load', function() {
                        try {
                            // Process the response if it's a valid JSON
                            if (this.responseText && this.responseText.trim()) {
                                // Check if this is a posts query
                                if (requestBody && requestBody.includes('GroupsCometFeedRegularStoriesPaginationQuery')) {
                                    console.log('Intercepted posts query response');
                                    
                                    // Split the response by newlines (Facebook returns multiple JSON objects)
                                    const payload = this.responseText;
                                    const lines = payload.split('\\n');
                                    
                                    try {
                                        // Parse first post
                                        const data1 = JSON.parse(lines[0]);
                                        const firstPost = parseFirstLevelJson(data1);
                                        console.log('Parsed first post:', firstPost.post?.postId);
                                        
                                        // Add first post and its comments
                                        if (firstPost.post) {
                                            addPostToAllContent(firstPost.post);
                                            addCommentsToAllContent(firstPost.topLevelComments);
                                        }
                                        
                                        // Parse second post if available
                                        if (lines.length > 1) {
                                            const data2 = JSON.parse(lines[1]);
                                            const secondPost = parseSecondLevelJson(data2);
                                            console.log('Parsed second post:', secondPost.post?.postId);
                                            
                                            // Add second post and its comments
                                            if (secondPost.post) {
                                                addPostToAllContent(secondPost.post);
                                                addCommentsToAllContent(secondPost.topLevelComments);
                                            }
                                        }
                                        
                                        // Parse third post if available
                                        if (lines.length > 2) {
                                            const data3 = JSON.parse(lines[2]);
                                            const thirdPost = parseThirdLevelJson(data3);
                                            console.log('Parsed third post:', thirdPost.post?.postId);
                                            
                                            // Add third post and its comments
                                            if (thirdPost.post) {
                                                addPostToAllContent(thirdPost.post);
                                                addCommentsToAllContent(thirdPost.topLevelComments);
                                            }
                                        }
                                    } catch (e) {
                                        console.error('Error processing posts response:', e);
                                    }
                                }
                                // Check if this is a comments query
                                else if (requestBody && requestBody.includes('CometFocusedStoryViewUFIQuery')) {
                                    console.log('Intercepted comments query response');
                                    
                                    try {
                                        // Parse the response
                                        const data = JSON.parse(this.responseText);
                                        const postId = data?.data?.story_card?.post_id;
                                        
                                        // Extract comments
                                        const comments = data?.data?.feedback?.ufi_renderer?.feedback?.comment_list_renderer?.feedback?.comment_rendering_instance_for_feed_location?.comments?.edges?.map(
                                            (edge) => {
                                                const comment = edge?.node;
                                                if (!comment) return null;
                                                
                                                const commentId = comment.id;
                                                const commentText = comment?.body?.text;
                                                const authorName = comment?.author?.name;
                                                const authorId = comment?.author?.id;
                                                const authorUrl = comment?.author?.url;
                                                
                                                // Extract timestamp
                                                const timeStuff = comment?.comment_action_links?.find(
                                                    (link) => link?.__typename === 'XFBCommentTimeStampActionLink'
                                                )?.comment;
                                                
                                                const timestamp = timeStuff?.created_time;
                                                const commentUrl = timeStuff?.url;
                                                const email = getEmailFromText(commentText);
                                                
                                                return {
                                                    id: commentId,
                                                    commentId,
                                                    postId,
                                                    commentText: commentText || '',
                                                    commentAuthorName: authorName || '',
                                                    commentAuthorId: authorId || '',
                                                    commentAuthorUrl: authorUrl || '',
                                                    timestamp: timestamp || '',
                                                    commentUrl: commentUrl || '',
                                                    email: email || '',
                                                    firstName: authorName?.split(' ')?.[0] || '',
                                                    lastName: authorName?.split(' ')?.[1] || ''
                                                };
                                            }
                                        ).filter(comment => comment !== null);
                                        
                                        // Add comments to allContent
                                        if (comments && Array.isArray(comments)) {
                                            addCommentsToAllContent(comments);
                                        }
                                    } catch (e) {
                                        console.error('Error processing comments response:', e);
                                    }
                                }
                            }
                        } catch (error) {
                            console.error('Error processing XHR response:', error);
                        }
                    });
                    
                    // Call the original send method
                    return originalSend.apply(this, arguments);
                };
            }
            
            // Call the original open method
            return originalOpen.apply(this, arguments);
        };
        
        console.log('XHR interception set up successfully');
    }
    
    // Function to get all posts
    function getAllPosts() {
        const posts = document.querySelectorAll('div[role=feed] > div');
        return [...posts].filter((post) => {
            const posterName = post?.querySelector('h2')?.textContent;
            return !!posterName;
        });
    }
    
    // Function to click on comments
    function clickOnComments(post) {
        // Get all divs in the post
        const allDivs = post.getElementsByTagName('div');
        
        // Loop through each div
        for (let i = 0; i < allDivs.length; i++) {
            // Check if the div has the attribute data-visualcompletion set to "ignore-dynamic"
            if (allDivs[i].getAttribute('data-visualcompletion') === 'ignore-dynamic') {
                // Try to find the comments button
                const commentsButton = allDivs[i]?.children?.[0]?.children?.[0]?.children?.[0]?.children?.[0]
                    ?.children?.[0]?.children?.[1]?.children?.[1]?.children?.[0]?.children?.[0];
                
                if (commentsButton) {
                    commentsButton.click();
                    console.log('Clicked comments button');
                    return true;
                }
            }
        }
        
        // Try alternative method to find comments button
        const commentButtons = post.querySelectorAll('div[role="button"]');
        for (const button of commentButtons) {
            const text = button.textContent.toLowerCase();
            if (text.includes('comment') || text.includes('reply')) {
                button.click();
                console.log('Clicked alternative comments button');
                return true;
            }
        }
        
        return false;
    }
    
    // Function to close dialog
    function closeDialog() {
        const closeButton = document?.querySelector('div[aria-label="Close"]');
        if (closeButton) {
            closeButton.click();
            console.log('Closed dialog');
            return true;
        }
        return false;
    }
    
    // Function to scroll down
    async function scrollDown() {
        return new Promise((resolve) => {
            window.scrollBy(0, config.scrollStep);
            setTimeout(resolve, config.scrollDelay);
        });
    }
    
    // Main function to scrape posts and comments
    async function scrapePostsAndComments(maxScrolls = config.maxScrolls) {
        console.log(`Starting to scrape posts and comments with up to ${maxScrolls} scrolls...`);
        
        // Set up XHR interception
        interceptXHRRequests();
        
        // Initial extraction of visible posts
        console.log('Processing initially visible posts...');
        let posts = getAllPosts();
        console.log(`Found ${posts.length} visible posts`);
        
        let i = 0;
        let scrollsLeft = maxScrolls;
        
        // Process each post
        while (i < posts.length) {
            const post = posts[i];
            
            // Click on comments to load them
            clickOnComments(post);
            
            // Wait for comments to load
            await new Promise(resolve => setTimeout(resolve, config.waitAfterClick));
            
            // Close any dialogs
            closeDialog();
            
            // Move to next post
            i++;
            
            // Scroll down if needed
            if (scrollsLeft > 0 && i >= posts.length - 3) { // If we're near the end of visible posts
                console.log(`Scrolling down (${maxScrolls - scrollsLeft + 1}/${maxScrolls})`);
                await scrollDown();
                scrollsLeft--;
                
                // Update posts list
                const currentPosts = getAllPosts();
                console.log(`Now found ${currentPosts.length} posts (was ${posts.length})`);
                posts = currentPosts;
            }
        }
        
        console.log(`Scraping completed. Extracted ${allContent.length} items (${seenPostIds.size} posts, ${seenCommentIds.size} comments).`);
        return {
            allContent,
            postCount: seenPostIds.size,
            commentCount: seenCommentIds.size
        };
    }
    
    // Return the public API
    return {
        scrapePostsAndComments,
        getAllContent: () => allContent,
        getPostCount: () => seenPostIds.size,
        getCommentCount: () => seenCommentIds.size,
        config
    };
}

// Export the function for use in Selenium
if (typeof module !== 'undefined') {
    module.exports = initFacebookXHRInterceptor;
}
