/**
 * Advanced Facebook Post Links Extractor
 */
const FacebookPostLinkExtractor = (function() {
    // Configuration
    const config = {
        minDelay: 1000,       // Minimum delay between scrolls (1 second)
        maxDelay: 3000,       // Maximum delay between scrolls (3 seconds)
        maxAttempts: 150,     // Increased maximum attempts for more posts
        scrollStep: 800,      // Reduced scroll step for more natural scrolling
        maxPosts: 5000,       // Maximum number of posts to extract
        skipDuplicates: true, // Skip duplicate posts
        maxEmptyScrolls: 5,   // Maximum number of scrolls with no new posts before giving up
        aggressiveMode: true, // Enable aggressive extraction mode
        retryOnFailure: true, // Retry extraction if it fails
        humanLikeScrolling: true, // Enable human-like scrolling behavior
        randomDelayEnabled: true, // Enable random delay between 1-3 seconds
        logCounterEnabled: true   // Enable continuous counter in log
    };

    const state = {
        collectedPosts: [],
        processedElements: new WeakSet(),
        attempts: 0,
        seenUrls: new Set(),       // Track seen URLs to avoid duplicates
        lastScrollPosition: 0,     // Track last scroll position
        noNewPostsCount: 0,        // Count consecutive scrolls with no new posts
        startTime: Date.now()      // Track start time for performance metrics
    };

    // Helper functions
    const helpers = {
        delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

        randomDelay: function() {
            // Implementar un retraso más natural con variaciones
            let delay;

            if (config.humanLikeScrolling) {
                // Usar una distribución más natural (no uniforme)
                // Esto favorece valores entre 1-2s con ocasionales valores más altos
                const baseDelay = config.minDelay;
                const extraDelay = Math.pow(Math.random(), 1.5) * (config.maxDelay - config.minDelay);

                // Añadir milisegundos aleatorios para que parezca más humano
                const randomMs = Math.floor(Math.random() * 100) / 100;

                delay = baseDelay + extraDelay + randomMs;

                // Ocasionalmente añadir una pausa más larga (1 de cada 10 veces)
                if (Math.random() < 0.1) {
                    delay += 500 + Math.random() * 1000;
                    console.log(`Añadiendo pausa extra para comportamiento más natural`);
                }
            } else {
                // Comportamiento original
                delay = config.minDelay + Math.random() * (config.maxDelay - config.minDelay);
            }

            console.log(`Esperando ${(delay/1000).toFixed(2)} segundos para evitar restricciones`);
            return this.delay(delay);
        },

        cleanText: (text) => text ? text.replace(/\s+/g, ' ').trim() : '',

        smartScroll: async function() {
            const currentPos = window.scrollY;
            const documentHeight = document.documentElement.scrollHeight;
            const remaining = documentHeight - window.innerHeight - currentPos;
            const viewportHeight = window.innerHeight;

            // Check if we're stuck at the same position
            if (Math.abs(currentPos - state.lastScrollPosition) < 10) { // Tolerancia de 10px
                state.noNewPostsCount++;
                console.log(`Atascado en la misma posición durante ${state.noNewPostsCount} intentos`);

                // Si estamos atascados demasiado tiempo, intentar un desplazamiento mayor o aleatorio
                if (state.noNewPostsCount > 3) {
                    // Intentar hacer clic en botones "Ver más" para cargar más contenido
                    this.clickSeeMoreButtons();

                    // Siempre desplazarse hacia adelante, nunca hacia atrás
                    // Usar una distribución más natural para el desplazamiento aleatorio
                    const randomFactor = 0.3 + (Math.random() * 0.4); // Entre 0.3 y 0.7
                    // Asegurarse de que el desplazamiento siempre sea hacia adelante
                    const randomScroll = Math.floor(currentPos + (randomFactor * viewportHeight));
                    console.log(`Intentando desplazamiento aleatorio a la posición ${randomScroll}`);

                    // Usar un desplazamiento más natural
                    if (config.humanLikeScrolling) {
                        // Simular desplazamiento humano con pequeños pasos
                        const steps = 5 + Math.floor(Math.random() * 5); // 5-10 pasos
                        const distance = randomScroll - currentPos;
                        const stepSize = distance / steps;

                        for (let i = 1; i <= steps; i++) {
                            const nextPos = currentPos + (stepSize * i);
                            window.scrollTo({
                                top: nextPos,
                                behavior: 'smooth'
                            });
                            // Pequeña pausa entre pasos
                            await this.delay(50 + Math.random() * 100);
                        }
                    } else {
                        window.scrollTo({
                            top: randomScroll,
                            behavior: 'smooth'
                        });
                    }

                    state.noNewPostsCount = 0;
                    await this.delay(1000 + Math.random() * 500);
                    return;
                }
            } else {
                // Reiniciar contador si nos movimos
                state.noNewPostsCount = 0;
            }

            // Guardar posición actual para la próxima comparación
            state.lastScrollPosition = currentPos;

            if (remaining > 0) {
                // Calcular un paso de desplazamiento dinámico basado en cuántas publicaciones hemos recopilado
                // Usar un paso más pequeño para un comportamiento más natural
                let dynamicStep;

                if (config.humanLikeScrolling) {
                    // Paso base más pequeño para comportamiento humano
                    const baseStep = config.scrollStep * 0.7;
                    // Variación aleatoria para que no sea siempre igual
                    const randomVariation = (Math.random() * 0.3 + 0.85) * baseStep;
                    // Ajustar según cuántas publicaciones hemos recopilado (aumentar gradualmente)
                    const postFactor = 1 + (Math.floor(state.collectedPosts.length / 100) * 0.1);

                    dynamicStep = Math.min(
                        remaining,
                        randomVariation * postFactor
                    );
                } else {
                    // Comportamiento original
                    dynamicStep = Math.min(
                        remaining,
                        config.scrollStep * (1 + Math.floor(state.collectedPosts.length / 50))
                    );
                }

                const scrollTo = currentPos + dynamicStep;
                console.log(`Desplazándose de ${currentPos} a ${scrollTo} (paso: ${dynamicStep.toFixed(1)})`);

                if (config.humanLikeScrolling && dynamicStep > 300) {
                    // Simular desplazamiento humano con pequeños pasos
                    const steps = 3 + Math.floor(Math.random() * 3); // 3-5 pasos
                    const stepSize = dynamicStep / steps;

                    for (let i = 1; i <= steps; i++) {
                        const nextPos = currentPos + (stepSize * i);
                        window.scrollTo({
                            top: nextPos,
                            behavior: i === steps ? 'auto' : 'smooth' // Último paso más rápido
                        });
                        // Pequeña pausa entre pasos
                        await this.delay(30 + Math.random() * 70);
                    }
                } else {
                    window.scrollTo({
                        top: scrollTo,
                        behavior: 'smooth'
                    });
                }

                // Retraso más corto para desplazamiento más rápido, pero con variación natural
                const scrollDelay = 1000 + (Math.random() * 1000);
                await this.delay(scrollDelay);
            } else {
                // Si estamos en la parte inferior, esperar un poco más para que se cargue el contenido
                console.log('Se alcanzó la parte inferior de la página, esperando a que se cargue más contenido...');
                await this.delay(1500 + Math.random() * 1000);
            }
        },

        // Click "See More" buttons to expand content
        clickSeeMoreButtons: function() {
            const seeMoreButtons = [
                'div[role="button"]:not([aria-hidden="true"]):not([aria-disabled="true"])',
                'span[role="button"]',
                'a[role="button"]'
            ];

            let clicked = 0;

            for (const selector of seeMoreButtons) {
                const buttons = document.querySelectorAll(selector);
                for (const button of buttons) {
                    const text = button.textContent.toLowerCase();
                    if (text.includes('see more') || text.includes('show more')) {
                        try {
                            button.click();
                            clicked++;
                            console.log('Clicked "See More" button');
                        } catch (e) {
                            console.error('Error clicking button:', e);
                        }
                    }
                }
            }

            if (clicked > 0) {
                console.log(`Clicked ${clicked} "See More" buttons`);
            }

            return clicked;
        }
    };

    // Data extraction functions
    const extractors = {
        findPostElements: function() {
            // Enhanced selectors for better post detection
            const selectors = [
                '[role="article"]', // Primary elements (new Facebook)
                'div[data-pagelet^="Feed"] > div > div', // Feed items
                'div[class*="userContentWrapper"]', // Old Facebook
                'div[data-testid="post_container"]', // Another post container
                'div[data-ad-preview="message"]', // Ad-like posts
                'div.x1yztbdb:not([aria-hidden="true"])', // Modern feed items
                'div[data-pagelet*="FeedUnit"]', // Another feed unit type
                'div.x1iorvi4', // Common Facebook post container
                'div.x78zum5', // Another common container
                'div.x1n2onr6', // Another common container
                'div.xdj266r', // Content container
                'div.x1lliihq', // Another content container
                'div.x1i10hfl', // Clickable container
                'div.x1qjc9v5', // Common post class
                'div.x9f619', // Common post class
                'div.x1ja2u2z', // Common post class
                'div.x1y1aw1k', // Common post class
                'div.x1sxyh0', // Common post class
                'div.xurb0ha', // Common post class
                'div.x1roi4f4', // Common post class
                'div.x1iyjqo2', // Common post class
                'div.x193iq5w' // Common post class
            ];

            let results = [];
            let foundElements = false;

            // Try each selector until we find elements
            for (const selector of selectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        console.log(`Found ${elements.length} elements with selector: ${selector}`);
                        const newElements = Array.from(elements).filter(el => !state.processedElements.has(el));
                        results = results.concat(newElements);
                        foundElements = true;
                    }
                } catch(e) {
                    console.error(`Error with selector ${selector}:`, e);
                }
            }

            // If we didn't find any elements with the primary selectors or if aggressive mode is enabled
            if (!foundElements || config.aggressiveMode) {
                console.log('Using aggressive approach to find more posts');
                try {
                    // Look for elements that might contain posts based on their content
                    const allDivs = document.querySelectorAll('div');
                    let aggressiveResults = [];

                    for (const div of allDivs) {
                        // Skip already processed elements and small elements
                        if (state.processedElements.has(div)) continue;

                        // Skip elements that are too small to be posts
                        const rect = div.getBoundingClientRect();
                        if (rect.width < 300 || rect.height < 100) continue;

                        // Skip elements that are already in results
                        if (results.some(el => el.contains(div) || div.contains(el))) continue;

                        // Check if this div contains elements that suggest it's a post
                        const hasTimeElement = div.querySelector('abbr[data-utime]') !== null;
                        const hasPostLink = div.querySelector('a[href*="/posts/"]') !== null ||
                                           div.querySelector('a[href*="/permalink/"]') !== null ||
                                           div.querySelector('a[href*="/story.php"]') !== null;
                        const hasAuthor = div.querySelector('a[href*="/user/"]') !== null ||
                                         div.querySelector('a[href*="/profile/"]') !== null ||
                                         div.querySelector('a[role="link"][tabindex="0"]') !== null;
                        const hasImage = div.querySelector('img[src*="scontent"]') !== null;
                        const hasReactions = div.querySelector('div[aria-label*="reaction"]') !== null ||
                                            div.querySelector('span[aria-label*="reaction"]') !== null;
                        const hasComments = div.querySelector('div[aria-label*="comment"]') !== null ||
                                           div.querySelector('span[aria-label*="comment"]') !== null;

                        // If it has at least two of these characteristics, consider it a post
                        let score = 0;
                        if (hasTimeElement) score += 2; // Time element is a strong indicator
                        if (hasPostLink) score += 2;   // Post link is a strong indicator
                        if (hasAuthor) score += 1;
                        if (hasImage) score += 1;
                        if (hasReactions) score += 1;
                        if (hasComments) score += 1;

                        if (score >= 2) {
                            aggressiveResults.push(div);
                        }
                    }

                    // Filter out nested elements (keep only the outermost containers)
                    const filteredResults = [];
                    for (const el of aggressiveResults) {
                        let isNested = false;
                        for (const otherEl of aggressiveResults) {
                            if (el !== otherEl && otherEl.contains(el)) {
                                isNested = true;
                                break;
                            }
                        }
                        if (!isNested) {
                            filteredResults.push(el);
                        }
                    }

                    console.log(`Found ${filteredResults.length} additional elements with aggressive approach`);
                    results = results.concat(filteredResults);
                } catch(e) {
                    console.error('Error with aggressive approach:', e);
                }
            }

            // Remove duplicates (elements that contain other elements in our results)
            const uniqueResults = [];
            for (const el of results) {
                let isContainer = false;
                for (const otherEl of results) {
                    if (el !== otherEl && el.contains(otherEl)) {
                        isContainer = true;
                        break;
                    }
                }
                if (!isContainer) {
                    uniqueResults.push(el);
                }
            }

            console.log(`Found ${uniqueResults.length} unique post elements`);
            return uniqueResults;
        },

        extractPostLink: function(element) {
            // First attempt: Look in time/date element
            const timeSelectors = [
                'a[href*="/posts/"][role="link"]', // New structure
                'a[href*="/permalink/"]', // Alternative structure
                'a[aria-label*="post"]', // Alternative description
                'abbr + a', // Element next to time marker
                'span[data-utime] a', // Element with time data
                'a[href*="/groups/"][href*="/posts/"]', // Group post
                'a[href*="/groups/"][href*="/permalink/"]', // Group permalink
                'a[href*="fbid="]', // Post with fbid parameter
                'a[href*="story_fbid="]' // Post with story_fbid parameter
            ];

            for (const selector of timeSelectors) {
                try {
                    const linkElement = element.querySelector(selector);
                    if (linkElement) {
                        const href = linkElement.getAttribute('href');
                        if (href) {
                            // Clean up the URL by removing tracking parameters
                            let cleanUrl = href;
                            try {
                                const url = new URL(href.includes('facebook.com') ? href : `https://facebook.com${href}`);
                                // Remove tracking parameters
                                url.searchParams.delete('__cft__[0]');
                                url.searchParams.delete('__tn__');
                                url.searchParams.delete('comment_id'); // Keep only the post URL, not comment URL
                                cleanUrl = url.toString();
                            } catch (e) {
                                console.error('Error cleaning URL:', e);
                                cleanUrl = href.includes('facebook.com') ? href : `https://facebook.com${href}`;
                            }
                            return cleanUrl;
                        }
                    }
                } catch(e) { console.error(e); }
            }

            // Second attempt: Search anywhere in post
            const backupSelectors = [
                'a[href*="/posts/"]',
                'a[href*="/permalink/"]',
                'a[href*="/story.php"]',
                'a[href*="/photo.php"]',
                'a[href*="/video.php"]',
                'a[href*="/reel/"]',
                'a[href*="/watch/"]'
            ];

            for (const selector of backupSelectors) {
                try {
                    const links = element.querySelectorAll(selector);
                    for (const link of links) {
                        const href = link.getAttribute('href');
                        if (href && (href.match(/\/posts\/|\/permalink\/|\/story\.php|\/photo\.php|\/video\.php|\/reel\/|\/watch\//))) {
                            // Clean up the URL
                            let cleanUrl = href;
                            try {
                                const url = new URL(href.includes('facebook.com') ? href : `https://facebook.com${href}`);
                                // Remove tracking parameters
                                url.searchParams.delete('__cft__[0]');
                                url.searchParams.delete('__tn__');
                                url.searchParams.delete('comment_id');
                                cleanUrl = url.toString();
                            } catch (e) {
                                console.error('Error cleaning URL:', e);
                                cleanUrl = href.includes('facebook.com') ? href : `https://facebook.com${href}`;
                            }
                            return cleanUrl;
                        }
                    }
                } catch(e) { console.error(e); }
            }

            // Third attempt: Look for any link that might be a post
            try {
                const allLinks = element.querySelectorAll('a[href]');
                for (const link of allLinks) {
                    const href = link.getAttribute('href');
                    if (href && (
                        href.includes('/groups/') &&
                        (href.includes('/posts/') || href.includes('/permalink/') || href.includes('fbid='))
                    )) {
                        // Clean up the URL
                        let cleanUrl = href;
                        try {
                            const url = new URL(href.includes('facebook.com') ? href : `https://facebook.com${href}`);
                            // Remove tracking parameters
                            url.searchParams.delete('__cft__[0]');
                            url.searchParams.delete('__tn__');
                            url.searchParams.delete('comment_id');
                            cleanUrl = url.toString();
                        } catch (e) {
                            console.error('Error cleaning URL:', e);
                            cleanUrl = href.includes('facebook.com') ? href : `https://facebook.com${href}`;
                        }
                        return cleanUrl;
                    }
                }
            } catch(e) { console.error(e); }

            return null;
        },

        extractFullPostData: async function(element) {
            const postLink = this.extractPostLink(element);
            if (!postLink) return null;

            const postId = postLink.split('/posts/')[1]?.split('/')[0] ||
                          postLink.split('/permalink/')[1]?.split('/')[0] ||
                          Math.random().toString(36).substring(2, 15);

            // Extract basic data
            const authorElement = element.querySelector('[data-testid="story_actor_line"] a') ||
                                element.querySelector('a[href*="/user/"], a[href*="/profile/"]');

            const contentElement = element.querySelector('[data-testid="post_message"]') ||
                                 element.querySelector('div[dir="auto"]');

            const timeElement = element.querySelector('abbr[data-utime]') ||
                              element.querySelector('a[aria-label*="post"]');

            // Extract timestamp with better handling
            let timestamp = '';
            if (timeElement) {
                // First try to get data-utime (Unix timestamp)
                const utime = timeElement.getAttribute('data-utime');
                if (utime) {
                    // Convert Unix timestamp to ISO date
                    try {
                        const date = new Date(parseInt(utime) * 1000); // Convert seconds to milliseconds
                        if (!isNaN(date.getTime())) {
                            // Ensure the date is not in the future
                            const now = new Date();
                            if (date > now) {
                                console.log('Timestamp is in the future, using current time instead');
                                timestamp = now.toISOString();
                            } else {
                                timestamp = date.toISOString();
                            }
                        } else {
                            timestamp = utime; // Fallback to raw value
                        }
                    } catch (e) {
                        console.error('Error parsing timestamp:', e);
                        timestamp = utime; // Fallback to raw value
                    }
                }

                // If not available, try aria-label (human-readable date)
                if (!timestamp) {
                    const ariaLabel = timeElement.getAttribute('aria-label');
                    if (ariaLabel) {
                        // Try to parse human-readable date
                        timestamp = ariaLabel;

                        // For common patterns, convert to approximate timestamp
                        const now = new Date();

                        if (ariaLabel.toLowerCase().includes('yesterday')) {
                            const yesterday = new Date(now);
                            yesterday.setDate(yesterday.getDate() - 1);
                            timestamp = yesterday.toISOString();
                        } else if (ariaLabel.toLowerCase().includes('hour')) {
                            const hourMatch = ariaLabel.match(/(\d+)\s*hour/i);
                            if (hourMatch) {
                                const hoursAgo = parseInt(hourMatch[1]);
                                const pastTime = new Date(now);
                                pastTime.setHours(now.getHours() - hoursAgo);
                                timestamp = pastTime.toISOString();
                            }
                        } else if (ariaLabel.toLowerCase().includes('minute')) {
                            const minMatch = ariaLabel.match(/(\d+)\s*minute/i);
                            if (minMatch) {
                                const minsAgo = parseInt(minMatch[1]);
                                const pastTime = new Date(now);
                                pastTime.setMinutes(now.getMinutes() - minsAgo);
                                timestamp = pastTime.toISOString();
                            }
                        } else if (ariaLabel.toLowerCase().includes('just now')) {
                            timestamp = now.toISOString();
                        }
                    }
                }

                // If still not available, try innerText
                if (!timestamp) {
                    const innerText = timeElement.innerText;
                    if (innerText) {
                        timestamp = innerText;

                        // For common patterns, convert to approximate timestamp
                        const now = new Date();

                        if (innerText.toLowerCase().includes('yesterday')) {
                            const yesterday = new Date(now);
                            yesterday.setDate(yesterday.getDate() - 1);
                            timestamp = yesterday.toISOString();
                        } else if (innerText.toLowerCase().includes('hr') || innerText.toLowerCase().includes('hour')) {
                            const hourMatch = innerText.match(/(\d+)\s*h/i);
                            if (hourMatch) {
                                const hoursAgo = parseInt(hourMatch[1]);
                                const pastTime = new Date(now);
                                pastTime.setHours(now.getHours() - hoursAgo);
                                timestamp = pastTime.toISOString();
                            }
                        } else if (innerText.toLowerCase().includes('min')) {
                            const minMatch = innerText.match(/(\d+)\s*m/i);
                            if (minMatch) {
                                const minsAgo = parseInt(minMatch[1]);
                                const pastTime = new Date(now);
                                pastTime.setMinutes(now.getMinutes() - minsAgo);
                                timestamp = pastTime.toISOString();
                            }
                        } else if (innerText.toLowerCase().includes('just now')) {
                            timestamp = now.toISOString();
                        }
                    }
                }
            }

            // If we still don't have a timestamp, use current time
            if (!timestamp) {
                timestamp = new Date().toISOString();
            }

            // Extract author ID and name with improved handling
            let authorId = '';
            let authorName = '';

            if (authorElement) {
                // Extract author name
                authorName = authorElement.textContent?.trim() || 'Unknown';

                // Extract author ID from profile URL
                if (authorElement.href) {
                    const profileUrl = authorElement.href;

                    // Try to extract user ID from URL patterns
                    const userIdMatch = profileUrl.match(/\/user\/([^\/\?]+)/) ||
                                       profileUrl.match(/\/profile\.php\?id=([^&]+)/) ||
                                       profileUrl.match(/facebook\.com\/([^\/\?]+)/);

                    if (userIdMatch) {
                        authorId = userIdMatch[1];

                        // Clean up common patterns that aren't actually IDs
                        if (authorId === 'profile.php' || authorId === 'people') {
                            // Try to extract from query parameters
                            const idParam = profileUrl.match(/[\?&]id=([^&]+)/);
                            if (idParam) {
                                authorId = idParam[1];
                            } else {
                                authorId = '';
                            }
                        }
                    }
                }
            }

            // If we couldn't extract an author ID but have a name, use a hash of the name
            if (!authorId && authorName && authorName !== 'Unknown') {
                // Simple string hash function
                const hashString = str => {
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // Convert to 32bit integer
                    }
                    return Math.abs(hash).toString(16); // Convert to hex string
                };

                authorId = 'name_' + hashString(authorName);
            }

            return {
                postId,
                url: postLink,
                authorId: authorId,
                author: authorName,
                authorProfile: authorElement?.href || '',
                content: contentElement?.textContent?.trim() || '',
                timestamp: timestamp,
                images: Array.from(element.querySelectorAll('img[src*="scontent-"]')).map(img => img.src),
                extractedAt: new Date().toISOString() // Add extraction timestamp for debugging
            };
        }
    };

    // Main interface
    return {
        async start(options = {}) {
            console.log('Starting enhanced post links extraction...');
            state.startTime = Date.now();

            // Apply any custom options
            if (options) {
                Object.assign(config, options);
                console.log('Applied custom options:', options);
            }

            // Reset state for a fresh start
            state.collectedPosts = [];
            state.seenUrls.clear();
            state.attempts = 0;
            state.noNewPostsCount = 0;

            // Initial page setup
            console.log('Preparing page for extraction...');
            helpers.clickSeeMoreButtons(); // Expand any collapsed content
            await helpers.delay(1000);

            // Main extraction loop
            console.log(`Starting extraction with max attempts: ${config.maxAttempts}, max posts: ${config.maxPosts}`);

            while (state.attempts < config.maxAttempts && state.collectedPosts.length < config.maxPosts) {
                // Add a small random delay to avoid detection
                await helpers.randomDelay();

                // Find new posts
                const posts = extractors.findPostElements();
                if (posts.length === 0) {
                    state.attempts++;
                    console.log(`No new posts found (Attempt ${state.attempts}/${config.maxAttempts})`);
                    await helpers.smartScroll();
                    continue;
                }

                // Reset attempt counter when we find posts
                state.attempts = 0;

                // Calculate how many more posts we can extract
                const remainingCapacity = config.maxPosts - state.collectedPosts.length;
                const postsToProcess = posts.slice(0, remainingCapacity);

                // Process each post with improved duplicate detection
                let newPostsInBatch = 0;
                for (const post of postsToProcess) {
                    try {
                        const postData = await extractors.extractFullPostData(post);
                        if (!postData) continue;

                        // Skip duplicates
                        if (config.skipDuplicates && state.seenUrls.has(postData.url)) {
                            state.processedElements.add(post); // Mark as processed anyway
                            continue;
                        }

                        // Add to collected posts
                        state.collectedPosts.push(postData);
                        state.processedElements.add(post);
                        state.seenUrls.add(postData.url);
                        newPostsInBatch++;

                        // Log progress periodically
                        if (state.collectedPosts.length % 10 === 0) {
                            const elapsedSeconds = Math.round((Date.now() - state.startTime) / 1000);
                            const postsPerSecond = (state.collectedPosts.length / elapsedSeconds).toFixed(2);
                            console.log(`Extracted ${state.collectedPosts.length} posts in ${elapsedSeconds} seconds (${postsPerSecond} posts/sec)`);
                        }
                    } catch(e) {
                        console.error('Extraction error:', e);
                    }

                    // Shorter delay between posts for faster extraction
                    await helpers.delay(300 + Math.random() * 700);

                    // Check if we've reached the maximum
                    if (state.collectedPosts.length >= config.maxPosts) {
                        console.log(`Reached maximum post count (${config.maxPosts}), stopping extraction`);
                        break;
                    }
                }

                console.log(`Extracted ${newPostsInBatch} new posts in this batch (total: ${state.collectedPosts.length})`);

                // Scroll to load more content
                await helpers.smartScroll();

                // If we didn't find any new posts, try clicking "See More" buttons
                if (newPostsInBatch === 0) {
                    helpers.clickSeeMoreButtons();
                }
            }

            // Extraction complete
            const elapsedSeconds = Math.round((Date.now() - state.startTime) / 1000);
            console.log(`Extraction complete! Extracted ${state.collectedPosts.length} posts in ${elapsedSeconds} seconds`);

            this.downloadResults();
            return state.collectedPosts;
        },

        downloadResults() {
            if (state.collectedPosts.length === 0) {
                console.log('No data to download');
                return;
            }

            const blob = new Blob([JSON.stringify(state.collectedPosts, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `facebook_posts_${new Date().toISOString()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // Don't log downloading photos
            console.log(`Processed ${state.collectedPosts.length} posts`);
        },

        // Methods for use with Selenium
        getCollectedPosts() {
            return state.collectedPosts;
        },

        getPostCount() {
            return state.collectedPosts.length;
        },

        setConfig(newConfig) {
            // Apply new configuration
            Object.assign(config, newConfig);

            // Log the updated configuration
            console.log('Updated configuration:', JSON.stringify(config, null, 2));

            // Reset state counters when configuration changes
            state.attempts = 0;
            state.noNewPostsCount = 0;

            // Return success
            return {
                success: true,
                config: {...config},
                message: 'Configuration updated successfully'
            };
        },

        async extractNextBatch() {
            // Check if we've reached the maximum number of posts
            if (state.collectedPosts.length >= config.maxPosts) {
                console.log(`Reached maximum post count (${config.maxPosts}), stopping extraction`);
                return [];
            }

            // Track the state before extraction to detect if we're making progress
            const startingPostCount = state.collectedPosts.length;

            // Try multiple strategies to find posts
            let posts = [];

            // Strategy 1: Standard post detection
            posts = extractors.findPostElements();
            console.log(`Found ${posts.length} post elements with standard detection`);

            // Strategy 2: If we found very few posts, try clicking "See More" buttons first
            if (posts.length < 5) {
                console.log("Found few posts, trying to expand content first...");
                const clickCount = helpers.clickSeeMoreButtons();
                if (clickCount > 0) {
                    // Wait for content to load after clicking buttons
                    await helpers.delay(1500);
                    // Try finding posts again
                    posts = extractors.findPostElements();
                    console.log(`After expanding content, found ${posts.length} post elements`);
                }
            }

            // Strategy 3: If still few posts, try a more aggressive scroll first
            if (posts.length < 3) {
                console.log("Still found few posts, trying aggressive scroll...");
                // Scroll more aggressively
                window.scrollBy({
                    top: window.innerHeight * 2,
                    behavior: 'smooth'
                });
                await helpers.delay(2000); // Wait longer for content to load

                // Try finding posts again
                posts = extractors.findPostElements();
                console.log(`After aggressive scroll, found ${posts.length} post elements`);
            }

            // Calculate how many more posts we can extract
            const remainingCapacity = config.maxPosts - state.collectedPosts.length;
            const postsToProcess = posts.slice(0, remainingCapacity);

            const newPosts = [];
            let duplicateCount = 0;
            let failedCount = 0;

            // Process each post with improved error handling
            for (const post of postsToProcess) {
                try {
                    const postData = await extractors.extractFullPostData(post);
                    if (!postData) {
                        failedCount++;
                        continue;
                    }

                    // Skip duplicates if configured
                    if (config.skipDuplicates && state.seenUrls.has(postData.url)) {
                        duplicateCount++;
                        state.processedElements.add(post); // Mark as processed anyway
                        continue;
                    }

                    // Add to collected posts
                    state.collectedPosts.push(postData);
                    state.processedElements.add(post);
                    state.seenUrls.add(postData.url);
                    newPosts.push(postData);

                    // Log progress periodically
                    if (state.collectedPosts.length % 5 === 0) {
                        const elapsedSeconds = Math.round((Date.now() - state.startTime) / 1000);
                        console.log(`Extracted ${state.collectedPosts.length} posts in ${elapsedSeconds} seconds`);
                    }
                } catch(e) {
                    console.error('Extraction error:', e);
                    failedCount++;
                }
            }

            // Log detailed results
            console.log(`Batch summary: ${newPosts.length} new posts, ${duplicateCount} duplicates, ${failedCount} failed extractions`);
            console.log(`Total posts collected: ${state.collectedPosts.length}`);

            // Check if we made progress
            if (newPosts.length === 0) {
                state.noNewPostsCount++;
                console.log(`No new posts found in this batch. Empty batch count: ${state.noNewPostsCount}/${config.maxEmptyScrolls}`);

                // If we've had too many empty batches, try more aggressive strategies
                if (state.noNewPostsCount >= config.maxEmptyScrolls) {
                    console.log("Reached maximum empty batches, trying recovery strategies...");

                    // Strategy 1: Click all "See More" buttons
                    const clickCount = helpers.clickSeeMoreButtons();
                    console.log(`Clicked ${clickCount} "See More" buttons`);

                    // Strategy 2: Always scroll forward, never backward
                    const currentPosition = window.scrollY;
                    const scrollAmount = Math.floor(window.innerHeight * (0.5 + Math.random() * 0.5)); // 50-100% of viewport height
                    const newPosition = currentPosition + scrollAmount;
                    console.log(`Scrolling forward by ${scrollAmount}px to position: ${newPosition}`);
                    window.scrollTo({
                        top: newPosition,
                        behavior: 'smooth'
                    });

                    // Reset the counter if we've tried recovery
                    state.noNewPostsCount = 0;
                    await helpers.delay(2000); // Wait longer after recovery attempts
                }
            } else {
                // Reset counter when we find new posts
                state.noNewPostsCount = 0;
            }

            // Scroll to load more content with a more reliable approach
            try {
                await helpers.smartScroll();
            } catch (e) {
                console.error("Error during scroll:", e);
                // Fallback to basic scroll if smart scroll fails
                window.scrollBy({
                    top: 1000,
                    behavior: 'smooth'
                });
                await helpers.delay(1000);
            }

            return newPosts;
        }
    };
})();

// Make the extractor available globally immediately
window.FacebookPostLinkExtractor = FacebookPostLinkExtractor;
console.log('Facebook Post Link Extractor initialized and available globally');

// Also provide a reference through fbPostLinkExtractor for backward compatibility
window.fbPostLinkExtractor = FacebookPostLinkExtractor;

// Export for use in Selenium
if (typeof module !== 'undefined') {
    module.exports = FacebookPostLinkExtractor;
}
