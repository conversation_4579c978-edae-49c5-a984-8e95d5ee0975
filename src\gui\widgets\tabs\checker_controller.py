import os
import sys
import json
import time
import logging
import random
import subprocess
from urllib.parse import quote, urlencode
import requests
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QThread

logger = logging.getLogger(__name__)

class CheckerWorker(QThread):
    """Worker thread for running the checker process using simulation."""

    # Define signals
    progress_updated = pyqtSignal(int, int, str)
    log_message = pyqtSignal(str)
    profile_banned = pyqtSignal(str)  # Signal to emit when a profile is banned
    finished = pyqtSignal()

    def __init__(self, profiles, settings, db):
        super().__init__()
        self.profiles = profiles
        self.settings = settings
        self.db = db  # Database instance
        self.running = False
        self.banned_profiles = []  # List to store banned profiles
        self.active_profiles = []  # List to store active profiles

    def run(self):
        """Run the checker process using simulation."""
        self.running = True
        total = len(self.profiles)
        self.banned_profiles = []  # Reset banned profiles list
        self.active_profiles = []  # Reset active profiles list

        # Get simulation settings
        ban_rate = self.settings.get("ban_rate", 20)  # Default 20%
        delay = self.settings.get("delay", 500)  # Default 500ms

        # Check each profile
        for i, profile in enumerate(self.profiles):
            if not self.running:
                break

            # Update progress
            progress = int((i + 1) / total * 100)
            self.progress_updated.emit(i + 1, total, f"Checking profile {i + 1}/{total}")
            self.log_message.emit(f"[INFO] Checking profile: {profile}")

            # Simulate checking delay
            time.sleep(delay / 1000)  # Convert ms to seconds

            # Simulate random result based on ban rate
            # Use a fixed seed based on profile ID to ensure consistent results
            random.seed(hash(profile))
            is_banned = random.random() * 100 < ban_rate
            random.seed()  # Reset the seed

            if is_banned:
                self.log_message.emit(f"[BANNED] Profile {profile} is banned")

                # Add to banned profiles list regardless of database update
                self.banned_profiles.append(profile)

                # Emit signal that profile is banned
                self.profile_banned.emit(profile)

                # Update profile status in database
                if self.db.save_profile(profile, status="banned"):
                    self.log_message.emit(f"[INFO] Profile {profile} marked as banned in database")
                else:
                    self.log_message.emit(f"[ERROR] Failed to update profile {profile} status in database")
            else:
                self.log_message.emit(f"[GOOD] Profile {profile} is active")
                self.active_profiles.append(profile)

                # Update profile status in database if auto-update is enabled
                if self.settings.get("auto_update_status", False):
                    if self.db.save_profile(profile, status="active"):
                        self.log_message.emit(f"[INFO] Profile {profile} marked as active in database")
                    else:
                        self.log_message.emit(f"[ERROR] Failed to update profile {profile} status in database")

        # Log summary
        if self.banned_profiles:
            self.log_message.emit(f"[SUMMARY] Found {len(self.banned_profiles)} banned profiles")
        else:
            self.log_message.emit(f"[SUMMARY] No banned profiles found")

        self.log_message.emit(f"[SUMMARY] Found {len(self.active_profiles)} active profiles")

        self.running = False
        self.finished.emit()

    def stop(self):
        """Stop the checker process."""
        self.running = False

class CheckerController(QObject):
    """Controller for the checker tab."""

    # Define signals
    progress_updated = pyqtSignal(int, int, str)
    log_message = pyqtSignal(str)
    profiles_updated = pyqtSignal()  # Signal to indicate profiles need to be updated

    def __init__(self):
        super().__init__()

        # Initialize database
        from core.utils.database import Database
        self.db = Database()

        # Initialize worker
        self.worker = None

    def load_profiles(self, file_path=None):
        """
        Load profiles from database or file.

        Args:
            file_path (str, optional): Path to the file to import.

        Returns:
            list: List of profile IDs.
        """
        if file_path:
            # Import profiles from file
            count = self.db.import_profiles_from_file(file_path)
            self.log_message.emit(f"[INFO] Imported {count} profiles from {file_path}")

        # Get profiles from database
        profiles_data = self.db.get_profiles()
        return [p["profile_id"] for p in profiles_data]

    def load_banned_profiles(self):
        """
        Load banned profiles from database.

        Returns:
            list: List of banned profile IDs.
        """
        # Get banned profiles from database
        profiles_data = self.db.get_profiles(status="banned")
        return [p["profile_id"] for p in profiles_data]

    def start_checker(self, profiles, settings):
        """
        Start the checker process.

        Args:
            profiles (list): List of profile IDs to check.
            settings (dict): Checker settings.

        Returns:
            bool: True if started successfully, False otherwise.
        """
        if self.worker and self.worker.isRunning():
            self.log_message.emit("[WARNING] Checker is already running")
            return False

        # Create and start worker thread
        self.worker = CheckerWorker(profiles, settings, self.db)
        self.worker.progress_updated.connect(self.on_progress_updated)
        self.worker.log_message.connect(self.log_message)
        self.worker.profile_banned.connect(self.on_profile_banned)
        self.worker.finished.connect(self.on_checker_finished)
        self.worker.start()

        self.log_message.emit("[INFO] Checker started")
        return True

    def stop_checker(self):
        """
        Stop the checker process.

        Returns:
            bool: True if stopped successfully, False otherwise.
        """
        if not self.worker or not self.worker.isRunning():
            self.log_message.emit("[WARNING] Checker is not running")
            return False

        # Stop worker thread
        self.worker.stop()
        self.log_message.emit("[INFO] Stopping checker...")
        return True

    @pyqtSlot(int, int, str)
    def on_progress_updated(self, current, total, message):
        """Handle progress update from worker thread."""
        self.progress_updated.emit(current, total, message)

    @pyqtSlot(str)
    def on_profile_banned(self, profile):
        """Handle profile banned signal from worker thread."""
        # Log the banned profile
        self.log_message.emit(f"[INFO] Controller: Profile {profile} marked as banned")

    @pyqtSlot()
    def on_checker_finished(self):
        """Handle checker finished signal from worker thread."""
        # Get banned profiles from worker if available
        banned_profiles = []
        if self.worker and hasattr(self.worker, 'banned_profiles'):
            banned_profiles = self.worker.banned_profiles

        # Log completion message
        if banned_profiles:
            self.log_message.emit(f"[SUMMARY] Found {len(banned_profiles)} banned profiles")

            # Log each banned profile for clarity
            for profile in banned_profiles:
                self.log_message.emit(f"[INFO] Profile {profile} is confirmed banned")

                # Make sure the profile is saved as banned in the database
                if self.db.save_profile(profile, status="banned"):
                    self.log_message.emit(f"[INFO] Profile {profile} marked as banned in database")
        else:
            self.log_message.emit("[SUMMARY] No banned profiles found")

        # Log active profiles summary
        active_profiles = []
        if self.worker and hasattr(self.worker, 'active_profiles'):
            active_profiles = self.worker.active_profiles
            self.log_message.emit(f"[SUMMARY] Found {len(active_profiles)} active profiles")

        # Emit signal to update UI
        self.progress_updated.emit(100, 100, "Finished")

        # Emit a custom signal to indicate that banned profiles should be removed from profiles list
        self.log_message.emit("[INFO] Checker finished. Updating profiles lists...")
        self.profiles_updated.emit()
