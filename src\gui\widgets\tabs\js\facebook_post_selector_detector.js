/**
 * Facebook Post Selector Detector
 *
 * This script detects and tests selectors for Facebook posts.
 * It helps to automatically adapt to changes in Facebook's HTML structure.
 */

// Initialize the Facebook Post Selector Detector
function initFacebookPostSelectorDetector() {
    console.log('Initializing Facebook Post Selector Detector...');

    // Object to store detected selectors
    const detectedSelectors = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        selectors: {
            post: [],
            author: [],
            content: [],
            time: [],
            reactions: [],
            media: [],
            postLink: []
        },
        stats: {}
    };

    // Candidate selectors to test
    const candidateSelectors = {
        post: [
            'div[role="article"]',
            'div.x1yztbdb',
            'div.x1lliihq',
            'div.x1n2onr6',
            'div.x78zum5',
            'div.x1iyjqo2',
            'div[data-pagelet*="FeedUnit"]',
            'div[data-pagelet*="GroupFeed"]',
            'div.xdj266r'
        ],
        author: [
            'a[role="link"][tabindex="0"]',
            'a.x1i10hfl',
            'h3.x1heor9g a',
            'span.x193iq5w a',
            'h2 span a',
            'h2 a',
            'h3 a',
            'div.x1heor9g a',
            'div.x1iorvi4 a'
        ],
        content: [
            'div[data-ad-preview="message"]',
            'div.xdj266r',
            'div.x11i5rnm',
            'div.x1iorvi4',
            'div.x1lliihq',
            'div.x1n2onr6 div.x1iorvi4',
            'div[data-ad-comet-preview="message"]',
            'div.x78zum5 div.xdj266r',
            'div.x78zum5 div.x11i5rnm'
        ],
        time: [
            'a[href*="/posts/"] > abbr',
            'a.x1i10hfl.xjbqb8w',
            'span.x4k7w5x a',
            'span.x1yrsyyn a',
            'span.x4k7w5x a[href*="/posts/"]',
            'span.x1yrsyyn a[href*="/posts/"]',
            'a[href*="/permalink/"] > abbr',
            'a[href*="/permalink/"]',
            'a[href*="/posts/"]'
        ],
        reactions: [
            'span.x193iq5w',
            'div.x1n2onr6.x1vqgdyp',
            'div.x78zum5 span',
            'div.x1i10hfl span',
            'div[aria-label*="reaction"]',
            'div[aria-label*="like"]',
            'div.x1i10hfl div.x78zum5',
            'div.x1i10hfl div.x1n2onr6'
        ],
        media: [
            'img.x1ey2m1c.xds687c.x5yr21d',
            'img.x1bwycvy',
            'video.x1lliihq',
            'div.x1qjc9v5 img',
            'div.x1n2onr6 img',
            'div.x78zum5 img',
            'div.x1iyjqo2 img',
            'div.x1n2onr6 video',
            'div.x78zum5 video'
        ],
        postLink: [
            'a[href*="/posts/"]',
            'a[href*="/permalink/"]',
            'a[href*="/groups/"][href*="/permalink/"]',
            'a[href*="/groups/"][href*="/posts/"]',
            'span.x4k7w5x a[href*="/posts/"]',
            'span.x1yrsyyn a[href*="/posts/"]',
            'span.x4k7w5x a[href*="/permalink/"]',
            'span.x1yrsyyn a[href*="/permalink/"]',
            'a[href*="fbid="]',
            'a[href*="story_fbid="]'
        ]
    };

    // Function to test a CSS selector and check if it finds elements
    function testSelector(selector, context = document) {
        try {
            const elements = context.querySelectorAll(selector);
            return elements.length > 0 ? elements.length : 0;
        } catch (e) {
            console.error(`Error testing CSS selector ${selector}:`, e);
            return 0;
        }
    }

    // Function to detect selectors for a specific type
    function detectSelectors(type) {
        console.log(`Detecting ${type} selectors...`);
        const results = {};

        for (const selector of candidateSelectors[type]) {
            const count = testSelector(selector);
            results[selector] = count;

            if (count > 0) {
                console.log(`Found ${count} elements with selector: ${selector}`);
                if (!detectedSelectors.selectors[type].includes(selector)) {
                    detectedSelectors.selectors[type].push(selector);
                }
            }
        }

        detectedSelectors.stats[type] = results;
        return results;
    }

    // Function to detect all selectors
    function detectAllSelectors() {
        for (const type in candidateSelectors) {
            detectSelectors(type);
        }
        return detectedSelectors;
    }

    // Function to test selectors on a specific post
    function testSelectorsOnPost(post) {
        console.log('Testing selectors on post...');

        // Test each selector type on the post
        for (const type in detectedSelectors.selectors) {
            if (type === 'post') continue; // Skip post selectors as we already have the post element

            for (const selector of detectedSelectors.selectors[type]) {
                try {
                    const elements = post.querySelectorAll(selector);
                    if (elements.length > 0) {
                        console.log(`Selector ${selector} found ${elements.length} ${type} elements in post`);
                    }
                } catch (e) {
                    console.error(`Error testing selector ${selector} on post:`, e);
                }
            }
        }
    }

    // Function to test selectors on all visible posts
    function testSelectorsOnAllPosts() {
        // First, find all posts using all detected post selectors
        let posts = [];
        for (const selector of detectedSelectors.selectors.post) {
            const foundPosts = document.querySelectorAll(selector);
            if (foundPosts.length > 0) {
                console.log(`Found ${foundPosts.length} posts with selector: ${selector}`);
                posts = [...posts, ...foundPosts];
            }
        }

        // Remove duplicates
        posts = Array.from(new Set(posts));
        console.log(`Testing selectors on ${posts.length} visible posts...`);

        for (let i = 0; i < Math.min(posts.length, 5); i++) { // Limit to 5 posts to avoid too many operations
            testSelectorsOnPost(posts[i]);
        }
    }

    // Function to save the detected selectors
    function saveDetectedSelectors() {
        // This function will be implemented in the Python controller
        // Here we just return the detected selectors
        return JSON.stringify(detectedSelectors, null, 2);
    }

    // Function to add new candidate selectors
    function addCandidateSelectors(type, newSelectors) {
        if (Array.isArray(newSelectors)) {
            for (const selector of newSelectors) {
                if (!candidateSelectors[type].includes(selector)) {
                    candidateSelectors[type].push(selector);
                }
            }
        }
    }

    // Function to test a specific selector
    function testSpecificSelector(type, selector) {
        const count = testSelector(selector);
        console.log(`Tested selector "${selector}" for ${type}: found ${count} elements`);

        if (count > 0 && !detectedSelectors.selectors[type].includes(selector)) {
            detectedSelectors.selectors[type].push(selector);
        }

        return count;
    }

    // Function to generate dynamic selectors based on page content
    function generateDynamicSelectors() {
        console.log('Generating dynamic selectors based on page content...');

        // Find all elements with role="article" as a starting point
        const articles = document.querySelectorAll('div[role="article"]');
        if (articles.length === 0) {
            console.log('No articles found, trying alternative methods...');
            // Try to find posts by looking for common patterns
            const possiblePosts = document.querySelectorAll('div.x1yztbdb, div.x1lliihq, div.x1n2onr6');
            if (possiblePosts.length > 0) {
                console.log(`Found ${possiblePosts.length} possible posts using alternative selectors`);

                // Add these selectors to candidates
                for (const post of possiblePosts) {
                    const classes = post.className.split(' ');
                    if (classes.length > 0) {
                        const selector = `div.${classes[0]}`;
                        addCandidateSelectors('post', [selector]);
                    }
                }
            }
        } else {
            console.log(`Found ${articles.length} articles with role="article"`);
        }

        // Process each article to find common patterns
        for (let i = 0; i < Math.min(articles.length, 5); i++) {
            const article = articles[i];

            // Find links that might be post links
            const links = article.querySelectorAll('a[href*="/posts/"], a[href*="/permalink/"]');
            for (const link of links) {
                // Generate selectors based on the link's attributes and position
                const classes = link.className.split(' ');
                if (classes.length > 0) {
                    const selector = `a.${classes[0]}`;
                    addCandidateSelectors('postLink', [selector]);
                }

                // Check if the link has an abbr child (timestamp)
                const abbr = link.querySelector('abbr');
                if (abbr) {
                    addCandidateSelectors('time', [`a[href*="${link.href.includes('/posts/') ? '/posts/' : '/permalink/'}"] > abbr`]);
                }

                // Check parent elements for patterns
                let parent = link.parentElement;
                for (let j = 0; j < 3 && parent; j++) {
                    const parentClasses = parent.className.split(' ');
                    if (parentClasses.length > 0) {
                        const parentSelector = `${parent.tagName.toLowerCase()}.${parentClasses[0]}`;
                        addCandidateSelectors('time', [`${parentSelector} a`]);
                    }
                    parent = parent.parentElement;
                }
            }

            // Find content divs
            const contentDivs = article.querySelectorAll('div[data-ad-preview="message"], div.xdj266r, div.x11i5rnm');
            for (const div of contentDivs) {
                const classes = div.className.split(' ');
                if (classes.length > 0) {
                    const selector = `div.${classes[0]}`;
                    addCandidateSelectors('content', [selector]);
                }
            }

            // Find author links
            const authorLinks = article.querySelectorAll('a[role="link"][tabindex="0"], h3 a, h2 a');
            for (const link of authorLinks) {
                const classes = link.className.split(' ');
                if (classes.length > 0) {
                    const selector = `a.${classes[0]}`;
                    addCandidateSelectors('author', [selector]);
                }

                // Check parent elements for patterns
                let parent = link.parentElement;
                for (let j = 0; j < 3 && parent; j++) {
                    const parentClasses = parent.className.split(' ');
                    if (parentClasses.length > 0) {
                        const parentSelector = `${parent.tagName.toLowerCase()}.${parentClasses[0]}`;
                        addCandidateSelectors('author', [`${parentSelector} a`]);
                    }
                    parent = parent.parentElement;
                }
            }

            // Find images and videos
            const media = article.querySelectorAll('img, video');
            for (const item of media) {
                const classes = item.className.split(' ');
                if (classes.length > 0) {
                    const selector = `${item.tagName.toLowerCase()}.${classes[0]}`;
                    addCandidateSelectors('media', [selector]);
                }
            }

            // Find reaction elements
            const reactions = article.querySelectorAll('div[aria-label*="reaction"], div[aria-label*="like"]');
            for (const reaction of reactions) {
                const classes = reaction.className.split(' ');
                if (classes.length > 0) {
                    const selector = `div.${classes[0]}`;
                    addCandidateSelectors('reactions', [selector]);
                }

                // Check child elements for patterns
                const spans = reaction.querySelectorAll('span');
                for (const span of spans) {
                    const spanClasses = span.className.split(' ');
                    if (spanClasses.length > 0) {
                        const spanSelector = `span.${spanClasses[0]}`;
                        addCandidateSelectors('reactions', [spanSelector]);
                    }
                }
            }
        }

        console.log('Dynamic selector generation complete');
        console.log('Candidate selectors:', candidateSelectors);
    }

    // Function to get the detected selectors
    function getDetectedSelectors() {
        return detectedSelectors;
    }

    // Return the public API
    return {
        detectSelectors,
        detectAllSelectors,
        testSelectorsOnPost,
        testSelectorsOnAllPosts,
        saveDetectedSelectors,
        addCandidateSelectors,
        testSpecificSelector,
        generateDynamicSelectors,
        getDetectedSelectors
    };
}

// Create the global facebookPostSelectorDetector object
window.facebookPostSelectorDetector = initFacebookPostSelectorDetector();
