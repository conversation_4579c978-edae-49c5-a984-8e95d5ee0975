/**
 * Facebook Group Members Extractor - Direct DOM Injection Version
 *
 * This script is designed to be injected directly into the Facebook page DOM
 * to extract members from Facebook groups.
 */

// Main extraction function that returns a Promise
function extractFacebookGroupMembers(config) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('Starting Facebook Group Members extraction with config:', config);

            // Default configuration
            const settings = {
                membersToExtract: config.membersToExtract || 100,
                minDelay: config.minDelay || 1500,
                maxDelay: config.maxDelay || 3000,
                scrollStep: config.scrollStep || 800,
                maxEmptyScrolls: config.maxEmptyScrolls || 3,
                maxScrolls: config.maxScrolls || 50,
                debug: config.debug || false
            };

            // State management
            const state = {
                members: {
                    admins: [],
                    moderators: [],
                    regular: []
                },
                processedElements: new WeakSet(),
                emptyScrolls: 0,
                totalExtracted: 0,
                isComplete: false
            };

            // Helper functions
            const helpers = {
                delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

                randomDelay: () => {
                    const min = settings.minDelay;
                    const max = settings.maxDelay;
                    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
                    return helpers.delay(delay);
                },

                safeQuery: (selector, parent = document) => {
                    try {
                        return Array.from(parent.querySelectorAll(selector));
                    } catch (e) {
                        console.warn('Query failed:', e);
                        return [];
                    }
                },

                log: (message) => {
                    if (settings.debug) {
                        console.log(`[FB Extractor] ${message}`);
                    }
                }
            };

            // Member extraction functions
            const extractor = {
                // Extract member data from element
                extractMemberData: function(element) {
                    try {
                        // Intentar diferentes selectores para encontrar el nombre
                        const nameSelectors = [
                            '[role="link"][tabindex="0"]',
                            'a[href*="/user/"]',
                            'a[href*="/profile.php"]',
                            'span[dir="auto"]',
                            'a.x1i10hfl',  // Clase común en enlaces de perfil
                            'strong',       // A veces el nombre está en un elemento strong
                            'a'            // Último recurso: cualquier enlace
                        ];

                        let nameElement = null;
                        for (const selector of nameSelectors) {
                            const el = element.querySelector(selector);
                            if (el && el.textContent && el.textContent.trim()) {
                                nameElement = el;
                                break;
                            }
                        }

                        if (!nameElement?.textContent?.trim()) {
                            console.log('No name element found for:', element.outerHTML.substring(0, 100));
                            return null;
                        }

                        // Buscar el rol (admin, moderador, etc.)
                        const roleSelectors = [
                            '[aria-label*="Admin"]',
                            '[aria-label*="Moderator"]',
                            '[aria-label*="Administrador"]',  // Español
                            '[aria-label*="Modérateur"]',     // Francés
                            '[aria-label*="مشرف"]'           // Árabe
                        ];

                        let roleElement = null;
                        for (const selector of roleSelectors) {
                            const el = element.querySelector(selector);
                            if (el) {
                                roleElement = el;
                                break;
                            }
                        }

                        // Buscar la imagen de avatar
                        const avatarElement = element.querySelector('img[src*="scontent"], image[href*="scontent"]');

                        // Extraer ID del perfil
                        let id = '';
                        let profileUrl = nameElement.href || '';

                        if (profileUrl) {
                            // Intentar extraer ID de diferentes formatos de URL
                            const idPatterns = [
                                /\/user\/([^\/\?]+)/,           // /user/123456789
                                /facebook\.com\/([^\/\?]+)/,   // facebook.com/username
                                /profile\.php\?id=([^&]+)/,    // profile.php?id=123456789
                                /\/profile\.php\?id=([^&]+)/   // /profile.php?id=123456789
                            ];

                            for (const pattern of idPatterns) {
                                const match = profileUrl.match(pattern);
                                if (match && match[1]) {
                                    id = match[1];
                                    break;
                                }
                            }
                        }

                        // Validar el ID y el nombre antes de devolver el miembro
                        const memberName = nameElement.textContent.trim();

                        // Lista de nombres a filtrar (elementos de UI y no miembros reales)
                        const uiElementNames = [
                            'See all', 'Learn More', 'See More', 'View More', 'Load More',
                            'Unread', 'Mark as read', 'Confirm', 'Delete', 'Cancel',
                            'Friend Request', 'mutual friend', 'Notification', 'Welcome'
                        ];

                        // Verificar si el nombre parece ser un elemento de UI
                        const isUIElement = uiElementNames.some(uiName => memberName.includes(uiName));

                        // Verificar si el ID parece ser un elemento de UI
                        const isUIElementID = ['notifications', 'groups', 'friends', 'help', 'messages'].includes(id);

                        // Si parece ser un elemento de UI, no lo incluimos
                        if (isUIElement || isUIElementID) {
                            console.log(`Filtering out UI element: ${memberName} (ID: ${id})`);
                            return null;
                        }

                        return {
                            id: id,
                            name: memberName,
                            profileUrl: profileUrl,
                            role: roleElement?.getAttribute('aria-label')?.split('·')[0].trim() || 'Member',
                            avatar: avatarElement?.src || avatarElement?.href?.baseVal || '',
                            extractedAt: new Date().toISOString()
                        };
                    } catch (error) {
                        console.error('Extraction error:', error);
                        return null;
                    }
                },

                // Find member elements
                findMemberElements: function() {
                    // Different selectors to try
                    const selectors = [
                        '[role="listitem"]',
                        '[data-visualcompletion="ignore-dynamic"]',
                        'div[aria-label*="member"]',
                        'div[aria-label*="عضو"]',
                        // Selectores adicionales para Facebook en diferentes idiomas
                        'div[aria-label*="miembro"]',  // Español
                        'div[aria-label*="membre"]',   // Francés
                        'div[aria-label*="mitglied"]', // Alemán
                        'div[aria-label*="membro"]',   // Italiano/Portugués
                        // Selectores más específicos para la nueva interfaz de Facebook
                        'div[data-pagelet*="GroupProfileGrid"] div[role="article"]',
                        'div[data-pagelet*="GroupFeed"] div[role="article"]',
                        // Selectores genéricos que podrían contener miembros
                        'div.x1qjc9v5', // Clase común en elementos de lista de miembros
                        'div.x1lliihq'  // Otra clase común en elementos de lista
                    ];

                    let results = [];

                    // Try each selector
                    for (const selector of selectors) {
                        try {
                            const elements = helpers.safeQuery(selector);
                            if (elements.length > 0) {
                                helpers.log(`Found ${elements.length} elements with selector: ${selector}`);
                                results = results.concat(elements);
                            }
                        } catch (e) {
                            console.error(`Error with selector ${selector}:`, e);
                        }
                    }

                    return results;
                },

                // Extract special sections (admins, moderators)
                extractSpecialSections: function() {
                    const sections = [
                        { type: 'admins', selectors: ['div[aria-label*="Admins"]', 'div[aria-label*="المشرفين"]'] },
                        { type: 'moderators', selectors: ['div[aria-label*="Moderators"]', 'div[aria-label*="المشرفين المساعدين"]'] }
                    ];

                    // Conjunto para rastrear IDs de miembros ya procesados
                    const processedMemberIds = new Set();

                    // Añadir IDs existentes al conjunto
                    state.members.admins.forEach(m => m.id && processedMemberIds.add(m.id));
                    state.members.moderators.forEach(m => m.id && processedMemberIds.add(m.id));
                    state.members.regular.forEach(m => m.id && processedMemberIds.add(m.id));

                    sections.forEach(section => {
                        for (const selector of section.selectors) {
                            const container = document.querySelector(selector);
                            if (container) {
                                helpers.safeQuery('[role="listitem"]', container).forEach(member => {
                                    if (!state.processedElements.has(member)) {
                                        const memberData = this.extractMemberData(member);
                                        if (memberData && memberData.id) {
                                            // Verificar si ya hemos procesado este ID
                                            if (!processedMemberIds.has(memberData.id)) {
                                                if (section.type === 'admins') {
                                                    state.members.admins.push(memberData);
                                                } else if (section.type === 'moderators') {
                                                    state.members.moderators.push(memberData);
                                                }

                                                // Marcar este ID como procesado
                                                processedMemberIds.add(memberData.id);
                                                state.processedElements.add(member);
                                                state.totalExtracted++;

                                                helpers.log(`Extracted special member: ${memberData.name} (ID: ${memberData.id})`);
                                            } else {
                                                // Ya hemos procesado este miembro, solo marcamos el elemento
                                                state.processedElements.add(member);
                                                helpers.log(`Skipping duplicate special member: ${memberData.name} (ID: ${memberData.id})`);
                                            }
                                        } else {
                                            // Marcar el elemento como procesado aunque no obtuvimos datos válidos
                                            state.processedElements.add(member);
                                        }
                                    }
                                });
                                break;
                            }
                        }
                    });

                    helpers.log(`Total unique members after special sections: ${processedMemberIds.size}`);
                    return processedMemberIds.size;
                },

                // Extract all visible members
                extractVisibleMembers: function() {
                    const memberElements = this.findMemberElements();
                    const newMembers = [];

                    // Conjunto para rastrear IDs de miembros ya procesados
                    const processedMemberIds = new Set();

                    // Añadir IDs existentes al conjunto
                    state.members.admins.forEach(m => m.id && processedMemberIds.add(m.id));
                    state.members.moderators.forEach(m => m.id && processedMemberIds.add(m.id));
                    state.members.regular.forEach(m => m.id && processedMemberIds.add(m.id));

                    for (const element of memberElements) {
                        if (!state.processedElements.has(element)) {
                            const memberData = this.extractMemberData(element);

                            if (memberData && memberData.id) {
                                // Verificar si ya hemos procesado este ID
                                if (!processedMemberIds.has(memberData.id)) {
                                    // Categorize member
                                    if (memberData.role.includes('Admin')) {
                                        state.members.admins.push(memberData);
                                    } else if (memberData.role.includes('Moderator')) {
                                        state.members.moderators.push(memberData);
                                    } else {
                                        state.members.regular.push(memberData);
                                    }

                                    // Marcar este ID como procesado
                                    processedMemberIds.add(memberData.id);
                                    state.processedElements.add(element);
                                    state.totalExtracted++;
                                    newMembers.push(memberData);

                                    helpers.log(`Extracted member: ${memberData.name} (ID: ${memberData.id})`);
                                } else {
                                    // Ya hemos procesado este miembro, solo marcamos el elemento
                                    state.processedElements.add(element);
                                    helpers.log(`Skipping duplicate member: ${memberData.name} (ID: ${memberData.id})`);
                                }
                            } else {
                                // Marcar el elemento como procesado aunque no obtuvimos datos válidos
                                state.processedElements.add(element);
                            }
                        }
                    }

                    helpers.log(`Extracted ${newMembers.length} new unique members (total unique: ${processedMemberIds.size})`);
                    return newMembers;
                },

                // Scroll the page with improved strategy
                scrollPage: async function() {
                    if (state.isComplete) return false;

                    const startPos = window.scrollY;
                    const canScroll = (startPos + window.innerHeight) < document.documentElement.scrollHeight - 100;

                    // Calcular el número de miembros únicos actuales
                    const uniqueIds = new Set();
                    state.members.admins.forEach(m => m.id && uniqueIds.add(m.id));
                    state.members.moderators.forEach(m => m.id && uniqueIds.add(m.id));
                    state.members.regular.forEach(m => m.id && uniqueIds.add(m.id));
                    const uniqueCount = uniqueIds.size;

                    // Guardar el conteo antes del desplazamiento para comparar después
                    const beforeScrollCount = uniqueCount;

                    if (canScroll) {
                        // Usar un desplazamiento más grande para evitar duplicados
                        const scrollAmount = settings.scrollStep * 1.5;

                        helpers.log(`Scrolling by ${scrollAmount}px (current unique members: ${uniqueCount})`);

                        window.scrollBy({
                            top: scrollAmount,
                            behavior: 'smooth'
                        });

                        // Esperar un tiempo aleatorio para que la página cargue
                        await helpers.randomDelay();

                        // Esperar un poco más para asegurar que los elementos se carguen
                        await helpers.delay(500);

                        // Check if scroll actually happened
                        if (Math.abs(window.scrollY - startPos) < 50) {
                            state.emptyScrolls++;
                            helpers.log(`Scroll didn't move much, empty scroll count: ${state.emptyScrolls}`);

                            if (state.emptyScrolls >= settings.maxEmptyScrolls) {
                                helpers.log('Max empty scrolls reached, stopping extraction');
                                state.isComplete = true;
                                return false;
                            }
                        } else {
                            // Verificar si se encontraron nuevos miembros después del desplazamiento
                            // Extraer miembros visibles para actualizar el conteo
                            this.extractVisibleMembers();

                            // Recalcular el número de miembros únicos
                            const afterIds = new Set();
                            state.members.admins.forEach(m => m.id && afterIds.add(m.id));
                            state.members.moderators.forEach(m => m.id && afterIds.add(m.id));
                            state.members.regular.forEach(m => m.id && afterIds.add(m.id));
                            const afterScrollCount = afterIds.size;

                            const newMembers = afterScrollCount - beforeScrollCount;
                            helpers.log(`Found ${newMembers} new unique members after scrolling`);

                            if (newMembers > 0) {
                                // Se encontraron nuevos miembros, reiniciar contador de desplazamientos vacíos
                                state.emptyScrolls = 0;
                            } else {
                                // No se encontraron nuevos miembros, incrementar contador
                                state.emptyScrolls++;
                                helpers.log(`No new members found, empty scroll count: ${state.emptyScrolls}`);
                            }

                            if (state.emptyScrolls >= settings.maxEmptyScrolls) {
                                helpers.log('Max empty scrolls reached, stopping extraction');
                                state.isComplete = true;
                                return false;
                            }
                        }

                        return true;
                    } else {
                        helpers.log('Reached bottom of page');
                        state.emptyScrolls++;

                        if (state.emptyScrolls >= settings.maxEmptyScrolls) {
                            helpers.log('Max empty scrolls reached, stopping extraction');
                            state.isComplete = true;
                            return false;
                        }

                        return false;
                    }
                },

                // Check if extraction is complete
                checkCompletion: function() {
                    if (typeof settings.membersToExtract === 'number') {
                        if (state.totalExtracted >= settings.membersToExtract) {
                            helpers.log(`Extracted ${state.totalExtracted} members, target reached`);
                            state.isComplete = true;
                            return true;
                        }
                    }

                    return false;
                }
            };

            // Main extraction loop with improved strategy
            async function extractionLoop(scrollCount = 0) {
                if (state.isComplete || scrollCount >= settings.maxScrolls) {
                    helpers.log('Extraction complete or max scrolls reached');
                    return finishExtraction();
                }

                // Calcular el número de miembros únicos antes de la extracción
                const uniqueIdsBefore = new Set();
                state.members.admins.forEach(m => m.id && uniqueIdsBefore.add(m.id));
                state.members.moderators.forEach(m => m.id && uniqueIdsBefore.add(m.id));
                state.members.regular.forEach(m => m.id && uniqueIdsBefore.add(m.id));
                const uniqueCountBefore = uniqueIdsBefore.size;

                // Extract members
                const newMembers = extractor.extractVisibleMembers();

                // Calcular el número de miembros únicos después de la extracción
                const uniqueIdsAfter = new Set();
                state.members.admins.forEach(m => m.id && uniqueIdsAfter.add(m.id));
                state.members.moderators.forEach(m => m.id && uniqueIdsAfter.add(m.id));
                state.members.regular.forEach(m => m.id && uniqueIdsAfter.add(m.id));
                const uniqueCountAfter = uniqueIdsAfter.size;

                const newUniqueMembers = uniqueCountAfter - uniqueCountBefore;

                helpers.log(`Extracted ${newMembers.length} new members, ${newUniqueMembers} unique (total unique: ${uniqueCountAfter})`);

                // Update global progress counter
                window.currentExtractedCount = uniqueCountAfter;

                // Check if we've reached the target
                if (extractor.checkCompletion()) {
                    return finishExtraction();
                }

                // Si no encontramos nuevos miembros únicos en esta iteración, incrementar contador de iteraciones vacías
                if (newUniqueMembers === 0) {
                    state.emptyScrolls++;
                    helpers.log(`No new unique members found in this iteration, empty count: ${state.emptyScrolls}`);

                    if (state.emptyScrolls >= settings.maxEmptyScrolls) {
                        helpers.log('Max empty iterations reached, stopping extraction');
                        state.isComplete = true;
                        return finishExtraction();
                    }
                }

                // Scroll down
                const canScroll = await extractor.scrollPage();

                if (!canScroll) {
                    helpers.log('Cannot scroll further');
                    return finishExtraction();
                }

                // Continue extraction in next tick to avoid stack overflow
                setTimeout(() => extractionLoop(scrollCount + 1), 0);
            }

            // Finish extraction and return results
            function finishExtraction() {
                helpers.log('Finishing extraction');

                // Crear una lista plana de miembros únicos
                const uniqueMembers = [];
                const processedIds = new Set();

                // Función para añadir miembros únicos a la lista plana
                const addUniqueMembers = (membersList) => {
                    membersList.forEach(member => {
                        if (member.id && !processedIds.has(member.id)) {
                            uniqueMembers.push(member);
                            processedIds.add(member.id);
                        }
                    });
                };

                // Añadir todos los miembros a la lista plana
                addUniqueMembers(state.members.admins);
                addUniqueMembers(state.members.moderators);
                addUniqueMembers(state.members.regular);

                // Filtrar elementos de UI que puedan haberse colado
                const filteredMembers = uniqueMembers.filter(member => {
                    // Lista de IDs a filtrar
                    const uiElementIds = ['notifications', 'groups', 'friends', 'help', 'messages'];
                    return !uiElementIds.includes(member.id);
                });

                // Prepare result
                const result = {
                    metadata: {
                        extractedAt: new Date().toISOString(),
                        totalMembers: state.totalExtracted,
                        uniqueMembers: filteredMembers.length,
                        settings: settings
                    },
                    members: {
                        admins: state.members.admins,
                        moderators: state.members.moderators,
                        regular: state.members.regular,
                        // Añadir la lista plana de miembros únicos
                        all: filteredMembers
                    }
                };

                helpers.log(`Extraction completed. Found ${state.totalExtracted} total members, ${filteredMembers.length} unique members`);
                resolve(result);
            }

            // Start extraction
            try {
                helpers.log('Starting extraction process');

                // Extract special sections first
                extractor.extractSpecialSections();
                helpers.log(`Extracted ${state.totalExtracted} members from special sections`);

                // Start extraction loop
                await extractionLoop();
            } catch (error) {
                console.error('Extraction error:', error);
                reject(error);
            }
        } catch (error) {
            console.error('Fatal extraction error:', error);
            reject(error);
        }
    });
}

// Make the function available globally with compatibility checks
try {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
        // Make functions and variables available globally
        window.extractFacebookGroupMembers = extractFacebookGroupMembers;
        window._lastExtractionResult = null;
        window._lastExtractionError = null;
        window.currentExtractedCount = 0;

        // Log successful initialization
        console.log('Facebook Member Extractor initialized successfully');
    } else {
        console.error('Not in a browser environment');
    }
} catch (e) {
    console.error('Error initializing Facebook Member Extractor:', e);
}

// Helper function to run extraction and store results
try {
    window.runExtraction = function(config) {
        try {
            console.log('Starting extraction with config:', config);
            window._lastExtractionResult = null;
            window._lastExtractionError = null;
            window.currentExtractedCount = 0;

            // Verify that the extraction function exists
            if (typeof window.extractFacebookGroupMembers !== 'function') {
                console.error('Extraction function not available');
                window._lastExtractionError = 'Extraction function not available';
                window._lastExtractionResult = {
                    success: false,
                    error: 'Extraction function not available'
                };
                return window._lastExtractionResult;
            }

            // Run the extraction
            return window.extractFacebookGroupMembers(config)
                .then(function(result) {
                    console.log('Extraction completed successfully, storing result');
                    window._lastExtractionResult = {
                        success: true,
                        data: result
                    };
                    return window._lastExtractionResult;
                })
                .catch(function(error) {
                    console.error('Extraction failed:', error);
                    window._lastExtractionError = error.toString();
                    window._lastExtractionResult = {
                        success: false,
                        error: error.toString()
                    };
                    return window._lastExtractionResult;
                });
        } catch (e) {
            console.error('Error in runExtraction:', e);
            window._lastExtractionError = e.toString();
            window._lastExtractionResult = {
                success: false,
                error: e.toString()
            };
            return window._lastExtractionResult;
        }
    };

    console.log('runExtraction function defined successfully');
} catch (e) {
    console.error('Failed to define runExtraction function:', e);
}
