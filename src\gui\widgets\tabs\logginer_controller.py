import os
import logging
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QThread

from core.utils.database import Database

# Configure logger
logger = logging.getLogger(__name__)

class LogginerWorker(QThread):
    """Worker thread for running the logginer process."""
    
    # Define signals
    progress_updated = pyqtSignal(int, int, str)
    log_message = pyqtSignal(str)
    finished = pyqtSignal()
    
    def __init__(self, not_logged_in, settings):
        super().__init__()
        self.not_logged_in = not_logged_in
        self.settings = settings
        self.running = False
    
    def run(self):
        """Run the logginer process."""
        self.running = True
        total = len(self.not_logged_in)
        
        # Simulate logging in accounts
        for i, account in enumerate(self.not_logged_in):
            if not self.running:
                break
            
            # Update progress
            progress = int((i + 1) / total * 100)
            self.progress_updated.emit(i + 1, total, f"Logging in account {i + 1}/{total}")
            self.log_message.emit(f"[INFO] Logging in account: {account}")
            
            # Simulate login delay
            self.msleep(500)
            
            # Simulate random result
            import random
            if random.random() < 0.8:  # 80% chance of successful login
                self.log_message.emit(f"[SUCCESS] Account {account} logged in successfully")
            else:
                self.log_message.emit(f"[ERROR] Failed to log in account {account}")
        
        self.running = False
        self.finished.emit()
    
    def stop(self):
        """Stop the logginer process."""
        self.running = False


class LogginerController(QObject):
    """Controller for the logginer tab."""
    
    # Define signals
    progress_updated = pyqtSignal(int, int, str)
    log_message = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Create database instance
        self.db = Database()
        
        # Initialize worker
        self.worker = None
    
    def load_not_logged_in(self, file_path=None):
        """
        Load not logged in accounts from a file or database.
        
        Args:
            file_path (str, optional): Path to the not logged in accounts file.
            
        Returns:
            list: List of not logged in account IDs.
        """
        if file_path:
            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if content:
                    # Split content into lines and filter empty lines
                    accounts = [line.strip() for line in content.split('\n') if line.strip()]
                    
                    # Save accounts to database
                    count = 0
                    for account_id in accounts:
                        if self.db.save_profile(account_id, status="not_logged_in"):
                            count += 1
                    
                    self.log_message.emit(f"[INFO] Imported {count} not logged in accounts from file to database")
                else:
                    self.log_message.emit(f"[WARNING] No not logged in accounts found in {file_path}")
                    accounts = []
            except Exception as e:
                self.log_message.emit(f"[ERROR] Failed to import not logged in accounts from file: {e}")
                accounts = []
        
        # Get not logged in accounts from database
        accounts_data = self.db.get_profiles(status="not_logged_in")
        accounts = [p['profile_id'] for p in accounts_data]
        
        self.log_message.emit(f"[INFO] Loaded {len(accounts)} not logged in accounts from database")
        return accounts
    
    def load_accounts(self, file_path=None):
        """
        Load accounts from a file or database.
        
        Args:
            file_path (str, optional): Path to the accounts file.
            
        Returns:
            list: List of account IDs.
        """
        if file_path:
            try:
                # Check if it's a CSV file
                if file_path.lower().endswith('.csv'):
                    import csv
                    
                    # Read CSV file
                    with open(file_path, 'r', encoding='utf-8', newline='') as f:
                        reader = csv.DictReader(f)
                        accounts = []
                        
                        # Save accounts to database
                        count = 0
                        for row in reader:
                            # Extract account data
                            fb_id = row.get('FB', '')
                            profile_id = row.get('Profile ID', '')
                            browser_id = row.get('Browser ID', '')
                            username = row.get('User', '')
                            password = row.get('Pass', '')
                            status = row.get('Statue', 'active')
                            
                            # Save account to database
                            if self.db.save_account(fb_id, profile_id, browser_id, username, password, status):
                                count += 1
                                accounts.append(profile_id)
                    
                    self.log_message.emit(f"[INFO] Imported {count} accounts from CSV file to database")
                else:
                    # Read text file
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                    
                    if content:
                        # Split content into lines and filter empty lines
                        accounts = [line.strip() for line in content.split('\n') if line.strip()]
                        
                        # Save accounts to database
                        count = 0
                        for account_id in accounts:
                            if self.db.save_profile(account_id):
                                count += 1
                        
                        self.log_message.emit(f"[INFO] Imported {count} accounts from file to database")
                    else:
                        self.log_message.emit(f"[WARNING] No accounts found in {file_path}")
                        accounts = []
            except Exception as e:
                self.log_message.emit(f"[ERROR] Failed to import accounts from file: {e}")
                accounts = []
        
        # Get accounts from database
        accounts_data = self.db.get_accounts()
        accounts = [a['profile_id'] for a in accounts_data if a['profile_id']]
        
        self.log_message.emit(f"[INFO] Loaded {len(accounts)} accounts from database")
        return accounts
    
    def save_not_logged_in(self, accounts, file_path=None):
        """
        Save not logged in accounts to the database and optionally to a file.
        
        Args:
            accounts (list): List of not logged in account IDs.
            file_path (str, optional): Path to the not logged in accounts file.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Save to database
            count = 0
            for account_id in accounts:
                if self.db.save_profile(account_id, status="not_logged_in"):
                    count += 1
            
            self.log_message.emit(f"[INFO] Saved {count} not logged in accounts to database")
            
            # Optionally save to file
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(accounts))
                self.log_message.emit(f"[INFO] Saved {len(accounts)} not logged in accounts to {file_path}")
            
            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save not logged in accounts: {e}")
            return False
    
    def save_accounts(self, accounts, file_path=None):
        """
        Save accounts to the database and optionally to a file.
        
        Args:
            accounts (list): List of account IDs.
            file_path (str, optional): Path to the accounts file.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Save to database
            count = 0
            for account_id in accounts:
                if self.db.save_profile(account_id):
                    count += 1
            
            self.log_message.emit(f"[INFO] Saved {count} accounts to database")
            
            # Optionally save to file
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(accounts))
                self.log_message.emit(f"[INFO] Saved {len(accounts)} accounts to {file_path}")
            
            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save accounts: {e}")
            return False
    
    def start_logginer(self, not_logged_in, settings):
        """
        Start the logginer process.
        
        Args:
            not_logged_in (list): List of not logged in account IDs.
            settings (dict): Logginer settings.
            
        Returns:
            bool: True if started successfully, False otherwise.
        """
        if self.worker and self.worker.isRunning():
            self.log_message.emit("[WARNING] Logginer is already running")
            return False
        
        # Create and start worker thread
        self.worker = LogginerWorker(not_logged_in, settings)
        self.worker.progress_updated.connect(self.on_progress_updated)
        self.worker.log_message.connect(self.log_message)
        self.worker.finished.connect(self.on_logginer_finished)
        self.worker.start()
        
        self.log_message.emit("[INFO] Logginer started")
        return True
    
    def stop_logginer(self):
        """
        Stop the logginer process.
        
        Returns:
            bool: True if stopped successfully, False otherwise.
        """
        if not self.worker or not self.worker.isRunning():
            self.log_message.emit("[WARNING] Logginer is not running")
            return False
        
        # Stop worker thread
        self.worker.stop()
        self.worker.wait()
        
        self.log_message.emit("[INFO] Logginer stopped")
        return True
    
    @pyqtSlot(int, int, str)
    def on_progress_updated(self, current, total, message):
        """Handle progress update from worker thread."""
        self.progress_updated.emit(current, total, message)
    
    @pyqtSlot()
    def on_logginer_finished(self):
        """Handle logginer finished signal."""
        self.log_message.emit("[INFO] Logginer finished")
