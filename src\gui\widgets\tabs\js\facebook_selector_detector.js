/**
 * Facebook Selector Detector
 *
 * هذا الملف يقوم باستكشاف محددات العناصر المختلفة في صفحة فيسبوك
 * ويقوم بتخزينها لاستخدامها في استخراج البيانات
 *
 * يدعم استكشاف محددات المنشورات والتعليقات والمؤلفين والروابط
 * ويستخدم تقنيات متقدمة مثل اعتراض طلبات GraphQL
 */

function initFacebookSelectorDetector() {
    'use strict';

    // قاموس لتخزين المحددات المكتشفة
    const detectedSelectors = {
        timestamp: new Date().toISOString(),
        version: '2.0',
        selectors: {
            // محددات XPath
            xpathSelectors: {
                reply_button_xpaths: [
                    "//div[13]//div[4]//div[3]/div[{}]//ul/li[3]/div/div",
                    "//div[13]//div[4]//div[3]/div[{}]//ul/li[3]",
                    "//div[13]//div[4]//div[3]/div[{}]//div[2]//div[2]//ul/li[3]",
                    "//div[2]//div[4]//div[3]/div[{}]//div[1]//div[2]//ul/li[3]"
                ],
                comment_full_xpaths: [
                    "//div[13]//div[4]//div[3]/div[{}]",
                    "//div[2]//div[4]//div[3]/div[{}]",
                    "//div[2]//div[4]//div[3]/div[{}]",
                    "//div[3]//div[4]//div[3]/div[{}]",
                    "//div[2]//div[4]//div[2]/div[3]/div[{}]"
                ]
            },
            // محددات المنشورات
            post: [],
            // محددات المؤلف
            author: [],
            // محددات المحتوى
            content: [],
            // محددات الوقت
            time: [],
            // محددات التفاعلات
            reactions: [],
            // محددات الوسائط
            media: [],
            // محددات روابط المنشورات
            postLink: [],
            // محددات التعليقات
            comments: [],
            // محددات المشاركات
            shares: [],
            // محددات للتعليقات (متوافقة مع الإصدار السابق)
            commentButtons: [],
            commentInputFields: [],
            commentSubmitButtons: [],
            commentContainers: [],
            replyButtons: []
        },
        // إحصائيات النجاح لكل محدد
        stats: {
            post: {},
            author: {},
            content: {},
            time: {},
            reactions: {},
            media: {},
            postLink: {},
            comments: {},
            shares: {},
            commentButtons: {},
            commentInputFields: {},
            commentSubmitButtons: {},
            commentContainers: {},
            replyButtons: {}
        }
    };

    // المحددات المرشحة للاختبار
    const candidateSelectors = {
        // محددات المنشورات
        post: [
            'div[role="article"]',
            'div.x1yztbdb',
            'div.x1lliihq',
            'div.x1n2onr6',
            'div.x78zum5',
            'div[data-pagelet*="FeedUnit"]',
            'div.xdj266r',
            'div[data-ad-preview="message"]',
            'div[data-ad-comet-preview="message"]'
        ],
        // محددات المؤلف
        author: [
            'a[role="link"][tabindex="0"]',
            'h4 a[role="link"]',
            'h3 a[role="link"]',
            'h2 a[role="link"]',
            'a[role="link"][aria-hidden="false"]',
            'a.x1i10hfl',
            'span.x193iq5w a',
            'span.xt0psk2 a',
            'div.x1r8uery a'
        ],
        // محددات المحتوى
        content: [
            'div[data-ad-preview="message"]',
            'div[data-ad-comet-preview="message"]',
            'div.xdj266r',
            'div.x1iorvi4',
            'span[dir="auto"]',
            'div.x1n2onr6.x1iorvi4',
            'div.x78zum5.xdt5ytf',
            'div.x1iorvi4.x1pi30zi'
        ],
        // محددات الوقت
        time: [
            'a[href*="/posts/"] > abbr',
            'a[href*="/permalink/"] > abbr',
            'a[href*="/posts/"] span',
            'a[href*="/permalink/"] span',
            'a.x1i10hfl.xjbqb8w',
            'span[id^="jsc"] a',
            'span.x4k7w5x a',
            'span.x1yrsyyn a'
        ],
        // محددات التفاعلات
        reactions: [
            'span.x193iq5w',
            'div.x1n2onr6.x1vqgdyp',
            'div[role="button"][tabindex="0"]',
            'span[role="button"]',
            'div.x1i10hfl[role="button"]',
            'div.x78zum5 span.x1e558r4'
        ],
        // محددات الوسائط
        media: [
            'img.x1ey2m1c',
            'img.xds687c',
            'img.x5yr21d',
            'video',
            'div.x1qjc9v5 img',
            'div.x1n2onr6 img'
        ],
        // محددات روابط المنشورات
        postLink: [
            'a[href*="/posts/"]',
            'a[href*="/permalink/"]',
            'a[href*="story_fbid="]',
            'a[href*="fbid="]',
            'a[role="link"][href*="/posts/"]',
            'a[role="link"][href*="/permalink/"]',
            'span.x4k7w5x a[href*="/posts/"]',
            'span.x1yrsyyn a[href*="/posts/"]'
        ],
        // محددات التعليقات
        comments: [
            'div[aria-label*="comment"]',
            'div[data-testid="UFI2CommentsList"]',
            'ul.x1qjc9v5',
            'div.x1n2onr6 div.x1r8uery',
            'div.x78zum5 div.x1r8uery'
        ],
        // محددات المشاركات
        shares: [
            'a[href*="shares"]',
            'a[role="link"][href*="shares"]',
            'span[role="button"]:contains("Share")',
            'div[role="button"]:has(span:contains("Share"))',
            'div[role="button"]:has(span:contains("share"))'
        ],
        // محددات أزرار التعليقات (متوافقة مع الإصدار السابق)
        commentButtons: [
            'div[aria-label*="comment"]',
            'div[role="button"]:has(span:contains("Comment"))',
            'div[role="button"]:has(span:contains("comment"))',
            'a[href*="comment"]',
            'span[role="button"]:not([aria-label=""]):nth-of-type(1)',
            'div.x1i10hfl[role="button"]:has(span:contains("Comment"))',
            'div[data-visualcompletion="ignore-dynamic"] div[role="button"]'
        ],
        commentInputFields: [
            'div[contenteditable="true"][role="textbox"]',
            'div[contenteditable="true"][aria-label*="comment"]',
            'div[contenteditable="true"][aria-label*="Comment"]',
            'div[contenteditable="true"][aria-label*="Write"]',
            'div[contenteditable="true"][aria-label*="write"]',
            'div[contenteditable="true"][aria-label*="Reply"]',
            'div[contenteditable="true"][aria-label*="reply"]',
            'div[role="textbox"]',
            'div.notranslate[role="textbox"]',
            'div.notranslate[contenteditable="true"]'
        ],
        commentSubmitButtons: [
            'div[aria-label="Comment"]',
            'div[aria-label="comment"]',
            'div[aria-label="Post"]',
            'div[aria-label="post"]',
            'div[aria-label="Reply"]',
            'div[aria-label="reply"]',
            'div[role="button"]:has(span:contains("Post"))',
            'div[role="button"]:has(span:contains("post"))',
            'div[role="button"]:has(span:contains("Reply"))',
            'div[role="button"]:has(span:contains("reply"))'
        ],
        commentContainers: [
            '.html-div.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x1gslohp',
            'div[role="article"] div.x1n2onr6',
            'div.x1n2onr6:has(div[contenteditable="true"])',
            'div.x78zum5:has(div[contenteditable="true"])',
            'div.x1n2onr6.x1ja2u2z',
            'div.x1n2onr6.x1iorvi4'
        ],
        replyButtons: [
            'div[role="button"]:has(span:contains("Reply"))',
            'div[role="button"]:has(span:contains("reply"))',
            'a[role="button"]:has(span:contains("Reply"))',
            'a[role="button"]:has(span:contains("reply"))',
            'span[role="button"]:contains("Reply")',
            'span[role="button"]:contains("reply")'
        ]
    };

    // Función para probar un selector CSS y verificar si encuentra elementos
    function testSelector(selector, context = document) {
        try {
            const elements = context.querySelectorAll(selector);
            return elements.length > 0 ? elements.length : 0;
        } catch (e) {
            console.error(`Error testing CSS selector ${selector}:`, e);
            return 0;
        }
    }

    // Función para probar un selector XPath y verificar si encuentra elementos
    function testXPathSelector(xpath, context = document) {
        try {
            // Reemplazar el marcador de posición {} con un valor genérico para pruebas
            const formattedXPath = xpath.replace('{}', '1');
            const result = document.evaluate(
                formattedXPath,
                context,
                null,
                XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
                null
            );
            return result.snapshotLength > 0 ? result.snapshotLength : 0;
        } catch (e) {
            console.error(`Error testing XPath selector ${xpath}:`, e);
            return 0;
        }
    }

    // Función para detectar selectores para un tipo específico
    function detectSelectors(type) {
        console.log(`Detecting ${type} selectors...`);
        const results = {};

        for (const selector of candidateSelectors[type]) {
            const count = testSelector(selector);
            results[selector] = count;

            if (count > 0) {
                console.log(`Found ${count} elements with selector: ${selector}`);
                if (!detectedSelectors.selectors[type].includes(selector)) {
                    detectedSelectors.selectors[type].push(selector);
                }
            }
        }

        detectedSelectors.stats[type] = results;
        return results;
    }

    // Función para detectar todos los selectores
    function detectAllSelectors() {
        for (const type in candidateSelectors) {
            detectSelectors(type);
        }

        // Detectar selectores XPath
        detectXPathSelectors();

        return detectedSelectors;
    }

    // Función para detectar selectores XPath
    function detectXPathSelectors() {
        console.log('Detecting XPath selectors...');

        // Probar selectores XPath para botones de respuesta
        for (const xpath of detectedSelectors.selectors.xpathSelectors.reply_button_xpaths) {
            const count = testXPathSelector(xpath);
            if (count > 0) {
                console.log(`Found ${count} elements with XPath: ${xpath}`);
            }
        }

        // Probar selectores XPath para comentarios completos
        for (const xpath of detectedSelectors.selectors.xpathSelectors.comment_full_xpaths) {
            const count = testXPathSelector(xpath);
            if (count > 0) {
                console.log(`Found ${count} elements with XPath: ${xpath}`);
            }
        }
    }

    // Función para probar los selectores en un post específico
    function testSelectorsOnPost(post) {
        const postResults = {
            commentButtons: {},
            commentInputFields: {},
            commentSubmitButtons: {},
            commentContainers: {},
            replyButtons: {}
        };

        // Probar selectores de botones de comentarios en este post
        for (const selector of candidateSelectors.commentButtons) {
            const count = testSelector(selector, post);
            postResults.commentButtons[selector] = count;
        }

        // Intentar hacer clic en un botón de comentarios para mostrar el campo de entrada
        const commentButtonSelectors = detectedSelectors.selectors.commentButtons;
        let commentButtonClicked = false;

        for (const selector of commentButtonSelectors) {
            if (commentButtonClicked) break;

            const buttons = post.querySelectorAll(selector);
            for (const button of buttons) {
                try {
                    // Verificar si el botón está visible
                    const rect = button.getBoundingClientRect();
                    if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
                        button.click();
                        console.log(`Clicked comment button with selector: ${selector}`);
                        commentButtonClicked = true;
                        break;
                    }
                } catch (e) {
                    console.error(`Error clicking comment button with selector ${selector}:`, e);
                }
            }
        }

        // Esperar un momento para que aparezca el campo de entrada
        setTimeout(() => {
            // Probar selectores de campos de entrada de comentarios
            for (const selector of candidateSelectors.commentInputFields) {
                const count = testSelector(selector, post);
                postResults.commentInputFields[selector] = count;
            }

            // Probar selectores de botones de envío de comentarios
            for (const selector of candidateSelectors.commentSubmitButtons) {
                const count = testSelector(selector, post);
                postResults.commentSubmitButtons[selector] = count;
            }

            // Probar selectores de contenedores de comentarios
            for (const selector of candidateSelectors.commentContainers) {
                const count = testSelector(selector, post);
                postResults.commentContainers[selector] = count;
            }

            // Probar selectores de botones de respuesta
            for (const selector of candidateSelectors.replyButtons) {
                const count = testSelector(selector, post);
                postResults.replyButtons[selector] = count;
            }

            // Actualizar estadísticas globales
            for (const type in postResults) {
                for (const selector in postResults[type]) {
                    if (postResults[type][selector] > 0) {
                        if (!detectedSelectors.selectors[type].includes(selector)) {
                            detectedSelectors.selectors[type].push(selector);
                        }
                    }
                }
            }

            console.log('Post selector testing completed:', postResults);
        }, 1000);

        return postResults;
    }

    // Función para probar selectores en todos los posts visibles
    function testSelectorsOnAllPosts() {
        const posts = document.querySelectorAll('div[role="article"]');
        console.log(`Testing selectors on ${posts.length} visible posts...`);

        for (let i = 0; i < Math.min(posts.length, 5); i++) { // Limitar a 5 posts para evitar demasiados clics
            testSelectorsOnPost(posts[i]);
        }
    }

    // Función para guardar los selectores detectados
    function saveDetectedSelectors() {
        // Esta función será implementada en el controlador Python
        // Aquí solo devolvemos los selectores detectados
        return JSON.stringify(detectedSelectors, null, 2);
    }

    // Función para agregar nuevos selectores candidatos
    function addCandidateSelectors(type, newSelectors) {
        if (Array.isArray(newSelectors)) {
            for (const selector of newSelectors) {
                if (!candidateSelectors[type].includes(selector)) {
                    candidateSelectors[type].push(selector);
                }
            }
        }
    }

    // Función para probar un selector específico
    function testSpecificSelector(type, selector) {
        const count = testSelector(selector);
        console.log(`Tested selector "${selector}" for ${type}: found ${count} elements`);

        if (count > 0 && !detectedSelectors.selectors[type].includes(selector)) {
            detectedSelectors.selectors[type].push(selector);
        }

        return count;
    }

    // Función para generar selectores dinámicos basados en la estructura actual
    function generateDynamicSelectors() {
        const dynamicSelectors = {
            commentButtons: [],
            commentInputFields: [],
            commentSubmitButtons: [],
            commentContainers: [],
            replyButtons: []
        };

        // Buscar elementos por texto y generar selectores
        const allElements = document.querySelectorAll('*');
        for (const element of allElements) {
            const text = element.textContent.toLowerCase();

            // Detectar botones de comentarios
            if ((text.includes('comment') || text.includes('reply')) &&
                (element.tagName === 'DIV' || element.tagName === 'SPAN' || element.tagName === 'A') &&
                (element.getAttribute('role') === 'button' || element.parentElement.getAttribute('role') === 'button')) {

                // Generar selector basado en atributos
                let selector = element.tagName.toLowerCase();
                if (element.getAttribute('role')) {
                    selector += `[role="${element.getAttribute('role')}"]`;
                }
                if (element.getAttribute('aria-label')) {
                    selector += `[aria-label*="${element.getAttribute('aria-label')}"]`;
                }
                if (element.classList.length > 0) {
                    // Usar solo las primeras 3 clases para evitar selectores demasiado específicos
                    const classes = Array.from(element.classList).slice(0, 3);
                    selector += `.${classes.join('.')}`;
                }

                dynamicSelectors.commentButtons.push(selector);
            }

            // Detectar campos de entrada de comentarios
            if (element.getAttribute('contenteditable') === 'true' || element.getAttribute('role') === 'textbox') {
                let selector = element.tagName.toLowerCase();
                if (element.getAttribute('contenteditable')) {
                    selector += `[contenteditable="${element.getAttribute('contenteditable')}"]`;
                }
                if (element.getAttribute('role')) {
                    selector += `[role="${element.getAttribute('role')}"]`;
                }
                if (element.getAttribute('aria-label')) {
                    selector += `[aria-label*="${element.getAttribute('aria-label')}"]`;
                }

                dynamicSelectors.commentInputFields.push(selector);
            }
        }

        // Agregar los selectores dinámicos a los candidatos
        for (const type in dynamicSelectors) {
            addCandidateSelectors(type, dynamicSelectors[type]);
        }

        return dynamicSelectors;
    }

    // Exponer funciones públicas
    return {
        detectAllSelectors,
        testSelectorsOnAllPosts,
        saveDetectedSelectors,
        addCandidateSelectors,
        testSpecificSelector,
        generateDynamicSelectors
    };
}

// Inicializar el detector de selectores y asignarlo al objeto window para acceso global
window.facebookSelectorDetector = initFacebookSelectorDetector();
