from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLabel, QPushButton, QTextEdit, QProgressBar)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class DashboardTab(QWidget):
    """Dashboard tab showing system status and statistics."""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
        # Setup refresh timer (update every 5 seconds)
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_status)
        self.refresh_timer.start(5000)
    
    def setup_ui(self):
        """Create and configure the UI components."""
        layout = QVBoxLayout(self)
        
        # Status section
        self.status_group = QGroupBox("System Status")
        status_layout = QHBoxLayout()
        
        # Left side - status indicators
        status_left = QVBoxLayout()
        self.browser_status = QLabel("Chrome Browser: Not Connected")
        self.profiles_status = QLabel("Profiles Loaded: 0")
        self.posts_status = QLabel("Posts Loaded: 0")
        self.database_status = QLabel("Comments Database: Not Connected")
        
        status_left.addWidget(self.browser_status)
        status_left.addWidget(self.profiles_status)
        status_left.addWidget(self.posts_status)
        status_left.addWidget(self.database_status)
        
        # Right side - quick actions
        status_right = QVBoxLayout()
        self.btn_start_cycle = QPushButton("Start Cycle")
        self.btn_stop_all = QPushButton("Stop All Processes")
        self.btn_check_browser = QPushButton("Check Browser")
        self.btn_reload_data = QPushButton("Reload Data")
        
        self.btn_start_cycle.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        self.btn_stop_all.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        
        status_right.addWidget(self.btn_start_cycle)
        status_right.addWidget(self.btn_stop_all)
        status_right.addWidget(self.btn_check_browser)
        status_right.addWidget(self.btn_reload_data)
        
        status_layout.addLayout(status_left, 2)
        status_layout.addLayout(status_right, 1)
        self.status_group.setLayout(status_layout)
        
        # Statistics section
        self.stats_group = QGroupBox("Statistics")
        stats_layout = QHBoxLayout()
        
        # Left side - statistics
        stats_left = QVBoxLayout()
        self.comments_posted = QLabel("Comments Posted: 0")
        self.posts_scraped = QLabel("Posts Scraped: 0")
        self.members_scraped = QLabel("Members Scraped: 0")
        self.active_accounts = QLabel("Active Accounts: 0/0")
        
        stats_left.addWidget(self.comments_posted)
        stats_left.addWidget(self.posts_scraped)
        stats_left.addWidget(self.members_scraped)
        stats_left.addWidget(self.active_accounts)
        
        # Right side - activity chart placeholder
        stats_right = QVBoxLayout()
        self.chart_placeholder = QLabel("Activity Chart (Last 7 Days)")
        self.chart_placeholder.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;")
        self.chart_placeholder.setMinimumHeight(150)
        self.chart_placeholder.setAlignment(Qt.AlignCenter)
        stats_right.addWidget(self.chart_placeholder)
        
        stats_layout.addLayout(stats_left, 1)
        stats_layout.addLayout(stats_right, 2)
        self.stats_group.setLayout(stats_layout)
        
        # Current tasks section
        self.tasks_group = QGroupBox("Current Tasks")
        tasks_layout = QVBoxLayout()
        
        # Task progress bars
        self.spammer_progress = self.create_task_progress("Spammer")
        self.checker_progress = self.create_task_progress("Checker")
        self.post_scraper_progress = self.create_task_progress("Post Scraper")
        self.logginer_progress = self.create_task_progress("Logginer")
        self.member_scraper_progress = self.create_task_progress("Member Scraper")
        
        tasks_layout.addLayout(self.spammer_progress)
        tasks_layout.addLayout(self.checker_progress)
        tasks_layout.addLayout(self.post_scraper_progress)
        tasks_layout.addLayout(self.logginer_progress)
        tasks_layout.addLayout(self.member_scraper_progress)
        
        self.tasks_group.setLayout(tasks_layout)
        
        # Console output
        self.console_group = QGroupBox("Console Output")
        console_layout = QVBoxLayout()
        self.console = QTextEdit()
        self.console.setReadOnly(True)
        self.console.setStyleSheet("background-color: #000; color: #0f0; font-family: Consolas, monospace;")
        self.console.setText("[INFO] Zpammer GUI started\n[INFO] System ready\n[INFO] Waiting for commands...")
        console_layout.addWidget(self.console)
        self.console_group.setLayout(console_layout)
        
        # Add all sections to the main layout
        layout.addWidget(self.status_group)
        layout.addWidget(self.stats_group)
        layout.addWidget(self.tasks_group)
        layout.addWidget(self.console_group)
        
        # Connect signals
        self.btn_start_cycle.clicked.connect(self.on_start_cycle)
        self.btn_stop_all.clicked.connect(self.on_stop_all)
        self.btn_check_browser.clicked.connect(self.on_check_browser)
        self.btn_reload_data.clicked.connect(self.on_reload_data)
    
    def create_task_progress(self, task_name):
        """Create a progress bar layout for a task."""
        layout = QHBoxLayout()
        label = QLabel(f"{task_name}:")
        progress = QProgressBar()
        progress.setValue(0)
        status = QLabel("Idle")
        
        layout.addWidget(label, 1)
        layout.addWidget(progress, 3)
        layout.addWidget(status, 1)
        
        # Store references to widgets
        setattr(self, f"{task_name.lower().replace(' ', '_')}_label", label)
        setattr(self, f"{task_name.lower().replace(' ', '_')}_progress", progress)
        setattr(self, f"{task_name.lower().replace(' ', '_')}_status", status)
        
        return layout
    
    def refresh_status(self):
        """Refresh status information."""
        # This would be connected to the actual backend in a real implementation
        # For now, we'll just add a log message to show it's working
        self.log_message("[INFO] Dashboard refreshed")
    
    def log_message(self, message):
        """Add a message to the console."""
        self.console.append(message)
        # Auto-scroll to bottom
        self.console.verticalScrollBar().setValue(self.console.verticalScrollBar().maximum())
    
    def on_start_cycle(self):
        """Handle start cycle button click."""
        self.log_message("[INFO] Starting cycle...")
        # This would trigger the actual cycle in the backend
    
    def on_stop_all(self):
        """Handle stop all button click."""
        self.log_message("[WARNING] Stopping all processes...")
        # This would stop all processes in the backend
    
    def on_check_browser(self):
        """Handle check browser button click."""
        self.log_message("[INFO] Checking browser connection...")
        # This would check the browser connection in the backend
    
    def on_reload_data(self):
        """Handle reload data button click."""
        self.log_message("[INFO] Reloading data...")
        # This would reload data from files in the backend
