import os
import subprocess
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QComboBox, QCheckBox, QFileDialog,
                            QTabWidget, QScrollArea, QMessageBox, QLineEdit)
from PyQt5.QtCore import Qt

from core.utils.database import Database
from core.utils.config import Config

class SettingsTab(QWidget):
    """Settings tab for configuring the application."""

    def __init__(self):
        super().__init__()
        self.config = Config()
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """Create and configure the UI components."""
        main_layout = QVBoxLayout(self)

        # Create a tab widget for settings categories
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)

        # Create tabs for different settings categories
        self.general_tab = self.create_general_tab()
        self.browser_tab = self.create_browser_tab()
        self.nstbrowser_tab = self.create_nstbrowser_tab()
        self.spammer_tab = self.create_spammer_tab()
        self.checker_tab = self.create_checker_tab()

        # Add tabs to tab widget
        self.tabs.addTab(self.general_tab, "General")
        self.tabs.addTab(self.browser_tab, "Browser")
        self.tabs.addTab(self.nstbrowser_tab, "NstBrowser")
        self.tabs.addTab(self.spammer_tab, "Spammer")
        self.tabs.addTab(self.checker_tab, "Checker")

        # Add buttons at the bottom
        buttons_layout = QHBoxLayout()

        self.btn_save = QPushButton("Save Settings")
        self.btn_save.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")

        self.btn_reset = QPushButton("Reset to Defaults")
        self.btn_reset.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")

        self.btn_reset_all = QPushButton("Reset All Data")
        self.btn_reset_all.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")

        buttons_layout.addWidget(self.btn_save)
        buttons_layout.addWidget(self.btn_reset)
        buttons_layout.addWidget(self.btn_reset_all)

        main_layout.addLayout(buttons_layout)

        # Connect signals
        self.connect_signals()

    def create_general_tab(self):
        """Create the general settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # General settings group
        general_group = QGroupBox("General Settings")
        general_layout = QVBoxLayout()

        # Theme selection
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("Theme:"))
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Light", "Dark", "System"])
        theme_layout.addWidget(self.theme_combo)
        general_layout.addLayout(theme_layout)

        # Language selection
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("Language:"))
        self.lang_combo = QComboBox()
        self.lang_combo.addItems(["English", "Arabic", "French"])
        lang_layout.addWidget(self.lang_combo)
        general_layout.addLayout(lang_layout)

        # Log level
        log_layout = QHBoxLayout()
        log_layout.addWidget(QLabel("Log Level:"))
        self.log_combo = QComboBox()
        self.log_combo.addItems(["Debug", "Info", "Warning", "Error"])
        log_layout.addWidget(self.log_combo)
        general_layout.addLayout(log_layout)

        # Other options
        self.start_with_windows = QCheckBox("Start with Windows")
        self.minimize_to_tray = QCheckBox("Minimize to tray")

        general_layout.addWidget(self.start_with_windows)
        general_layout.addWidget(self.minimize_to_tray)

        general_group.setLayout(general_layout)
        layout.addWidget(general_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        return tab

    def create_browser_tab(self):
        """Create the browser settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Browser settings group
        browser_group = QGroupBox("Browser Settings")
        browser_layout = QVBoxLayout()

        # Chrome path
        chrome_layout = QHBoxLayout()
        chrome_layout.addWidget(QLabel("Chrome Path:"))
        self.chrome_path_label = QLabel("C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe")
        chrome_layout.addWidget(self.chrome_path_label, 1)
        self.btn_browse_chrome = QPushButton("Browse")
        chrome_layout.addWidget(self.btn_browse_chrome)
        browser_layout.addLayout(chrome_layout)

        # Chrome user data path
        user_data_layout = QHBoxLayout()
        user_data_layout.addWidget(QLabel("User Data Path:"))
        self.user_data_path_label = QLabel("C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data")
        user_data_layout.addWidget(self.user_data_path_label, 1)
        self.btn_browse_user_data = QPushButton("Browse")
        user_data_layout.addWidget(self.btn_browse_user_data)
        browser_layout.addLayout(user_data_layout)

        # Chromedriver path
        chromedriver_layout = QHBoxLayout()
        chromedriver_layout.addWidget(QLabel("ChromeDriver Path:"))
        self.chromedriver_path_label = QLabel("C:\\Users\\<USER>\\Desktop\\Zpammer\\chromedriver\\chromedriver.exe")
        chromedriver_layout.addWidget(self.chromedriver_path_label, 1)
        self.btn_browse_chromedriver = QPushButton("Browse")
        chromedriver_layout.addWidget(self.btn_browse_chromedriver)
        browser_layout.addLayout(chromedriver_layout)

        # Other options
        self.headless_mode = QCheckBox("Use headless mode by default")
        self.auto_close = QCheckBox("Auto-close browser after task")

        browser_layout.addWidget(self.headless_mode)
        browser_layout.addWidget(self.auto_close)

        browser_group.setLayout(browser_layout)
        layout.addWidget(browser_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        return tab

    def create_nstbrowser_tab(self):
        """Create the NstBrowser settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # NstBrowser settings group
        nstbrowser_group = QGroupBox("NstBrowser Settings")
        nstbrowser_layout = QVBoxLayout()

        # NstBrowser executable path
        executable_layout = QHBoxLayout()
        executable_layout.addWidget(QLabel("NstBrowser Executable:"))
        self.nstbrowser_path_label = QLabel("")
        executable_layout.addWidget(self.nstbrowser_path_label, 1)
        self.btn_browse_nstbrowser = QPushButton("Browse")
        executable_layout.addWidget(self.btn_browse_nstbrowser)
        nstbrowser_layout.addLayout(executable_layout)

        # NstBrowser profiles path
        profiles_layout = QHBoxLayout()
        profiles_layout.addWidget(QLabel("Profiles Directory:"))
        self.nstbrowser_profiles_label = QLabel("")
        profiles_layout.addWidget(self.nstbrowser_profiles_label, 1)
        self.btn_browse_nstbrowser_profiles = QPushButton("Browse")
        profiles_layout.addWidget(self.btn_browse_nstbrowser_profiles)
        nstbrowser_layout.addLayout(profiles_layout)

        # API settings
        api_group = QGroupBox("API Settings")
        api_layout = QVBoxLayout()

        # API Key
        api_key_layout = QHBoxLayout()
        api_key_layout.addWidget(QLabel("API Key:"))
        self.nstbrowser_api_key = QLineEdit()
        self.nstbrowser_api_key.setPlaceholderText("Enter your NstBrowser API key")
        api_key_layout.addWidget(self.nstbrowser_api_key)
        api_layout.addLayout(api_key_layout)

        # Host and Port
        host_port_layout = QHBoxLayout()
        host_port_layout.addWidget(QLabel("Host:"))
        self.nstbrowser_host = QLineEdit("127.0.0.1")
        host_port_layout.addWidget(self.nstbrowser_host)
        host_port_layout.addWidget(QLabel("Port:"))
        self.nstbrowser_port = QLineEdit("8848")
        host_port_layout.addWidget(self.nstbrowser_port)
        api_layout.addLayout(host_port_layout)

        api_group.setLayout(api_layout)
        nstbrowser_layout.addWidget(api_group)

        # Other options
        self.nstbrowser_auto_close = QCheckBox("Auto-close browser after task")
        self.nstbrowser_auto_close.setToolTip("When enabled, NstBrowser will be closed automatically when the application exits.")

        nstbrowser_layout.addWidget(self.nstbrowser_auto_close)

        # Launch NstBrowser button
        launch_layout = QHBoxLayout()
        self.btn_launch_nstbrowser = QPushButton("Launch NstBrowser")
        self.btn_launch_nstbrowser.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        launch_layout.addStretch()
        launch_layout.addWidget(self.btn_launch_nstbrowser)
        nstbrowser_layout.addLayout(launch_layout)

        nstbrowser_group.setLayout(nstbrowser_layout)
        layout.addWidget(nstbrowser_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        return tab

    # Removed create_paths_tab method as it is no longer needed

    def create_spammer_tab(self):
        """Create the spammer settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Note about Spammer settings
        note_label = QLabel("Spammer settings have been moved to the Spammer tab to avoid duplication.")
        note_label.setWordWrap(True)
        note_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(note_label)

        # Add stretch to push everything to the top
        layout.addStretch()

        return tab

    def create_checker_tab(self):
        """Create the checker settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # Checker settings group
        checker_group = QGroupBox("Checker Settings")
        checker_layout = QVBoxLayout()

        # Check interval
        check_interval_layout = QHBoxLayout()
        check_interval_layout.addWidget(QLabel("Check interval (hours):"))
        self.check_interval_combo = QComboBox()
        self.check_interval_combo.addItems(["0.1", "0.5", "1", "2", "6", "12", "24"])
        self.check_interval_combo.setCurrentText("1")
        check_interval_layout.addWidget(self.check_interval_combo)
        checker_layout.addLayout(check_interval_layout)

        # Other options
        self.auto_ban = QCheckBox("Automatically add banned accounts to list")
        self.auto_update = QCheckBox("Automatically update account status")

        self.auto_ban.setChecked(True)
        self.auto_update.setChecked(True)

        checker_layout.addWidget(self.auto_ban)
        checker_layout.addWidget(self.auto_update)

        checker_group.setLayout(checker_layout)
        layout.addWidget(checker_group)

        # Add stretch to push everything to the top
        layout.addStretch()

        return tab

    def connect_signals(self):
        """Connect signals to slots."""
        # Button signals
        self.btn_save.clicked.connect(self.save_settings)
        self.btn_reset.clicked.connect(self.reset_settings)
        self.btn_reset_all.clicked.connect(self.reset_all_data)

        # Browser tab buttons
        self.btn_browse_chrome.clicked.connect(self.browse_chrome_path)
        self.btn_browse_user_data.clicked.connect(self.browse_user_data_path)
        self.btn_browse_chromedriver.clicked.connect(self.browse_chromedriver_path)

        # NstBrowser tab buttons
        self.btn_browse_nstbrowser.clicked.connect(self.browse_nstbrowser_executable)
        self.btn_browse_nstbrowser_profiles.clicked.connect(self.browse_nstbrowser_profiles)
        self.btn_launch_nstbrowser.clicked.connect(self.launch_nstbrowser)

    def browse_chrome_path(self):
        """Browse for Chrome executable."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Chrome Executable", "", "Executable Files (*.exe);;All Files (*)"
        )
        if file_path:
            self.chrome_path_label.setText(file_path)

    def browse_user_data_path(self):
        """Browse for Chrome user data directory."""
        dir_path = QFileDialog.getExistingDirectory(
            self, "Select Chrome User Data Directory"
        )
        if dir_path:
            self.user_data_path_label.setText(dir_path)

    def browse_chromedriver_path(self):
        """Browse for ChromeDriver executable."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select ChromeDriver Executable", "", "Executable Files (*.exe);;All Files (*)"
        )
        if file_path:
            self.chromedriver_path_label.setText(file_path)

    # Removed browse_file_path method as it is no longer needed

    def save_settings(self):
        """Save settings."""
        # Save NstBrowser settings
        if not "nstbrowser" in self.config.config:
            self.config.config["nstbrowser"] = {}

        self.config.config["nstbrowser"]["executable_path"] = self.nstbrowser_path_label.text()
        self.config.config["nstbrowser"]["profiles_path"] = self.nstbrowser_profiles_label.text()
        self.config.config["nstbrowser"]["api_key"] = self.nstbrowser_api_key.text()
        self.config.config["nstbrowser"]["host"] = self.nstbrowser_host.text()

        try:
            port = int(self.nstbrowser_port.text() or "8848")
            self.config.config["nstbrowser"]["port"] = port
        except ValueError:
            self.config.config["nstbrowser"]["port"] = 8848

        # Set headless_mode from browser settings
        self.config.config["nstbrowser"]["headless_mode"] = self.headless_mode.isChecked()
        self.config.config["nstbrowser"]["auto_close"] = self.nstbrowser_auto_close.isChecked()

        # Save browser settings
        if not "browser" in self.config.config:
            self.config.config["browser"] = {}

        self.config.config["browser"]["chrome_path"] = self.chrome_path_label.text()
        self.config.config["browser"]["user_data_path"] = self.user_data_path_label.text()
        self.config.config["browser"]["chromedriver_path"] = self.chromedriver_path_label.text()
        self.config.config["browser"]["headless_mode"] = self.headless_mode.isChecked()
        self.config.config["browser"]["auto_close"] = self.auto_close.isChecked()

        # Save configuration to file
        self.config.save()

        QMessageBox.information(self, "Settings Saved", "Settings have been saved successfully.")

    def reset_settings(self):
        """Reset settings to defaults."""
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Are you sure you want to reset all settings to defaults?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Here you would actually reset the settings to defaults
            QMessageBox.information(self, "Settings Reset", "Settings have been reset to defaults.")

    def reset_all_data(self):
        """Reset all data in the database."""
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Are you sure you want to reset ALL DATA in the database? This action cannot be undone!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Double confirmation for dangerous action
            reply = QMessageBox.warning(
                self,
                "WARNING: Data Loss",
                "This will DELETE ALL DATA including profiles, posts, comments, accounts, etc. This action CANNOT be undone! Are you ABSOLUTELY sure?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Reset the database
                db = Database()
                success = db.reset_database()

                if success:
                    QMessageBox.information(self, "Database Reset", "All data has been successfully reset.")
                else:
                    QMessageBox.critical(self, "Error", "Failed to reset the database. Please check the logs for details.")

    def browse_nstbrowser_executable(self):
        """Browse for NstBrowser executable."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select NstBrowser Executable", "", "Executable Files (*.exe);;All Files (*)"
        )
        if file_path:
            self.nstbrowser_path_label.setText(file_path)

    def browse_nstbrowser_profiles(self):
        """Browse for NstBrowser profiles directory."""
        dir_path = QFileDialog.getExistingDirectory(
            self, "Select NstBrowser Profiles Directory", ""
        )
        if dir_path:
            self.nstbrowser_profiles_label.setText(dir_path)

    def load_settings(self):
        """Load settings from configuration file."""
        try:
            # Load NstBrowser settings
            if "nstbrowser" in self.config.config:
                nstbrowser_config = self.config.config["nstbrowser"]

                # Set NstBrowser executable path
                if "executable_path" in nstbrowser_config:
                    self.nstbrowser_path_label.setText(nstbrowser_config["executable_path"])

                # Set NstBrowser profiles path
                if "profiles_path" in nstbrowser_config:
                    self.nstbrowser_profiles_label.setText(nstbrowser_config["profiles_path"])

                # Set API key
                if "api_key" in nstbrowser_config:
                    self.nstbrowser_api_key.setText(nstbrowser_config["api_key"])

                # Set host
                if "host" in nstbrowser_config:
                    self.nstbrowser_host.setText(nstbrowser_config["host"])

                # Set port
                if "port" in nstbrowser_config:
                    self.nstbrowser_port.setText(str(nstbrowser_config["port"]))

                # Set checkboxes
                if "auto_close" in nstbrowser_config:
                    self.nstbrowser_auto_close.setChecked(nstbrowser_config["auto_close"])

            # Load browser settings
            if "browser" in self.config.config:
                browser_config = self.config.config["browser"]

                # Set Chrome path
                if "chrome_path" in browser_config:
                    self.chrome_path_label.setText(browser_config["chrome_path"])

                # Set user data path
                if "user_data_path" in browser_config:
                    self.user_data_path_label.setText(browser_config["user_data_path"])

                # Set ChromeDriver path
                if "chromedriver_path" in browser_config:
                    self.chromedriver_path_label.setText(browser_config["chromedriver_path"])

                # Set checkboxes
                if "headless_mode" in browser_config:
                    self.headless_mode.setChecked(browser_config["headless_mode"])

                if "auto_close" in browser_config:
                    self.auto_close.setChecked(browser_config["auto_close"])
        except Exception as e:
            QMessageBox.warning(self, "Settings Load Error", f"Failed to load settings: {str(e)}")

    def launch_nstbrowser(self):
        """Launch NstBrowser executable."""
        executable_path = self.nstbrowser_path_label.text()
        if not executable_path:
            QMessageBox.warning(self, "NstBrowser Not Configured", "Please set the NstBrowser executable path first.")
            return

        if not os.path.exists(executable_path):
            QMessageBox.critical(self, "File Not Found", f"NstBrowser executable not found at:\n{executable_path}")
            return

        try:
            # Prepare command arguments
            cmd_args = [executable_path, "--no-update"]

            # Add arguments for system tray mode
            cmd_args.extend(["--start-minimized", "--minimize-to-tray"])

            # Launch NstBrowser
            if os.name == 'nt':  # Windows
                subprocess.Popen(cmd_args, creationflags=subprocess.CREATE_NO_WINDOW)
            else:  # Linux/macOS
                import subprocess as sp  # Import with alias to avoid IDE confusion
                sp.Popen(cmd_args, stdout=sp.DEVNULL, stderr=sp.DEVNULL)

            QMessageBox.information(self, "NstBrowser Launched", "NstBrowser has been launched successfully.")
        except Exception as e:
            QMessageBox.critical(self, "Launch Error", f"Failed to launch NstBrowser:\n{str(e)}")
