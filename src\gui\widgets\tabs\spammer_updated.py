import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTextEdit, QListWidget, QSpinBox,
                            QProgressBar, QSplitter, QMessageBox, QComboBox,
                            QInputDialog, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSlot

# Import custom widgets
from ..custom.toggle_switch import ToggleSwitch

from .spammer_controller import SpammerController

# Import dialogs
from ...dialogs.bulk_editor import BulkEditorDialog

class SpammerTab(QWidget):
    """Spammer tab for managing comment spamming operations."""

    def __init__(self):
        super().__init__()

        # Create controller
        self.controller = SpammerController()

        # Connect controller signals
        self.controller.log_message.connect(self.log_message)
        self.controller.progress_updated.connect(self.on_progress_updated)
        self.controller.status_changed.connect(self.on_status_changed)

        # Setup UI
        self.setup_ui()

        # Load initial data
        self.load_data()

        # Set hand cursor for all buttons
        self.set_hand_cursor_for_buttons()

    def setup_ui(self):
        """Create and configure the UI components."""
        main_layout = QHBoxLayout(self)

        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - configuration
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Profiles section
        self.profiles_group = QGroupBox("Profiles (0)")
        profiles_layout = QVBoxLayout()
        self.profiles_list = QListWidget()

        profiles_buttons = QHBoxLayout()
        self.btn_import_profiles = QPushButton("Import File")
        self.btn_add_profile = QPushButton("Add New")
        self.btn_bulk_profiles = QPushButton("Edit Profiles")
        self.btn_bulk_profiles.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")
        self.btn_import_from_checker = QPushButton("Import from Checker")
        self.btn_import_from_checker.setStyleSheet("background-color: #4CAF50; color: white;")

        profiles_buttons.addWidget(self.btn_import_profiles)
        profiles_buttons.addWidget(self.btn_add_profile)
        profiles_buttons.addWidget(self.btn_bulk_profiles)
        profiles_buttons.addWidget(self.btn_import_from_checker)

        profiles_layout.addWidget(self.profiles_list)
        profiles_layout.addLayout(profiles_buttons)
        self.profiles_group.setLayout(profiles_layout)

        # Posts section
        self.posts_group = QGroupBox("Posts (0)")
        posts_layout = QVBoxLayout()
        self.posts_list = QListWidget()
        self.posts_list.setSelectionMode(QListWidget.ExtendedSelection)  # Allow multiple selection

        posts_buttons = QHBoxLayout()
        self.btn_import_posts = QPushButton("Import File")
        self.btn_add_post = QPushButton("Add New")
        self.btn_bulk_posts = QPushButton("Edit Posts")
        self.btn_bulk_posts.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")
        self.btn_delete_selected_posts = QPushButton("Delete Selected")
        self.btn_delete_selected_posts.setStyleSheet("background-color: #F44336; color: white;")

        posts_buttons.addWidget(self.btn_import_posts)
        posts_buttons.addWidget(self.btn_add_post)
        posts_buttons.addWidget(self.btn_bulk_posts)
        posts_buttons.addWidget(self.btn_delete_selected_posts)

        posts_layout.addWidget(self.posts_list)
        posts_layout.addLayout(posts_buttons)
        self.posts_group.setLayout(posts_layout)

        # Comments section
        self.comments_group = QGroupBox("Comments (0)")
        comments_layout = QVBoxLayout()
        self.comments_list = QListWidget()

        comments_buttons = QHBoxLayout()
        self.btn_import_comments = QPushButton("Import File")
        self.btn_add_comment = QPushButton("Add New")
        self.btn_bulk_comments = QPushButton("Edit Comments")
        self.btn_bulk_comments.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")

        comments_buttons.addWidget(self.btn_import_comments)
        comments_buttons.addWidget(self.btn_add_comment)
        comments_buttons.addWidget(self.btn_bulk_comments)

        comments_layout.addWidget(self.comments_list)
        comments_layout.addLayout(comments_buttons)
        self.comments_group.setLayout(comments_layout)

        # Add groups to left layout
        left_layout.addWidget(self.profiles_group)
        left_layout.addWidget(self.posts_group)
        left_layout.addWidget(self.comments_group)

        # Right panel - controls and output
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Settings section
        self.settings_group = QGroupBox("Spammer Settings")
        settings_layout = QVBoxLayout()

        # This section has been replaced by spam_count_spin below

        # Sleep Range Controls (from original Spammer.py)
        sleep_range_group = QGroupBox("Sleep Range Settings")
        sleep_range_layout = QVBoxLayout()

        # Min sleep time
        min_sleep_layout = QHBoxLayout()
        min_sleep_layout.addWidget(QLabel("Minimum sleep time (seconds):"))
        self.min_sleep_spin = QSpinBox()
        self.min_sleep_spin.setValue(1)
        self.min_sleep_spin.setRange(1, 10)
        self.min_sleep_spin.setToolTip("Minimum time to sleep between actions (in seconds)")
        min_sleep_layout.addWidget(self.min_sleep_spin)
        sleep_range_layout.addLayout(min_sleep_layout)

        # Max sleep time
        max_sleep_layout = QHBoxLayout()
        max_sleep_layout.addWidget(QLabel("Maximum sleep time (seconds):"))
        self.max_sleep_spin = QSpinBox()
        self.max_sleep_spin.setValue(4)
        self.max_sleep_spin.setRange(2, 15)
        self.max_sleep_spin.setToolTip("Maximum time to sleep between actions (in seconds)")
        max_sleep_layout.addWidget(self.max_sleep_spin)
        sleep_range_layout.addLayout(max_sleep_layout)

        # Add sleep range controls to the group
        sleep_range_group.setLayout(sleep_range_layout)

        # Add sleep range group to settings layout
        settings_layout.addWidget(sleep_range_group)

        # Spam Count Control (from original Spammer.py)
        spam_count_layout = QHBoxLayout()
        spam_count_layout.addWidget(QLabel("Comments per profile (spam count):"))
        self.spam_count_spin = QSpinBox()
        self.spam_count_spin.setValue(3)
        self.spam_count_spin.setRange(1, 100)
        self.spam_count_spin.setToolTip("Number of comments to post for each profile")
        spam_count_layout.addWidget(self.spam_count_spin)
        settings_layout.addLayout(spam_count_layout)

        # Selector Detection Time Control
        selector_detection_time_layout = QHBoxLayout()
        selector_detection_time_layout.addWidget(QLabel("Selector detection time per post (seconds):"))
        self.selector_detection_time_spin = QSpinBox()
        self.selector_detection_time_spin.setValue(30)  # Default to 30 seconds
        self.selector_detection_time_spin.setRange(10, 120)  # Allow 10-120 seconds
        self.selector_detection_time_spin.setToolTip("Time spent on each post during selector detection phase (longer time = better detection but slower start)")
        selector_detection_time_layout.addWidget(self.selector_detection_time_spin)
        settings_layout.addLayout(selector_detection_time_layout)

        # Toggle switches for options
        # Create a grid layout for toggle switches to make them more compact
        toggle_grid = QHBoxLayout()
        toggle_grid.setSpacing(10)  # Add some spacing between toggles

        # Create toggle switches container widgets
        toggle1_container = QWidget()
        toggle2_container = QWidget()
        toggle1_layout = QVBoxLayout(toggle1_container)
        toggle2_layout = QVBoxLayout(toggle2_container)
        toggle1_layout.setContentsMargins(0, 0, 0, 0)
        toggle2_layout.setContentsMargins(0, 0, 0, 0)

        # Create layouts for each option with label and toggle switch
        random_typing_layout = QHBoxLayout()
        skip_commented_layout = QHBoxLayout()
        typing_speed_layout = QHBoxLayout()
        headless_layout = QHBoxLayout()
        use_first_profile_for_selectors_layout = QHBoxLayout()
        use_first_profile_for_commenting_layout = QHBoxLayout()

        # Create labels based on original Spammer.py options with clearer descriptions
        execute_spam_comment_label = QLabel("Actually post comments")
        skip_commented_label = QLabel("Skip already commented posts")
        typing_speed_label = QLabel("Comment typing speed:")
        headless_label = QLabel("Headless mode (browser invisible)")
        use_first_profile_for_selectors_label = QLabel("Use first profile to detect selectors")
        use_first_profile_for_commenting_label = QLabel("Use first profile for commenting too")

        # Set cursor to pointing hand for labels
        execute_spam_comment_label.setCursor(Qt.PointingHandCursor)
        skip_commented_label.setCursor(Qt.PointingHandCursor)
        headless_label.setCursor(Qt.PointingHandCursor)
        use_first_profile_for_selectors_label.setCursor(Qt.PointingHandCursor)
        use_first_profile_for_commenting_label.setCursor(Qt.PointingHandCursor)

        # Make labels clickable to toggle the switches
        execute_spam_comment_label.mousePressEvent = lambda e: self.execute_spam_comment.toggle()
        skip_commented_label.mousePressEvent = lambda e: self.skip_commented.toggle()
        headless_label.mousePressEvent = lambda e: self.headless_mode.toggle()
        use_first_profile_for_selectors_label.mousePressEvent = lambda e: self.use_first_profile_for_selectors.toggle()
        use_first_profile_for_commenting_label.mousePressEvent = lambda e: self.use_first_profile_for_commenting.toggle()

        # Create toggle switches
        self.execute_spam_comment = ToggleSwitch()
        self.skip_commented = ToggleSwitch()
        self.headless_mode = ToggleSwitch()
        self.use_first_profile_for_selectors = ToggleSwitch()
        self.use_first_profile_for_commenting = ToggleSwitch()

        # Create typing speed combo box
        self.typing_speed_combo = QComboBox()
        self.typing_speed_combo.addItems(["Very Slow", "Slow", "Normal", "Fast", "Very Fast"])
        self.typing_speed_combo.setCurrentIndex(2)  # Default to Normal

        # Create post selection mode combo box
        self.post_selection_mode_combo = QComboBox()
        self.post_selection_mode_combo.addItems(["Sequential", "Random"])
        self.post_selection_mode_combo.setCurrentIndex(0)  # Default to Sequential

        # Auto close is always enabled but hidden from UI
        self.auto_close = True
        # Confirm comments is always disabled as requested
        self.confirm_comments = False

        # Set default states based on original Spammer.py
        self.execute_spam_comment.setChecked(True)  # Default to True as in original
        self.skip_commented.setChecked(True)       # Default to True
        self.headless_mode.setChecked(False)       # Default to False as requested
        self.use_first_profile_for_selectors.setChecked(True)  # Default to True
        self.use_first_profile_for_commenting.setChecked(True)  # Default to True

        # Add tooltips with clearer explanations
        self.execute_spam_comment.setToolTip("When enabled: The program will actually post comments.\nWhen disabled: The program will only simulate the process without posting any comments.")
        self.skip_commented.setToolTip("When enabled: Skip posts that have already been commented on.\nWhen disabled: Process all posts regardless of previous comments.")
        self.typing_speed_combo.setToolTip("Controls how fast the program types comments.")
        self.headless_mode.setToolTip("When enabled: Browser will run in background without visible window.\nWhen disabled: Browser will be visible during operation.")
        self.use_first_profile_for_selectors.setToolTip("When enabled: The first profile will be used to detect selectors for all posts before starting the commenting process.\nWhen disabled: Each profile will detect selectors for its own posts.")
        self.use_first_profile_for_commenting.setToolTip("When enabled: After using the first profile for selector detection, it will also be used for commenting.\nWhen disabled: The first profile will only be used for selector detection, not for commenting.")

        # Add widgets to layouts
        random_typing_layout.addWidget(execute_spam_comment_label)
        random_typing_layout.addStretch()
        random_typing_layout.addWidget(self.execute_spam_comment)

        skip_commented_layout.addWidget(skip_commented_label)
        skip_commented_layout.addStretch()
        skip_commented_layout.addWidget(self.skip_commented)

        use_first_profile_for_selectors_layout.addWidget(use_first_profile_for_selectors_label)
        use_first_profile_for_selectors_layout.addStretch()
        use_first_profile_for_selectors_layout.addWidget(self.use_first_profile_for_selectors)

        # Add typing speed control
        typing_speed_layout.addWidget(QLabel("Comment typing speed:"))
        typing_speed_layout.addStretch()
        typing_speed_layout.addWidget(self.typing_speed_combo)

        # Add post selection mode control
        post_selection_mode_layout = QHBoxLayout()
        post_selection_mode_layout.addWidget(QLabel("Post selection mode:"))
        post_selection_mode_layout.addStretch()
        post_selection_mode_layout.addWidget(self.post_selection_mode_combo)

        # Add max posts control
        self.max_posts_spin = QSpinBox()
        self.max_posts_spin.setRange(0, 1000)
        self.max_posts_spin.setValue(0)
        self.max_posts_spin.setToolTip("Maximum number of posts to process (0 = process all posts)")

        max_posts_layout = QHBoxLayout()
        max_posts_layout.addWidget(QLabel("Max posts to process (0 = all):"))
        max_posts_layout.addStretch()
        max_posts_layout.addWidget(self.max_posts_spin)

        # Auto close is always enabled and confirm comments is always disabled
        # These options are not shown in the UI as requested

        # Add headless mode layout
        headless_layout.addWidget(headless_label)
        headless_layout.addStretch()
        headless_layout.addWidget(self.headless_mode)

        use_first_profile_for_commenting_layout.addWidget(use_first_profile_for_commenting_label)
        use_first_profile_for_commenting_layout.addStretch()
        use_first_profile_for_commenting_layout.addWidget(self.use_first_profile_for_commenting)

        # Add layouts to container layouts - reorganized for better layout
        toggle1_layout.addLayout(random_typing_layout)
        toggle1_layout.addLayout(skip_commented_layout)
        toggle1_layout.addLayout(use_first_profile_for_selectors_layout)

        toggle2_layout.addLayout(typing_speed_layout)
        toggle2_layout.addLayout(post_selection_mode_layout)
        toggle2_layout.addLayout(max_posts_layout)
        toggle2_layout.addLayout(headless_layout)
        toggle2_layout.addLayout(use_first_profile_for_commenting_layout)

        # Add containers to grid
        toggle_grid.addWidget(toggle1_container)
        toggle_grid.addWidget(toggle2_container)

        # Add grid to settings layout
        settings_layout.addLayout(toggle_grid)

        self.settings_group.setLayout(settings_layout)

        # Control section
        self.control_group = QGroupBox("Control")
        control_layout = QVBoxLayout()

        # Create buttons
        self.btn_start = QPushButton("Start Spammer")
        self.btn_start.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")

        self.btn_stop = QPushButton("Stop Spammer")
        self.btn_stop.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        self.btn_stop.setEnabled(False)

        self.btn_skip = QPushButton("Skip Current Profile")
        self.btn_skip.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")
        self.btn_skip.setEnabled(False)

        self.btn_kill_browser = QPushButton("Kill Browser")
        self.btn_kill_browser.setStyleSheet("background-color: #9C27B0; color: white; font-weight: bold; padding: 8px;")

        # Create horizontal layout for buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(self.btn_start)
        buttons_layout.addWidget(self.btn_stop)
        buttons_layout.addWidget(self.btn_skip)
        buttons_layout.addWidget(self.btn_kill_browser)

        # Add buttons layout to control layout
        control_layout.addLayout(buttons_layout)

        # Progress section
        progress_layout = QVBoxLayout()

        # Overall progress
        overall_progress_layout = QHBoxLayout()
        overall_progress_layout.addWidget(QLabel("Overall Progress:"))
        self.overall_progress_bar = QProgressBar()
        self.overall_progress_bar.setValue(0)
        overall_progress_layout.addWidget(self.overall_progress_bar)
        progress_layout.addLayout(overall_progress_layout)

        # Current profile progress
        profile_progress_layout = QHBoxLayout()
        profile_progress_layout.addWidget(QLabel("Current Profile:"))
        self.profile_progress_bar = QProgressBar()
        self.profile_progress_bar.setValue(0)
        profile_progress_layout.addWidget(self.profile_progress_bar)
        progress_layout.addLayout(profile_progress_layout)

        control_layout.addLayout(progress_layout)

        # Status label
        self.status_label = QLabel("Status: Idle")
        control_layout.addWidget(self.status_label)

        # Statistics
        stats_layout = QVBoxLayout()
        self.profiles_processed_label = QLabel("Profiles Processed: 0/0")
        self.comments_posted_label = QLabel("Comments Posted: 0")
        stats_layout.addWidget(self.profiles_processed_label)
        stats_layout.addWidget(self.comments_posted_label)
        control_layout.addLayout(stats_layout)

        self.control_group.setLayout(control_layout)

        # Log section
        self.log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        self.log_group.setLayout(log_layout)

        # Add groups to right layout
        right_layout.addWidget(self.settings_group)
        right_layout.addWidget(self.control_group)
        right_layout.addWidget(self.log_group)

        # Add panels to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # Set initial splitter sizes (40% left, 60% right)
        splitter.setSizes([400, 600])

        # Connect signals
        self.connect_signals()

        # Connect additional signals
        self.use_first_profile_for_selectors.stateChanged.connect(self.on_selector_detection_changed)

        # Initialize dependent controls
        self.on_selector_detection_changed(self.use_first_profile_for_selectors.isChecked())

    def connect_signals(self):
        """Connect signals to slots."""
        # Button signals for profiles
        self.btn_import_profiles.clicked.connect(self.on_import_profiles)
        self.btn_add_profile.clicked.connect(self.on_add_profile)
        self.btn_bulk_profiles.clicked.connect(self.on_bulk_profiles)
        self.btn_import_from_checker.clicked.connect(self.on_import_from_checker)

        # Button signals for posts
        self.btn_import_posts.clicked.connect(self.on_import_posts)
        self.btn_add_post.clicked.connect(self.on_add_post)
        self.btn_bulk_posts.clicked.connect(self.on_bulk_posts)
        self.btn_delete_selected_posts.clicked.connect(self.on_delete_selected_posts)

        # Button signals for comments
        self.btn_import_comments.clicked.connect(self.on_import_comments)
        self.btn_add_comment.clicked.connect(self.on_add_comment)
        self.btn_bulk_comments.clicked.connect(self.on_bulk_comments)

        # Control buttons
        self.btn_start.clicked.connect(self.start)
        self.btn_stop.clicked.connect(self.stop)
        self.btn_skip.clicked.connect(self.skip_profile)
        self.btn_kill_browser.clicked.connect(self.kill_browser)

    def update_item_counts(self):
        """Update the item counts in group titles."""
        # Get counts from database
        profiles_count = self.controller.db.get_profiles_count()
        posts_count = self.controller.db.get_posts_count()
        comments_count = self.controller.db.get_comments_count()

        # Update UI
        self.profiles_group.setTitle(f"Profiles ({profiles_count})")
        self.posts_group.setTitle(f"Posts ({posts_count})")
        self.comments_group.setTitle(f"Comments ({comments_count})")

        # Update list widgets if they don't match the database
        if self.profiles_list.count() != profiles_count:
            self.refresh_profiles_list()

        if self.posts_list.count() != posts_count:
            self.refresh_posts_list()

        if self.comments_list.count() != comments_count:
            self.refresh_comments_list()

    def load_settings_from_db(self):
        """Load settings from database."""
        try:
            from core.utils.database import Database
            db = Database()

            # Get all settings from the 'spammer' category
            spammer_settings = db.get_settings_by_category("spammer")

            if not spammer_settings:
                self.log_message("[INFO] No spammer settings found in database. Using defaults.")
                return

            # Load execute_spam_comment setting
            if "execute_spam_comment" in spammer_settings:
                execute_spam_comment = spammer_settings["execute_spam_comment"]
                self.execute_spam_comment.setChecked(execute_spam_comment == "True")
                self.log_message(f"[INFO] Loaded execute_spam_comment setting: {execute_spam_comment}")

            # Load skip_commented setting
            if "skip_commented" in spammer_settings:
                skip_commented = spammer_settings["skip_commented"]
                self.skip_commented.setChecked(skip_commented == "True")
                self.log_message(f"[INFO] Loaded skip_commented setting: {skip_commented}")

            # Load typing_speed setting
            if "typing_speed" in spammer_settings:
                typing_speed = spammer_settings["typing_speed"]
                index = self.typing_speed_combo.findText(typing_speed)
                if index >= 0:
                    self.typing_speed_combo.setCurrentIndex(index)
                    self.log_message(f"[INFO] Loaded typing_speed setting: {typing_speed}")

            # Load spam_count setting
            if "spam_count" in spammer_settings:
                spam_count = spammer_settings["spam_count"]
                try:
                    self.spam_count_spin.setValue(int(spam_count))
                    self.log_message(f"[INFO] Loaded spam_count setting: {spam_count}")
                except ValueError:
                    self.log_message(f"[WARNING] Invalid spam_count value: {spam_count}")

            # Load sleep_min setting
            if "sleep_min" in spammer_settings:
                sleep_min = spammer_settings["sleep_min"]
                try:
                    self.min_sleep_spin.setValue(int(float(sleep_min)))
                    self.log_message(f"[INFO] Loaded sleep_min setting: {sleep_min}")
                except ValueError:
                    self.log_message(f"[WARNING] Invalid sleep_min value: {sleep_min}")

            # Load sleep_max setting
            if "sleep_max" in spammer_settings:
                sleep_max = spammer_settings["sleep_max"]
                try:
                    self.max_sleep_spin.setValue(int(float(sleep_max)))
                    self.log_message(f"[INFO] Loaded sleep_max setting: {sleep_max}")
                except ValueError:
                    self.log_message(f"[WARNING] Invalid sleep_max value: {sleep_max}")

            # Load headless setting
            if "headless" in spammer_settings:
                headless = spammer_settings["headless"]
                self.headless_mode.setChecked(headless == "True")
                self.log_message(f"[INFO] Loaded headless setting: {headless}")

            # Load use_first_profile_for_selectors setting
            if "use_first_profile_for_selectors" in spammer_settings:
                use_first_profile_for_selectors = spammer_settings["use_first_profile_for_selectors"]
                self.use_first_profile_for_selectors.setChecked(use_first_profile_for_selectors == "True")
                self.log_message(f"[INFO] Loaded use_first_profile_for_selectors setting: {use_first_profile_for_selectors}")

            # Load use_first_profile_for_commenting setting
            if "use_first_profile_for_commenting" in spammer_settings:
                use_first_profile_for_commenting = spammer_settings["use_first_profile_for_commenting"]
                self.use_first_profile_for_commenting.setChecked(use_first_profile_for_commenting == "True")
                self.log_message(f"[INFO] Loaded use_first_profile_for_commenting setting: {use_first_profile_for_commenting}")

            # Load selector_detection_time setting
            if "selector_detection_time" in spammer_settings:
                selector_detection_time = spammer_settings["selector_detection_time"]
                try:
                    self.selector_detection_time_spin.setValue(int(selector_detection_time))
                    self.log_message(f"[INFO] Loaded selector_detection_time setting: {selector_detection_time}")
                except ValueError:
                    self.log_message(f"[WARNING] Invalid selector_detection_time value: {selector_detection_time}")

            # Load post_selection_mode setting
            if "post_selection_mode" in spammer_settings:
                post_selection_mode = spammer_settings["post_selection_mode"]
                index = self.post_selection_mode_combo.findText(post_selection_mode)
                if index >= 0:
                    self.post_selection_mode_combo.setCurrentIndex(index)
                    self.log_message(f"[INFO] Loaded post_selection_mode setting: {post_selection_mode}")

            # Load max_posts setting
            if "max_posts" in spammer_settings:
                max_posts = spammer_settings["max_posts"]
                try:
                    self.max_posts_spin.setValue(int(max_posts))
                    self.log_message(f"[INFO] Loaded max_posts setting: {max_posts}")
                except ValueError:
                    self.log_message(f"[WARNING] Invalid max_posts value: {max_posts}")

            # Note: auto_close setting is not used in the UI
            # as it is always set to True as requested

            self.log_message("[INFO] Settings loaded from database")
        except Exception as e:
            self.log_message(f"[WARNING] Failed to load settings from database: {e}")

    def refresh_profiles_list(self):
        """Refresh the profiles list from the database."""
        profiles = self.controller.load_profiles()
        self.profiles_list.clear()
        self.profiles_list.addItems(profiles)

    def refresh_posts_list(self):
        """Refresh the posts list from the database."""
        posts = self.controller.load_posts()
        self.posts_list.clear()
        self.posts_list.addItems(posts)

    def refresh_comments_list(self):
        """Refresh the comments list from the database."""
        comments = self.controller.load_comments()
        self.comments_list.clear()
        self.comments_list.addItems(comments)

    def load_data(self):
        """Load initial data."""
        self.log_message("[INFO] Loading initial data...")

        # Load profiles from database
        self.log_message("[INFO] Loading profiles from database...")
        self.refresh_profiles_list()

        # Load posts from database
        self.log_message("[INFO] Loading posts from database...")
        self.refresh_posts_list()

        # Load comments from database
        self.log_message("[INFO] Loading comments from database...")
        self.refresh_comments_list()

        # Update item counts
        self.update_item_counts()

        # Load settings from database
        self.log_message("[INFO] Loading settings from database...")
        self.load_settings_from_db()

        self.log_message("[INFO] Initial data loading completed")

    @pyqtSlot(str)
    def log_message(self, message):
        """Add a message to the log."""
        self.log_text.append(message)
        # Auto-scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    @pyqtSlot(int, int, int, int, str)
    def on_progress_updated(self, profile_index, total_profiles, comments_posted, total_comments_posted, status):
        """
        Handle progress update from controller.

        Args:
            profile_index (int): Current profile index.
            total_profiles (int): Total number of profiles.
            comments_posted (int): Number of comments posted for current profile.
            total_comments_posted (int): Total number of comments posted.
            status (str): Current status message.
        """
        # Update progress bars
        overall_progress = int(((profile_index + 1) / total_profiles) * 100)
        self.overall_progress_bar.setValue(overall_progress)

        profile_progress = int((comments_posted / self.spam_count_spin.value()) * 100)
        self.profile_progress_bar.setValue(profile_progress)

        # Update status label
        self.status_label.setText(f"Status: {status}")

        # Update statistics
        self.profiles_processed_label.setText(f"Profiles Processed: {profile_index + 1}/{total_profiles}")
        self.comments_posted_label.setText(f"Comments Posted: {total_comments_posted}")

    @pyqtSlot(str)
    def on_status_changed(self, status):
        """
        Handle status change from controller.

        Args:
            status (str): New status.
        """
        self.status_label.setText(f"Status: {status}")

        # Update button states
        if status == "Running":
            self.btn_start.setEnabled(False)
            self.btn_stop.setEnabled(True)
            self.btn_skip.setEnabled(True)
        elif status == "Stopping":
            self.btn_start.setEnabled(False)
            self.btn_stop.setEnabled(False)
            self.btn_skip.setEnabled(False)
        else:  # Completed, Idle, etc.
            self.btn_start.setEnabled(True)
            self.btn_stop.setEnabled(False)
            self.btn_skip.setEnabled(False)

    def on_import_profiles(self):
        """Handle import profiles button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Profiles File", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing profiles from {file_path}")

            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if content:
                    # Split content into lines and filter empty lines
                    profiles = [line.strip() for line in content.split('\n') if line.strip()]

                    # Save profiles to database
                    count = 0
                    for profile_id in profiles:
                        if self.controller.db.save_profile(profile_id):
                            count += 1

                    self.log_message(f"[INFO] Imported {count} profiles from {file_path} to database")

                    # Refresh the profiles list
                    self.refresh_profiles_list()

                    # Update item counts
                    self.update_item_counts()

                    # Show success message
                    QMessageBox.information(self, "Import Successful", f"Successfully imported {count} profiles.")
                else:
                    self.log_message(f"[WARNING] No profiles found in {file_path}")
                    QMessageBox.warning(self, "Import Warning", "No profiles found in the selected file.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import profiles: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import profiles: {e}")

    def on_add_profile(self):
        """Handle add profile button click."""
        profile, ok = QInputDialog.getText(
            self, "Add Profile", "Enter profile ID:"
        )

        if ok and profile.strip():
            profile_id = profile.strip()

            # Check if profile exists in database
            existing_profile = self.controller.db.get_profile(profile_id)

            if not existing_profile:
                # Save profile to database
                if self.controller.db.save_profile(profile_id):
                    self.log_message(f"[INFO] Added new profile: {profile_id}")

                    # Refresh the profiles list
                    self.refresh_profiles_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add profile: {profile_id}")
                    QMessageBox.critical(self, "Error", f"Failed to add profile '{profile_id}'.")
            else:
                self.log_message(f"[WARNING] Profile already exists: {profile_id}")
                QMessageBox.warning(self, "Duplicate Profile", f"Profile '{profile_id}' already exists.")

    def on_import_from_checker(self):
        """Handle import from checker button click."""
        try:
            # Get active profiles from database
            from core.utils.database import Database
            db = Database()
            active_profiles = db.get_profiles(status="active")

            if not active_profiles:
                self.log_message("[WARNING] No active profiles found in Checker")
                QMessageBox.warning(self, "No Profiles", "No active profiles found in Checker.")
                return

            # Extract profile IDs
            profile_ids = [p["profile_id"] for p in active_profiles]

            # Ask for confirmation
            reply = QMessageBox.question(
                self,
                "Import from Checker",
                f"Do you want to import {len(profile_ids)} active profiles from Checker?\n\nThis will replace your current profiles list.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Clear existing profiles in Spammer
                self.controller.db.clear_profiles()

                # Add active profiles to Spammer
                for profile_id in profile_ids:
                    self.controller.db.save_profile(profile_id)

                # Refresh profiles list
                self.refresh_profiles_list()

                # Update item counts
                self.update_item_counts()

                # Log message
                self.log_message(f"[INFO] Imported {len(profile_ids)} active profiles from Checker")
                QMessageBox.information(self, "Import Successful", f"Successfully imported {len(profile_ids)} active profiles from Checker.")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to import profiles from Checker: {e}")
            QMessageBox.critical(self, "Import Error", f"Failed to import profiles from Checker: {e}")

    def on_import_posts(self):
        """Handle import posts button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Posts File", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing posts from {file_path}")

            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if content:
                    # Split content into lines and filter empty lines
                    posts = [line.strip() for line in content.split('\n') if line.strip()]

                    # Save posts to database
                    count = 0
                    for post_url in posts:
                        if self.controller.db.save_post(post_url):
                            count += 1

                    self.log_message(f"[INFO] Imported {count} posts from {file_path} to database")

                    # Refresh the posts list
                    self.refresh_posts_list()

                    # Update item counts
                    self.update_item_counts()

                    # Show success message
                    QMessageBox.information(self, "Import Successful", f"Successfully imported {count} posts.")
                else:
                    self.log_message(f"[WARNING] No posts found in {file_path}")
                    QMessageBox.warning(self, "Import Warning", "No posts found in the selected file.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import posts: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import posts: {e}")

    def on_add_post(self):
        """Handle add post button click."""
        post, ok = QInputDialog.getText(
            self, "Add Post", "Enter post URL:"
        )

        if ok and post.strip():
            post_url = post.strip()

            # Check if post exists in database
            existing_post = self.controller.db.get_post(post_url)

            if not existing_post:
                # Save post to database
                if self.controller.db.save_post(post_url):
                    self.log_message(f"[INFO] Added new post: {post_url}")

                    # Refresh the posts list
                    self.refresh_posts_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add post: {post_url}")
                    QMessageBox.critical(self, "Error", f"Failed to add post '{post_url}'.")
            else:
                self.log_message(f"[WARNING] Post already exists: {post_url}")
                QMessageBox.warning(self, "Duplicate Post", f"Post '{post_url}' already exists.")

    def on_import_comments(self):
        """Handle import comments button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Comments File", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing comments from {file_path}")

            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if content:
                    # Split content into lines and filter empty lines
                    comments = [line.strip() for line in content.split('\n') if line.strip()]

                    # Save comments to database
                    count = 0
                    for comment_text in comments:
                        # Generate a unique comment ID
                        comment_id = f"comment_{hash(comment_text)}"
                        if self.controller.db.save_comment(comment_id, comment_text):
                            count += 1

                    self.log_message(f"[INFO] Imported {count} comments from {file_path} to database")

                    # Refresh the comments list
                    self.refresh_comments_list()

                    # Update item counts
                    self.update_item_counts()

                    # Show success message
                    QMessageBox.information(self, "Import Successful", f"Successfully imported {count} comments.")
                else:
                    self.log_message(f"[WARNING] No comments found in {file_path}")
                    QMessageBox.warning(self, "Import Warning", "No comments found in the selected file.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import comments: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import comments: {e}")

    def on_add_comment(self):
        """Handle add comment button click."""
        comment, ok = QInputDialog.getText(
            self, "Add Comment", "Enter comment text:"
        )

        if ok and comment.strip():
            comment_text = comment.strip()

            # Generate a unique comment ID
            comment_id = f"comment_{hash(comment_text)}"

            # Check if comment exists in database by content
            comments_data = self.controller.db.get_comments()
            existing_comment = any(c['content'] == comment_text for c in comments_data)

            if not existing_comment:
                # Save comment to database
                if self.controller.db.save_comment(comment_id, comment_text):
                    self.log_message(f"[INFO] Added new comment: {comment_text}")

                    # Refresh the comments list
                    self.refresh_comments_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add comment: {comment_text}")
                    QMessageBox.critical(self, "Error", f"Failed to add comment '{comment_text}'.")
            else:
                self.log_message(f"[WARNING] Comment already exists: {comment_text}")
                QMessageBox.warning(self, "Duplicate Comment", f"Comment '{comment_text}' already exists.")

    def on_bulk_profiles(self):
        """Handle bulk edit profiles button click."""
        self.log_message("[INFO] Opening bulk profiles editor")

        # Get profiles from database
        profiles_data = self.controller.db.get_profiles()
        current_profiles = [p['profile_id'] for p in profiles_data]

        # Create and show bulk editor dialog
        dialog = BulkEditorDialog("Bulk Edit Profiles", current_profiles, "profiles", self)

        # Connect the items_updated signal
        dialog.items_updated.connect(self.on_profiles_updated)

        # Show dialog
        dialog.exec_()

    def on_bulk_posts(self):
        """Handle bulk edit posts button click."""
        self.log_message("[INFO] Opening bulk posts editor")

        # Get posts from database
        posts_data = self.controller.db.get_posts()
        current_posts = [p['post_url'] for p in posts_data]

        # Create and show bulk editor dialog
        dialog = BulkEditorDialog("Bulk Edit Posts", current_posts, "posts", self)

        # Connect the items_updated signal
        dialog.items_updated.connect(self.on_posts_updated)

        # Show dialog
        dialog.exec_()

    def on_bulk_comments(self):
        """Handle bulk edit comments button click."""
        self.log_message("[INFO] Opening bulk comments editor")

        # Get comments from database
        comments_data = self.controller.db.get_comments()
        current_comments = [c['content'] for c in comments_data]

        # Create and show bulk editor dialog
        dialog = BulkEditorDialog("Bulk Edit Comments", current_comments, "comments", self)

        # Connect the items_updated signal
        dialog.items_updated.connect(self.on_comments_updated)

        # Show dialog
        dialog.exec_()

    @pyqtSlot(list)
    def on_profiles_updated(self, profiles):
        """Handle profiles updated from bulk editor."""
        self.log_message(f"[INFO] Updating profiles list with {len(profiles)} profiles")

        # Save profiles to database
        self.controller.save_profiles(profiles)

        # Refresh the profiles list
        self.refresh_profiles_list()

        # Update item counts
        self.update_item_counts()

    @pyqtSlot(list)
    def on_posts_updated(self, posts):
        """Handle posts updated from bulk editor."""
        self.log_message(f"[INFO] Updating posts list with {len(posts)} posts")

        # Save posts to database
        self.controller.save_posts(posts)

        # Refresh the posts list
        self.refresh_posts_list()

        # Update item counts
        self.update_item_counts()

    @pyqtSlot(list)
    def on_comments_updated(self, comments):
        """Handle comments updated from bulk editor."""
        self.log_message(f"[INFO] Updating comments list with {len(comments)} comments")

        # Save comments to database
        self.controller.save_comments(comments)

        # Refresh the comments list
        self.refresh_comments_list()

        # Update item counts
        self.update_item_counts()

    def start(self):
        """Start the spammer process."""
        # Get profiles, posts, and comments from lists
        profiles = [self.profiles_list.item(i).text() for i in range(self.profiles_list.count())]
        posts = [self.posts_list.item(i).text() for i in range(self.posts_list.count())]
        comments = [self.comments_list.item(i).text() for i in range(self.comments_list.count())]

        # Get settings based on original Spammer.py configuration
        settings = {
            # Use spam_count from the original Spammer.py
            "spam_count": self.spam_count_spin.value(),
            "comments_per_profile": self.spam_count_spin.value(),  # For backward compatibility

            # Sleep range settings from original Spammer.py
            "sleep_range": {
                "min": self.min_sleep_spin.value(),
                "max": self.max_sleep_spin.value()
            },

            # Toggle options from original Spammer.py
            "execute_spam_comment": self.execute_spam_comment.isChecked(),
            "skip_commented": self.skip_commented.isChecked(),
            "confirm_comments": False,  # Always disabled as requested
            "auto_close": True,  # Always enabled as requested
            "typing_speed": self.typing_speed_combo.currentText(),
            "headless": self.headless_mode.isChecked(),  # From original Spammer.py
            "use_first_profile_for_selectors": self.use_first_profile_for_selectors.isChecked(),
            "use_first_profile_for_commenting": self.use_first_profile_for_commenting.isChecked(),
            "selector_detection_time": self.selector_detection_time_spin.value(),  # Time for selector detection per post
            "post_selection_mode": self.post_selection_mode_combo.currentText(),  # Post selection mode
            "max_posts": self.max_posts_spin.value(),  # Maximum number of posts to process
            "selectors_detection_complete": False  # Reset this flag on each start
        }

        # Save settings to database in the 'spammer' category
        try:
            from core.utils.database import Database
            db = Database()
            db.save_setting("execute_spam_comment", str(settings["execute_spam_comment"]), "spammer")
            db.save_setting("skip_commented", str(settings["skip_commented"]), "spammer")
            db.save_setting("typing_speed", settings["typing_speed"], "spammer")
            db.save_setting("spam_count", str(settings["spam_count"]), "spammer")
            db.save_setting("sleep_min", str(settings["sleep_range"]["min"]), "spammer")
            db.save_setting("sleep_max", str(settings["sleep_range"]["max"]), "spammer")
            db.save_setting("headless", str(settings["headless"]), "spammer")  # From original Spammer.py
            db.save_setting("auto_close", "True", "spammer")  # Always enabled as requested
            db.save_setting("use_first_profile_for_selectors", str(settings["use_first_profile_for_selectors"]), "spammer")
            db.save_setting("use_first_profile_for_commenting", str(settings["use_first_profile_for_commenting"]), "spammer")
            db.save_setting("selector_detection_time", str(settings["selector_detection_time"]), "spammer")
            db.save_setting("post_selection_mode", settings["post_selection_mode"], "spammer")
            db.save_setting("max_posts", str(settings["max_posts"]), "spammer")
            self.log_message(f"[INFO] Settings saved to database in 'spammer' category")
        except Exception as e:
            self.log_message(f"[WARNING] Failed to save settings to database: {e}")

        # Validate inputs
        if not profiles:
            QMessageBox.warning(self, "Warning", "No profiles loaded.")
            return

        if not posts:
            QMessageBox.warning(self, "Warning", "No posts loaded.")
            return

        if not comments:
            QMessageBox.warning(self, "Warning", "No comments loaded.")
            return

        # Start spammer
        self.controller.start(profiles, posts, comments, settings)

    def stop(self):
        """Stop the spammer process."""
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Are you sure you want to stop the spammer?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.controller.stop()

    def skip_profile(self):
        """Skip the current profile."""
        self.controller.skip_profile()

    def on_comment_ready(self, comment_text, post_url):
        """Handle comment ready for confirmation."""
        self.log_message(f"[INFO] Comment ready for confirmation: {comment_text[:50]}...")

        # Create a more detailed confirmation dialog
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("Confirm Comment")
        msg_box.setIcon(QMessageBox.Question)

        # Create a more detailed message with better formatting
        message = f"<b>Do you want to post this comment?</b><br><br>"
        message += f"<b>Post URL:</b><br>"
        message += f"<a href=\"{post_url}\">{post_url}</a><br><br>"
        message += f"<b>Comment:</b><br>"
        message += f"{comment_text}<br><br>"
        message += f"<i>Note: If you click 'No', this comment will be skipped without posting.</i>"

        msg_box.setText(message)
        msg_box.setTextFormat(Qt.RichText)

        # Add buttons
        yes_button = msg_box.addButton("Yes, Post Comment", QMessageBox.YesRole)
        no_button = msg_box.addButton("No, Skip This Comment", QMessageBox.NoRole)

        # Set default button
        msg_box.setDefaultButton(yes_button)

        # Execute dialog
        msg_box.exec_()

        # Process result
        if msg_box.clickedButton() == yes_button:
            self.controller.spammer.confirm_comment(True)
            self.log_message(f"[INFO] Comment confirmed by user")
        else:
            self.controller.spammer.confirm_comment(False)
            self.log_message(f"[INFO] Comment cancelled by user")

    def on_comment_posted(self, comment_text, post_url):
        """Handle comment posted successfully."""
        self.log_message(f"[SUCCESS] Comment posted successfully: {comment_text[:50]}...")

    def on_comment_failed(self, comment_text, post_url):
        """Handle comment posting failure."""
        self.log_message(f"[WARNING] Comment posting failed: {comment_text[:50]}...")

    def kill_browser(self):
        """Kill the browser process."""
        try:
            # Ask for confirmation
            reply = QMessageBox.question(
                self,
                "Confirm Kill Browser",
                "Are you sure you want to kill the browser? This will stop all browser operations.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Stop the spammer first if it's running
                if self.controller.worker is not None and self.controller.worker.isRunning():
                    self.stop()

                # Kill all Chrome processes without checking if they're running
                if sys.platform == "win32":
                    import subprocess
                    subprocess.run("taskkill /F /IM nstchrome.exe", shell=True, stderr=subprocess.DEVNULL)
                    self.log_message("[INFO] NstBrowser processes killed")
                else:  # Linux/macOS
                    import os
                    os.system("pkill -f nstchrome 2>/dev/null")
                    self.log_message("[INFO] NstBrowser processes killed")

                QMessageBox.information(self, "Browser Killed", "Browser processes have been terminated.")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to kill browser: {e}")
            QMessageBox.critical(self, "Error", f"Failed to kill browser: {e}")

    def on_delete_selected_posts(self):
        """Handle delete selected posts button click."""
        # Get selected items
        selected_items = self.posts_list.selectedItems()

        if not selected_items:
            QMessageBox.warning(self, "No Selection", "Please select one or more posts to delete.")
            return

        # Ask for confirmation
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete {len(selected_items)} selected posts?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            deleted_count = 0
            for item in selected_items:
                post_url = item.text()
                if self.controller.db.delete_post(post_url):
                    deleted_count += 1
                    self.log_message(f"[INFO] Deleted post: {post_url}")
                else:
                    self.log_message(f"[ERROR] Failed to delete post: {post_url}")

            # Refresh posts list
            self.refresh_posts_list()

            # Update item counts
            self.update_item_counts()

            # Show success message
            self.log_message(f"[INFO] Deleted {deleted_count} posts")
            if deleted_count > 0:
                QMessageBox.information(self, "Deletion Successful", f"Successfully deleted {deleted_count} posts.")

    def on_selector_detection_changed(self, state):
        """Handle selector detection checkbox state change."""
        # Enable/disable the use_first_profile_for_commenting checkbox based on the state of use_first_profile_for_selectors
        if isinstance(state, bool):
            # Called directly with a boolean value
            enabled = state
        else:
            # Called from signal with Qt.CheckState
            enabled = state == Qt.Checked

        self.use_first_profile_for_commenting.setEnabled(enabled)

        # If selector detection is disabled, also uncheck the commenting option
        if not enabled:
            self.use_first_profile_for_commenting.setChecked(False)

        # Log the change
        self.log_message(f"[INFO] {'Enabled' if enabled else 'Disabled'} 'Use first profile for commenting' option")

    def set_hand_cursor_for_buttons(self):
        """Set hand cursor for all buttons in the UI."""
        # Find all buttons in the UI and set hand cursor
        for button in self.findChildren(QPushButton):
            button.setCursor(Qt.PointingHandCursor)

    def add_links(self, links):
        """Add links to the posts list.

        Args:
            links (list): List of post links to add
        """
        if not links:
            self.log_message("[WARNING] No links to add")
            return

        added_count = 0
        for link in links:
            # Check if link already exists in the list
            exists = False
            for i in range(self.posts_list.count()):
                if self.posts_list.item(i).text() == link:
                    exists = True
                    break

            if not exists:
                # Add to UI
                self.posts_list.addItem(link)
                # Add to database
                if hasattr(self.controller, 'db') and self.controller.db:
                    self.controller.db.save_post(link)
                added_count += 1

        if added_count > 0:
            self.log_message(f"[INFO] Added {added_count} new post links from Post Scraper")
            # Update item counts
            self.update_item_counts()
        else:
            self.log_message("[INFO] No new links added (all links already exist)")

    def closeEvent(self, event):
        """Handle tab close event."""
        self.controller.close()
        super().closeEvent(event)
