"""
Module for handling Facebook-specific interactions.
"""

import time
import logging
import json
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException

from .dynamic_page_handler import DynamicPageHandler

class FacebookHandler(DynamicPageHandler):
    """Handler for Facebook-specific interactions."""
    
    # Facebook selectors - these may need to be updated as Facebook changes its UI
    SELECTORS = {
        'post_containers': [
            (By.CSS_SELECTOR, 'div[role="article"]'),
            (By.CSS_SELECTOR, 'div[data-pagelet^="FeedUnit"]'),
            (By.CSS_SELECTOR, 'div.x1yztbdb:not([data-pagelet])'),
            (By.CSS_SELECTOR, 'div.x1lliihq')
        ],
        'post_timestamps': [
            (By.CSS_SELECTOR, 'span.x4k7w5x a'),
            (By.CSS_SELECTOR, 'span.x1i10hfl[role="link"]'),
            (By.CSS_SELECTOR, 'a[role="link"][aria-label*="at"]'),
            (By.CSS_SELECTOR, 'abbr[data-utime]'),
            (By.CSS_SELECTOR, 'span[title*=":"] a')
        ],
        'post_images': [
            (By.CSS_SELECTOR, 'a[role="link"] img.x1ey2m1c'),
            (By.CSS_SELECTOR, 'div.x1qjc9v5 a[role="link"]'),
            (By.CSS_SELECTOR, 'a[href*="photo.php"]'),
            (By.CSS_SELECTOR, 'a[href*="photo/?fbid"]')
        ],
        'post_content': [
            (By.CSS_SELECTOR, 'div[data-ad-preview="message"]'),
            (By.CSS_SELECTOR, 'div.xdj266r'),
            (By.CSS_SELECTOR, 'div.x1iorvi4')
        ],
        'comment_buttons': [
            (By.CSS_SELECTOR, 'div[aria-label*="comment"]'),
            (By.CSS_SELECTOR, 'span[role="button"]:not([aria-label=""]):nth-of-type(1)'),
            (By.CSS_SELECTOR, 'a[href*="comment"]')
        ],
        'like_buttons': [
            (By.CSS_SELECTOR, 'div[aria-label*="like"]'),
            (By.CSS_SELECTOR, 'span[role="button"]:not([aria-label=""]):nth-of-type(2)'),
            (By.CSS_SELECTOR, 'a[href*="like"]')
        ],
        'popups': {
            (By.CSS_SELECTOR, 'div[aria-label="Close"]'): lambda e: e.click(),
            (By.CSS_SELECTOR, 'div[role="dialog"] button[aria-label="Close"]'): lambda e: e.click(),
            (By.XPATH, '//div[contains(text(), "Accept")]'): lambda e: e.click(),
            (By.XPATH, '//div[contains(text(), "Accept All")]'): lambda e: e.click()
        }
    }
    
    def __init__(self, driver, logger=None):
        """Initialize the Facebook handler.
        
        Args:
            driver: Selenium WebDriver instance
            logger: Logger instance (optional)
        """
        super().__init__(driver, logger or logging.getLogger(__name__))
        self.logger.info("Facebook handler initialized")
    
    def navigate_to_group(self, group_url, wait_for_posts=True):
        """Navigate to a Facebook group and wait for it to load.
        
        Args:
            group_url: URL of the Facebook group
            wait_for_posts: Whether to wait for posts to load
            
        Returns:
            bool: True if navigation was successful, False otherwise
        """
        try:
            self.logger.info(f"Navigating to Facebook group: {group_url}")
            
            # Navigate to the group
            self.driver.get(group_url)
            
            # Wait for page to load
            if not self.wait_for_page_load(timeout=30):
                self.logger.error("Failed to load Facebook group page")
                return False
            
            # Handle any popups
            self.detect_and_handle_popups(self.SELECTORS['popups'])
            
            # Wait for posts to load if requested
            if wait_for_posts:
                self.logger.info("Waiting for posts to load")
                for selector in self.SELECTORS['post_containers']:
                    try:
                        element = self.find_element_with_retry([selector], max_attempts=3, wait_time=5)
                        if element:
                            self.logger.info("Posts loaded successfully")
                            return True
                    except Exception as e:
                        self.logger.debug(f"Error waiting for posts with selector {selector}: {str(e)}")
                
                self.logger.warning("Could not confirm posts loaded, but page navigation completed")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error navigating to Facebook group: {str(e)}")
            return False
    
    def extract_post_links(self, max_posts=20, use_timestamps=True, use_images=True):
        """Extract post links from the current Facebook page.
        
        Args:
            max_posts: Maximum number of posts to extract
            use_timestamps: Whether to use timestamps to find post links
            use_images: Whether to use images to find post links
            
        Returns:
            list: List of extracted post URLs
        """
        self.logger.info(f"Extracting up to {max_posts} post links (timestamps: {use_timestamps}, images: {use_images})")
        
        # JavaScript function to extract post links
        js_function = """
        function extractPostLinks(maxPosts, useTimestamps, useImages) {
            // Array to store extracted post URLs
            const extractedUrls = [];
            
            // Function to find post elements
            function findPostElements() {
                const elements = [];
                
                // Find timestamp elements if enabled
                if (useTimestamps) {
                    const timestampSelectors = [
                        'span.x4k7w5x a',
                        'span.x1i10hfl[role="link"]',
                        'a[role="link"][aria-label*="at"]',
                        'abbr[data-utime]',
                        'span[title*=":"] a',
                        'a.oajrlxb2[href*="/posts/"]',
                        'a.oajrlxb2[href*="/permalink/"]',
                        'a[href*="/posts/"][role="link"]',
                        'a[href*="/permalink/"][role="link"]'
                    ];
                    
                    for (const selector of timestampSelectors) {
                        const found = document.querySelectorAll(selector);
                        if (found && found.length > 0) {
                            console.log(`Found ${found.length} elements with selector: ${selector}`);
                            elements.push(...Array.from(found));
                        }
                    }
                }
                
                // Find image elements if enabled and if we need more elements
                if (useImages && (elements.length < maxPosts)) {
                    const imageSelectors = [
                        'a[role="link"] img.x1ey2m1c',
                        'div.x1qjc9v5 a[role="link"]',
                        'a[href*="photo.php"]',
                        'a[href*="photo/?fbid"]'
                    ];
                    
                    for (const selector of imageSelectors) {
                        const found = document.querySelectorAll(selector);
                        if (found && found.length > 0) {
                            console.log(`Found ${found.length} image elements with selector: ${selector}`);
                            elements.push(...Array.from(found));
                        }
                    }
                }
                
                return elements;
            }
            
            // Find post elements
            const postElements = findPostElements();
            console.log(`Found ${postElements.length} total post elements`);
            
            // Limit to max posts
            const elementsToProcess = postElements.slice(0, maxPosts);
            
            // Process each element
            for (let i = 0; i < elementsToProcess.length; i++) {
                const element = elementsToProcess[i];
                try {
                    // Check if element is or contains a link
                    if (element.tagName === 'A' || element.closest('a')) {
                        const link = element.tagName === 'A' ? element : element.closest('a');
                        
                        // Get href attribute
                        const href = link.getAttribute('href');
                        
                        // If href contains post identifiers, we can use it directly
                        if (href && (href.includes('/posts/') || href.includes('/permalink/'))) {
                            console.log(`Direct post link found: ${href}`);
                            
                            // Convert relative URL to absolute if needed
                            let fullUrl = href;
                            if (href.startsWith('/')) {
                                fullUrl = 'https://web.facebook.com' + href;
                            }
                            
                            // Add to extracted URLs if not already present
                            if (!extractedUrls.includes(fullUrl)) {
                                extractedUrls.push(fullUrl);
                                console.log(`Added direct URL: ${fullUrl}`);
                            }
                        }
                    }
                } catch (e) {
                    console.error(`Error processing element ${i+1}: ${e.message}`);
                }
            }
            
            return extractedUrls;
        }
        
        return extractPostLinks(arguments[0], arguments[1], arguments[2]);
        """
        
        try:
            # Execute the JavaScript function
            post_links = self.execute_js_function(js_function, max_posts, use_timestamps, use_images)
            
            if post_links and isinstance(post_links, list):
                self.logger.info(f"Successfully extracted {len(post_links)} post links")
                return post_links
            else:
                self.logger.warning("No post links were extracted")
                return []
                
        except Exception as e:
            self.logger.error(f"Error extracting post links: {str(e)}")
            return []
    
    def extract_post_links_by_clicking(self, max_posts=20):
        """Extract post links by clicking on post timestamps or images.
        
        Args:
            max_posts: Maximum number of posts to extract
            
        Returns:
            list: List of extracted post URLs
        """
        self.logger.info(f"Extracting up to {max_posts} post links by clicking")
        
        # JavaScript function to extract post links by clicking
        js_function = """
        async function extractPostLinksByClick(maxPosts) {
            // Array to store extracted post URLs
            const extractedUrls = [];
            
            // Function to find post elements (timestamps, images, etc.)
            function findPostElements() {
                const elements = [];
                
                // Find timestamp elements (these usually link directly to the post)
                const timestampSelectors = [
                    'span.x4k7w5x a',
                    'span.x1i10hfl[role="link"]',
                    'a[role="link"][aria-label*="at"]',
                    'abbr[data-utime]',
                    'span[title*=":"] a',
                    'a.oajrlxb2[href*="/posts/"]',
                    'a.oajrlxb2[href*="/permalink/"]',
                    'a[href*="/posts/"][role="link"]',
                    'a[href*="/permalink/"][role="link"]'
                ];
                
                // Try each selector
                for (const selector of timestampSelectors) {
                    const found = document.querySelectorAll(selector);
                    if (found && found.length > 0) {
                        console.log(`Found ${found.length} elements with selector: ${selector}`);
                        elements.push(...Array.from(found));
                    }
                }
                
                // If we didn't find any timestamp elements, try image elements
                if (elements.length === 0) {
                    const imageSelectors = [
                        'a[role="link"] img.x1ey2m1c',
                        'div.x1qjc9v5 a[role="link"]',
                        'a[href*="photo.php"]',
                        'a[href*="photo/?fbid"]'
                    ];
                    
                    for (const selector of imageSelectors) {
                        const found = document.querySelectorAll(selector);
                        if (found && found.length > 0) {
                            console.log(`Found ${found.length} image elements with selector: ${selector}`);
                            elements.push(...Array.from(found));
                        }
                    }
                }
                
                return elements;
            }
            
            // Function to extract post URL from current page
            function extractCurrentPostUrl() {
                const url = window.location.href;
                
                // Check if this is a post URL
                if (url.includes('/posts/') || 
                    url.includes('/permalink/') || 
                    url.includes('/photo.php') || 
                    url.includes('/photo/?fbid')) {
                    
                    // For photo URLs, try to find the post link in the page
                    if (url.includes('/photo.php') || url.includes('/photo/?fbid')) {
                        // Look for "View post" or similar links
                        const viewPostLinks = document.querySelectorAll('a[role="link"]');
                        for (const link of viewPostLinks) {
                            const href = link.getAttribute('href');
                            const text = link.textContent;
                            if ((href && (href.includes('/posts/') || href.includes('/permalink/'))) ||
                                (text && (text.includes('View post') || text.includes('View Post')))) {
                                return link.href;
                            }
                        }
                    }
                    
                    return url;
                }
                
                return null;
            }
            
            // Find post elements
            const postElements = findPostElements();
            console.log(`Found ${postElements.length} total post elements`);
            
            // Limit to max posts
            const elementsToProcess = postElements.slice(0, maxPosts);
            
            // Process each element
            for (let i = 0; i < elementsToProcess.length; i++) {
                const element = elementsToProcess[i];
                try {
                    console.log(`Processing element ${i+1}/${elementsToProcess.length}`);
                    
                    // Store current URL to return to
                    const currentUrl = window.location.href;
                    
                    // Check if element is clickable
                    if (element.tagName === 'A' || element.closest('a')) {
                        const link = element.tagName === 'A' ? element : element.closest('a');
                        
                        // Get href attribute
                        const href = link.getAttribute('href');
                        
                        // If href contains post identifiers, we can use it directly
                        if (href && (href.includes('/posts/') || href.includes('/permalink/'))) {
                            console.log(`Direct post link found: ${href}`);
                            
                            // Convert relative URL to absolute if needed
                            let fullUrl = href;
                            if (href.startsWith('/')) {
                                fullUrl = 'https://web.facebook.com' + href;
                            }
                            
                            // Add to extracted URLs if not already present
                            if (!extractedUrls.includes(fullUrl)) {
                                extractedUrls.push(fullUrl);
                                console.log(`Added direct URL: ${fullUrl}`);
                            }
                        } else {
                            // Click the element to navigate to the post
                            console.log(`Clicking element to navigate to post...`);
                            link.click();
                            
                            // Wait for navigation
                            await new Promise(resolve => setTimeout(resolve, 3000));
                            
                            // Extract post URL from current page
                            const postUrl = extractCurrentPostUrl();
                            if (postUrl && !extractedUrls.includes(postUrl)) {
                                extractedUrls.push(postUrl);
                                console.log(`Added URL after navigation: ${postUrl}`);
                            }
                            
                            // Navigate back to the group page
                            console.log(`Navigating back to group page...`);
                            window.location.href = currentUrl;
                            
                            // Wait for navigation back
                            await new Promise(resolve => setTimeout(resolve, 3000));
                        }
                    }
                } catch (e) {
                    console.error(`Error processing element ${i+1}: ${e.message}`);
                }
            }
            
            return extractedUrls;
        }
        
        return await extractPostLinksByClick(arguments[0]);
        """
        
        try:
            # Execute the JavaScript function
            post_links = self.execute_js_function(js_function, max_posts)
            
            if post_links and isinstance(post_links, list):
                self.logger.info(f"Successfully extracted {len(post_links)} post links by clicking")
                return post_links
            else:
                self.logger.warning("No post links were extracted by clicking")
                return []
                
        except Exception as e:
            self.logger.error(f"Error extracting post links by clicking: {str(e)}")
            return []
    
    def extract_post_data(self, post_url):
        """Extract data from a specific Facebook post.
        
        Args:
            post_url: URL of the Facebook post
            
        Returns:
            dict: Post data including text, timestamp, likes, comments, etc.
        """
        self.logger.info(f"Extracting data from post: {post_url}")
        
        try:
            # Navigate to the post
            self.driver.get(post_url)
            
            # Wait for page to load
            if not self.wait_for_page_load(timeout=30):
                self.logger.error("Failed to load Facebook post page")
                return {}
            
            # Handle any popups
            self.detect_and_handle_popups(self.SELECTORS['popups'])
            
            # Initialize post data
            post_data = {
                'url': post_url,
                'text': '',
                'timestamp': '',
                'likes': 0,
                'comments': 0,
                'shares': 0
            }
            
            # Extract post text
            for selector in self.SELECTORS['post_content']:
                try:
                    element = self.find_element_with_retry([selector], max_attempts=2, wait_time=5)
                    if element:
                        post_data['text'] = element.text
                        break
                except Exception as e:
                    self.logger.debug(f"Error extracting post text with selector {selector}: {str(e)}")
            
            # Extract timestamp
            for selector in self.SELECTORS['post_timestamps']:
                try:
                    element = self.find_element_with_retry([selector], max_attempts=2, wait_time=5)
                    if element:
                        post_data['timestamp'] = element.get_attribute('title') or element.text
                        break
                except Exception as e:
                    self.logger.debug(f"Error extracting timestamp with selector {selector}: {str(e)}")
            
            # Extract engagement data using JavaScript
            js_function = """
            function extractEngagementData() {
                const data = {
                    likes: 0,
                    comments: 0,
                    shares: 0
                };
                
                // Try to find like count
                const likeElements = document.querySelectorAll('span[role="button"]:not([aria-label=""]), a[href*="like"], div[aria-label*="like"]');
                for (const elem of likeElements) {
                    const text = elem.textContent || '';
                    const match = text.match(/(\\d+)\\s*(like|Like)/);
                    if (match) {
                        data.likes = parseInt(match[1], 10);
                        break;
                    }
                }
                
                // Try to find comment count
                const commentElements = document.querySelectorAll('span[role="button"]:not([aria-label=""]), a[href*="comment"], div[aria-label*="comment"]');
                for (const elem of commentElements) {
                    const text = elem.textContent || '';
                    const match = text.match(/(\\d+)\\s*(comment|Comment)/);
                    if (match) {
                        data.comments = parseInt(match[1], 10);
                        break;
                    }
                }
                
                // Try to find share count
                const shareElements = document.querySelectorAll('span[role="button"]:not([aria-label=""]), a[href*="share"], div[aria-label*="share"]');
                for (const elem of shareElements) {
                    const text = elem.textContent || '';
                    const match = text.match(/(\\d+)\\s*(share|Share)/);
                    if (match) {
                        data.shares = parseInt(match[1], 10);
                        break;
                    }
                }
                
                return data;
            }
            
            return extractEngagementData();
            """
            
            engagement_data = self.execute_js_function(js_function)
            if engagement_data and isinstance(engagement_data, dict):
                post_data.update(engagement_data)
            
            self.logger.info(f"Successfully extracted data from post: {post_url}")
            return post_data
            
        except Exception as e:
            self.logger.error(f"Error extracting post data: {str(e)}")
            return {'url': post_url, 'error': str(e)}
    
    def extract_multiple_posts(self, max_posts=20, scroll_count=5, extract_details=False):
        """Extract multiple posts from the current Facebook page.
        
        Args:
            max_posts: Maximum number of posts to extract
            scroll_count: Number of times to scroll to load more posts
            extract_details: Whether to extract detailed data for each post
            
        Returns:
            list: List of post data dictionaries
        """
        self.logger.info(f"Extracting up to {max_posts} posts (scroll count: {scroll_count}, extract details: {extract_details})")
        
        try:
            # Scroll to load more posts
            if scroll_count > 0:
                self.logger.info(f"Scrolling {scroll_count} times to load more posts")
                self.infinite_scroll(max_scrolls=scroll_count, scroll_pause_time=2)
            
            # Extract post links
            post_links = self.extract_post_links(max_posts=max_posts)
            
            if not post_links:
                self.logger.warning("No post links found, trying click method")
                post_links = self.extract_post_links_by_clicking(max_posts=max_posts)
            
            if not post_links:
                self.logger.error("Failed to extract any post links")
                return []
            
            # Limit to max_posts
            post_links = post_links[:max_posts]
            
            # Extract detailed data if requested
            if extract_details:
                self.logger.info(f"Extracting detailed data for {len(post_links)} posts")
                posts_data = []
                
                for i, post_url in enumerate(post_links):
                    self.logger.info(f"Extracting data for post {i+1}/{len(post_links)}: {post_url}")
                    post_data = self.extract_post_data(post_url)
                    posts_data.append(post_data)
                    
                    # Small delay between requests to avoid rate limiting
                    if i < len(post_links) - 1:
                        time.sleep(1)
                
                return posts_data
            else:
                # Just return the links as simple data
                return [{'url': url} for url in post_links]
                
        except Exception as e:
            self.logger.error(f"Error extracting multiple posts: {str(e)}")
            return []
    
    def save_posts_to_file(self, posts_data, file_path, format='json'):
        """Save extracted posts data to a file.
        
        Args:
            posts_data: List of post data dictionaries
            file_path: Path to save the file
            format: File format ('json', 'txt', or 'csv')
            
        Returns:
            bool: True if save was successful, False otherwise
        """
        self.logger.info(f"Saving {len(posts_data)} posts to {file_path} in {format} format")
        
        try:
            if format.lower() == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(posts_data, f, indent=2, ensure_ascii=False)
            
            elif format.lower() == 'txt':
                with open(file_path, 'w', encoding='utf-8') as f:
                    for post in posts_data:
                        f.write(f"URL: {post.get('url', '')}\n")
                        if 'text' in post:
                            f.write(f"Text: {post.get('text', '')}\n")
                        if 'timestamp' in post:
                            f.write(f"Timestamp: {post.get('timestamp', '')}\n")
                        if 'likes' in post:
                            f.write(f"Likes: {post.get('likes', 0)}\n")
                        if 'comments' in post:
                            f.write(f"Comments: {post.get('comments', 0)}\n")
                        if 'shares' in post:
                            f.write(f"Shares: {post.get('shares', 0)}\n")
                        f.write("\n---\n\n")
            
            elif format.lower() == 'csv':
                import csv
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    # Determine all possible fields
                    fields = set()
                    for post in posts_data:
                        fields.update(post.keys())
                    
                    fields = sorted(list(fields))
                    writer = csv.DictWriter(f, fieldnames=fields)
                    writer.writeheader()
                    writer.writerows(posts_data)
            
            else:
                self.logger.error(f"Unsupported format: {format}")
                return False
            
            self.logger.info(f"Successfully saved posts to {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving posts to file: {str(e)}")
            return False
