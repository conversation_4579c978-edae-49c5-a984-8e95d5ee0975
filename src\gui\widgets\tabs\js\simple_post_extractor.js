/**
 * Simple Facebook Post Extractor
 *
 * A lightweight script to extract posts from Facebook
 */

function extractFacebookPosts() {
    // Find all post elements
    const posts = [];
    const postElements = document.querySelectorAll('div[role="article"]');
    console.log(`Found ${postElements.length} post elements`);

    for (const post of postElements) {
        try {
            // Initialize post data
            const postData = {
                url: '',
                postId: '',
                authorId: '',
                author: '',
                content: '',
                timestamp: '',
                commentCount: 0,
                reactionCount: 0,
                sharedCount: 0
            };

            // Extract post link
            const postLinks = post.querySelectorAll('a[href*="/posts/"], a[href*="/permalink/"], a[href*="story_fbid="], a[href*="fbid="]');
            for (const link of postLinks) {
                if (link.href) {
                    postData.url = link.href;
                    postData.timestamp = link.textContent.trim() || link.getAttribute('aria-label') || '';

                    // Extract post ID
                    if (postData.url.includes('/posts/')) {
                        postData.postId = postData.url.split('/posts/')[1].split('/')[0].split('?')[0];
                    } else if (postData.url.includes('/permalink/')) {
                        postData.postId = postData.url.split('/permalink/')[1].split('/')[0].split('?')[0];
                    } else if (postData.url.includes('story_fbid=')) {
                        try {
                            postData.postId = new URL(postData.url).searchParams.get('story_fbid');
                        } catch (e) {
                            console.error('Error parsing story_fbid:', e);
                        }
                    } else if (postData.url.includes('fbid=')) {
                        try {
                            postData.postId = new URL(postData.url).searchParams.get('fbid');
                        } catch (e) {
                            console.error('Error parsing fbid:', e);
                        }
                    }

                    break;
                }
            }

            // Skip if no URL found
            if (!postData.url) continue;

            // Skip media links
            if (postData.url.includes('/photo.php') ||
                postData.url.includes('/video.php') ||
                postData.url.includes('/watch/') ||
                postData.url.includes('/reel/') ||
                postData.url.includes('media') ||
                postData.url.includes('giphy') ||
                postData.url.includes('.gif') ||
                postData.url.includes('.jpg') ||
                postData.url.includes('.png')) {
                continue;
            }

            // Clean URL
            if (postData.url.includes('?')) {
                postData.url = postData.url.split('?')[0];
            }

            // Extract author
            const authorSelectors = [
                'h4 a[role="link"]',
                'a[role="link"][tabindex="0"]',
                'h3 a[role="link"]',
                'h2 a[role="link"]',
                'a[role="link"][aria-hidden="false"]',
                'span[dir="auto"] a[role="link"]',
                'div.x1heor9g a[role="link"]',
                'div.x1iorvi4 a[role="link"]'
            ];

            let authorElement = null;
            for (const selector of authorSelectors) {
                authorElement = post.querySelector(selector);
                if (authorElement) {
                    postData.author = authorElement.textContent.trim();
                    break;
                }
            }

            // Extract author ID if author element found - improved version
            if (authorElement) {
                // Method 1: Extract from href attribute
                if (authorElement.href && authorElement.href.includes('facebook.com/')) {
                    // Handle different URL formats
                    if (authorElement.href.includes('profile.php')) {
                        try {
                            const urlObj = new URL(authorElement.href);
                            const id = urlObj.searchParams.get('id');
                            if (id) {
                                postData.authorId = id;
                                console.log(`Found author ID from profile.php URL: ${id}`);
                            }
                        } catch (e) {
                            console.error('Error parsing profile URL:', e);
                        }
                    } else if (authorElement.href.includes('/user/')) {
                        // Handle /user/ URLs
                        const parts = authorElement.href.split('/user/')[1].split('?')[0].split('/');
                        if (parts.length > 0 && parts[0]) {
                            postData.authorId = parts[0];
                            console.log(`Found author ID from /user/ URL: ${parts[0]}`);
                        }
                    } else {
                        // Handle username URLs
                        const parts = authorElement.href.split('facebook.com/')[1].split('?')[0].split('/');
                        if (parts.length > 0 && parts[0] && parts[0] !== 'groups') {
                            postData.authorId = parts[0];
                            console.log(`Found author username from URL: ${parts[0]}`);
                        }
                    }
                }

                // Method 2: Try data attributes (more reliable for numeric IDs)
                const hovercard = authorElement.getAttribute('data-hovercard');
                if (hovercard && hovercard.includes('id=')) {
                    const match = hovercard.match(/id=(\d+)/);
                    if (match && match[1]) {
                        postData.authorId = match[1];
                        console.log(`Found author ID from data-hovercard: ${match[1]}`);
                    }
                }

                // Method 3: Try data-hovercard-owner-id attribute
                const ownerIdAttr = authorElement.getAttribute('data-hovercard-owner-id');
                if (ownerIdAttr) {
                    postData.authorId = ownerIdAttr;
                    console.log(`Found author ID from data-hovercard-owner-id: ${ownerIdAttr}`);
                }

                // Method 4: Try data-gt attribute (contains JSON with actor_id)
                const gtAttr = authorElement.getAttribute('data-gt');
                if (gtAttr) {
                    try {
                        const gtData = JSON.parse(gtAttr);
                        if (gtData && gtData.actor_id) {
                            postData.authorId = gtData.actor_id;
                            console.log(`Found author ID from data-gt: ${gtData.actor_id}`);
                        }
                    } catch (e) {
                        console.error('Error parsing data-gt attribute:', e);
                    }
                }

                // Method 5: Look in parent elements
                if (!postData.authorId || postData.authorId === 'groups') {
                    try {
                        // Try to find the user ID in the parent elements
                        const parentWithId = authorElement.closest('[data-hovercard], [data-hovercard-owner-id], [data-gt]');
                        if (parentWithId) {
                            // Check data-hovercard
                            const parentIdAttr = parentWithId.getAttribute('data-hovercard');
                            if (parentIdAttr && parentIdAttr.includes('id=')) {
                                const idMatch = parentIdAttr.match(/id=(\d+)/);
                                if (idMatch && idMatch[1]) {
                                    postData.authorId = idMatch[1];
                                    console.log(`Found author ID from parent data-hovercard: ${idMatch[1]}`);
                                }
                            }

                            // Check data-hovercard-owner-id
                            const parentOwnerId = parentWithId.getAttribute('data-hovercard-owner-id');
                            if (parentOwnerId) {
                                postData.authorId = parentOwnerId;
                                console.log(`Found author ID from parent data-hovercard-owner-id: ${parentOwnerId}`);
                            }
                        }
                    } catch (e) {
                        console.error('Error extracting author ID from parent elements:', e);
                    }
                }
            }

            // Extract content
            const contentSelectors = [
                'div[data-ad-preview="message"]',
                'div[data-ad-comet-preview="message"]',
                'div.xdj266r',
                'div.x1iorvi4',
                'div.x11i5rnm'
            ];

            for (const selector of contentSelectors) {
                const contentElement = post.querySelector(selector);
                if (contentElement) {
                    postData.content = contentElement.textContent.trim();
                    break;
                }
            }

            // Add to results if we have a valid post
            if (postData.url && postData.postId) {
                posts.push(postData);
            }

        } catch (e) {
            console.error('Error extracting post data:', e);
        }
    }

    return posts;
}

// Export for use in Selenium
if (typeof module !== 'undefined') {
    module.exports = extractFacebookPosts;
}
