/**
 * Facebook Direct Post Extractor
 *
 * This script extracts post links directly from the DOM without clicking on date elements.
 * It's designed to be more reliable and work in cases where the date click method fails.
 */

const FacebookDirectPostExtractor = (function() {
    // Configuration
    const config = {
        // Delay between actions (ms)
        minDelay: 1000,
        maxDelay: 3000,

        // Maximum number of posts to extract
        maxPosts: 3000,

        // Scroll settings
        scrollStep: 800,
        maxScrolls: 50,

        // Maximum number of consecutive failures before trying recovery
        maxFailures: 3
    };

    // State tracking
    const state = {
        collectedUrls: [],
        processedElements: new Set(),
        failureCount: 0,
        isRunning: false,
        startTime: null,
        lastScrollPosition: 0
    };

    // Helper functions
    const helpers = {
        delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

        randomDelay: async function() {
            const delay = config.minDelay + Math.random() * (config.maxDelay - config.minDelay);
            console.log(`Waiting ${(delay/1000).toFixed(1)} seconds...`);
            await this.delay(delay);
        },

        scrollDown: async function() {
            const currentPosition = window.scrollY;

            // Always scroll forward, never backward
            const scrollAmount = Math.floor(window.innerHeight * (0.5 + Math.random() * 0.5));
            const newPosition = currentPosition + scrollAmount;

            console.log(`Scrolling down by ${scrollAmount}px to position ${newPosition}`);

            window.scrollTo({
                top: newPosition,
                behavior: 'smooth'
            });

            // Save last scroll position
            state.lastScrollPosition = newPosition;

            // Wait for content to load
            await this.delay(1000 + Math.random() * 1000);
        },

        clickSeeMoreButtons: async function() {
            // Selectors for buttons that might be "See More" buttons
            const seeMoreButtons = [
                'div[role="button"]:not([aria-hidden="true"]):not([aria-disabled="true"])',
                'span[role="button"]',
                'a[role="button"]',
                'div.x1i10hfl[role="button"]',  // Facebook's dynamic class for buttons
                'div.x1n2onr6[role="button"]',  // Another Facebook dynamic class
                'div[aria-expanded="false"]',    // Collapsed content indicators
                'a.see_more_link',              // Classic see more link
                'a.UFIPagerLink',               // Comments expander
                'a.UFICommentLink'              // Reply expander
            ];

            // "See More" text in multiple languages
            const seeMoreTexts = [
                // English
                'see more', 'show more', 'view more', 'more comments', 'more replies', 'more results', 'load more',

                // Arabic
                'عرض المزيد', 'اقرأ المزيد', 'المزيد من التعليقات', 'مشاهدة المزيد', 'تحميل المزيد',

                // Spanish
                'ver más', 'mostrar más', 'más comentarios', 'cargar más',

                // French
                'voir plus', 'afficher plus', 'plus de commentaires', 'charger plus',

                // German
                'mehr anzeigen', 'weitere kommentare', 'mehr laden',

                // Portuguese
                'ver mais', 'mostrar mais', 'mais comentários', 'carregar mais',

                // Turkish
                'daha fazla', 'daha fazla yorum', 'daha fazla göster',

                // Indonesian
                'lihat selengkapnya', 'tampilkan lebih banyak', 'muat lebih banyak',

                // Russian
                'показать больше', 'еще комментарии', 'загрузить еще'
            ];

            let clicked = 0;

            // First try to find buttons by their aria-label
            try {
                const expandButtons = document.querySelectorAll('[aria-label*="expand"], [aria-label*="more"], [aria-label*="show"]');
                for (const button of expandButtons) {
                    try {
                        button.click();
                        clicked++;
                        console.log('Clicked expand button by aria-label');
                    } catch (e) {
                        console.error('Error clicking expand button:', e);
                    }
                }
            } catch (e) {
                console.error('Error finding expand buttons by aria-label:', e);
            }

            // Then try by selectors and text content
            for (const selector of seeMoreButtons) {
                try {
                    const buttons = document.querySelectorAll(selector);
                    for (const button of buttons) {
                        const text = button.textContent.toLowerCase();

                        // Check if the button text includes any of the "see more" phrases
                        const isSeeMoreButton = seeMoreTexts.some(phrase => text.includes(phrase.toLowerCase()));

                        if (isSeeMoreButton) {
                            try {
                                button.click();
                                clicked++;
                                console.log(`Clicked "See More" button with text: "${text.substring(0, 20)}..."`);

                                // Add a small delay to avoid clicking too many buttons at once
                                await new Promise(resolve => setTimeout(resolve, 100));
                            } catch (e) {
                                console.error('Error clicking button:', e);
                            }
                        }
                    }
                } catch (e) {
                    console.error(`Error with selector ${selector}:`, e);
                }
            }

            return clicked;
        },

        findPostElements: function() {
            // Comprehensive selectors for post elements
            const postSelectors = [
                // Main post containers
                'div[role="article"]',
                'div.userContentWrapper',
                'div.fbUserContent',
                'div.fbUserPost',
                'div.feed_story_container',
                'div.story_body_container',
                'div[data-testid="post_container"]',

                // Modern Facebook post containers (dynamic classes)
                'div.x1yztbdb',  // Common post container class
                'div.x1lliihq',  // Another post container class
                'div.x1n2onr6',  // Feed item class

                // Group post specific containers
                'div[data-pagelet^="GroupFeed"]',
                'div[data-pagelet="GroupFeed"] > div',
                'div[data-pagelet="GroupInlineComposer"]',

                // Fallback to any div that might be a post
                'div.xdj266r',   // Common in group posts
                'div.x1jx94hy',  // Common in feed posts
                'div.x78zum5'    // Another common post class
            ];

            // Find all post elements
            let postElements = [];

            for (const selector of postSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        console.log(`Found ${elements.length} post elements with selector: ${selector}`);
                        postElements = [...postElements, ...Array.from(elements)];
                    }
                } catch (e) {
                    console.error(`Error with selector ${selector}:`, e);
                }
            }

            // Filter out duplicates and non-post elements
            const uniquePosts = new Set();
            const validPosts = [];

            for (const element of postElements) {
                // Skip if we've already processed this element
                if (uniquePosts.has(element)) continue;

                // Check if it's a valid post (has links, text content, etc.)
                const hasLinks = element.querySelectorAll('a[href]').length > 0;
                const hasText = element.textContent.trim().length > 50; // Posts usually have some text

                if (hasLinks && hasText) {
                    uniquePosts.add(element);
                    validPosts.push(element);
                }
            }

            console.log(`Found ${validPosts.length} valid post elements after filtering`);
            return validPosts;
        },

        extractPostLinks: function(postElements) {
            const postLinks = [];

            // Time-related text patterns in multiple languages
            const timePatterns = [
                // Short time formats (English)
                's ago', 'm ago', 'h ago', 'd ago', 'w ago', 'mo ago', 'y ago',
                'sec', 'min', 'hr', 'hrs', 'day', 'days', 'wk', 'wks', 'mo', 'mos', 'yr', 'yrs',

                // Numeric time formats (all languages)
                '1s', '2s', '3s', '4s', '5s', '10s', '15s', '20s', '30s', '45s', '60s',
                '1m', '2m', '3m', '4m', '5m', '10m', '15m', '20m', '30m', '45m', '60m',
                '1h', '2h', '3h', '4h', '5h', '6h', '8h', '10h', '12h', '24h',
                '1d', '2d', '3d', '4d', '5d', '6d', '7d', '14d', '30d',
                '1w', '2w', '3w', '4w',
                '1y', '2y', '3y', '4y', '5y', '10y',

                // English
                'seconds ago', 'second ago', 'minutes ago', 'minute ago', 'hours ago', 'hour ago',
                'yesterday at', 'yesterday', 'days ago', 'day ago', 'weeks ago', 'week ago',
                'months ago', 'month ago', 'years ago', 'year ago', 'just now',

                // Arabic
                'ثانية', 'ثواني', 'دقيقة', 'دقائق', 'ساعة', 'ساعات', 'يوم', 'أيام',
                'أسبوع', 'أسابيع', 'شهر', 'أشهر', 'سنة', 'سنوات', 'الأمس', 'البارحة', 'منذ',

                // Short time formats (Arabic)
                'ث', 'د', 'س', 'ي', 'أس', 'ش', 'سن'
            ];

            // Regular expressions to match common time formats
            const timeRegexes = [
                /\b\d+\s*s\b/i,      // 10s, 20 s
                /\b\d+\s*m\b/i,      // 12m, 30 m
                /\b\d+\s*h\b/i,      // 1h, 2 h
                /\b\d+\s*d\b/i,      // 3d, 5 d
                /\b\d+\s*w\b/i,      // 2w, 3 w
                /\b\d+\s*mo\b/i,     // 1mo, 6 mo
                /\b\d+\s*y\b/i,      // 1y, 10 y
                /\b\d+\s*min\b/i,    // 5min, 10 min
                /\b\d+\s*hr\b/i,     // 1hr, 2 hr
                /\b\d+\s*sec\b/i,    // 30sec, 45 sec
                /\b\d+\s*day\b/i,    // 1day, 2 day
                /\b\d+\s*week\b/i,   // 1week, 3 week
                /\b\d+\s*month\b/i,  // 1month, 6 month
                /\b\d+\s*year\b/i,   // 1year, 5 year
                /\b\d+\s*دقيقة\b/i,  // Arabic minutes
                /\b\d+\s*ساعة\b/i,   // Arabic hours
                /\b\d+\s*يوم\b/i,    // Arabic days
                /\b\d+\s*شهر\b/i,    // Arabic months
                /\b\d+\s*سنة\b/i     // Arabic years
            ];

            for (const postElement of postElements) {
                try {
                    // Skip if we've already processed this element
                    if (state.processedElements.has(postElement)) continue;

                    // Mark as processed
                    state.processedElements.add(postElement);

                    // Try different methods to extract the post link

                    // Method 1: Look for date/time links using standard selectors
                    const dateLinks = postElement.querySelectorAll('a[href] abbr[data-utime], a[href] span[data-sigil="timestamp"], a[role="link"] span[data-testid="post-time"]');
                    for (const dateLink of dateLinks) {
                        const linkElement = dateLink.closest('a[href]');
                        if (linkElement && linkElement.href) {
                            const url = this.cleanPostUrl(linkElement.href);
                            if (url && this.isPostLink(url)) {
                                console.log(`Found post link via date element: ${url}`);
                                postLinks.push(url);
                                break; // Found a link for this post, move to next
                            }
                        }
                    }

                    // Method 1.5: Look for date/time links by text content (e.g., "12m", "1h", etc.)
                    if (postLinks.length === 0) {
                        // Get all spans and small text elements that might contain time
                        const potentialTimeElements = postElement.querySelectorAll('span, a, div');

                        for (const element of potentialTimeElements) {
                            // Skip elements that are too large to be time indicators
                            if (element.textContent.length > 20) continue;

                            const text = element.textContent.trim();

                            // Check if the text matches any time pattern
                            const isTimePattern = timePatterns.some(pattern =>
                                text.toLowerCase().includes(pattern.toLowerCase())
                            );

                            // Check if the text matches any time regex
                            const isTimeRegex = timeRegexes.some(regex =>
                                regex.test(text)
                            );

                            if (isTimePattern || isTimeRegex) {
                                // Found a time element, look for the closest link
                                let linkElement = element.closest('a[href]');

                                // If no direct parent link, try to find a nearby link
                                if (!linkElement) {
                                    // Try parent elements up to 3 levels
                                    let parent = element.parentElement;
                                    for (let i = 0; i < 3 && parent; i++) {
                                        const links = parent.querySelectorAll('a[href]');
                                        if (links.length > 0) {
                                            linkElement = links[0];
                                            break;
                                        }
                                        parent = parent.parentElement;
                                    }

                                    // If still no link, try siblings
                                    if (!linkElement && element.parentElement) {
                                        const siblings = element.parentElement.children;
                                        for (const sibling of siblings) {
                                            if (sibling.tagName === 'A' && sibling.href) {
                                                linkElement = sibling;
                                                break;
                                            }
                                        }
                                    }
                                }

                                if (linkElement && linkElement.href) {
                                    const url = this.cleanPostUrl(linkElement.href);
                                    if (url && this.isPostLink(url)) {
                                        console.log(`Found post link via time text "${text}": ${url}`);
                                        postLinks.push(url);
                                        break; // Found a link for this post, move to next
                                    }
                                }
                            }
                        }
                    }

                    // Method 2: Look for post ID in attributes
                    if (postLinks.length === 0) {
                        const postIdAttrs = ['data-ft', 'data-store', 'data-testid', 'id'];
                        for (const attr of postIdAttrs) {
                            const attrValue = postElement.getAttribute(attr);
                            if (attrValue) {
                                // Try to extract post ID from attribute
                                const postIdMatch = attrValue.match(/(?:post|story)(?:_)?(?:id|fbid)[":=]?(\d+)/i);
                                if (postIdMatch && postIdMatch[1]) {
                                    const postId = postIdMatch[1];

                                    // Try to extract group ID from URL or page
                                    const groupIdMatch = window.location.href.match(/groups\/([^\/\?]+)/);
                                    if (groupIdMatch && groupIdMatch[1]) {
                                        const groupId = groupIdMatch[1];
                                        const url = `https://www.facebook.com/groups/${groupId}/posts/${postId}`;
                                        console.log(`Constructed post link from attributes: ${url}`);
                                        postLinks.push(url);
                                        break; // Found a link for this post, move to next
                                    }
                                }
                            }
                        }
                    }

                    // Method 3: Look for any links that might be post links
                    if (postLinks.length === 0) {
                        const allLinks = postElement.querySelectorAll('a[href]');
                        for (const link of allLinks) {
                            const url = this.cleanPostUrl(link.href);
                            if (url && this.isPostLink(url)) {
                                console.log(`Found post link via general link search: ${url}`);
                                postLinks.push(url);
                                break; // Found a link for this post, move to next
                            }
                        }
                    }
                } catch (e) {
                    console.error('Error extracting post link:', e);
                }
            }

            return postLinks;
        },

        isPostLink: function(url) {
            if (!url) return false;

            // Skip messenger links and notification links
            if (url.includes('/messages/') || url.includes('/notifications/')) {
                return false;
            }

            // Check if it's a post link using various patterns
            return url.includes('/posts/') ||
                   url.includes('/permalink/') ||
                   url.includes('story_fbid=') ||
                   url.includes('fbid=') ||
                   url.includes('/photo.php') ||
                   url.includes('/video.php') ||
                   url.includes('/reel/') ||
                   url.includes('/watch/') ||
                   (url.includes('/groups/') && url.includes('/feed/') && url.includes('item_id=')) ||
                   (url.includes('/groups/') && url.includes('/permalink/')) ||
                   (url.includes('/groups/') && url.includes('/feed/item/')) ||
                   // Match post ID patterns in the URL
                   /\/\d{15,}/.test(url) || // Long numeric IDs are usually post IDs
                   /pfbid[\w]{22,}/.test(url); // pfbid format used in newer Facebook URLs
        },

        cleanPostUrl: function(url) {
            if (!url) return null;

            try {
                // Create URL object to manipulate the URL
                const urlObj = new URL(url);

                // First, extract the group ID and post ID if present in the URL
                let groupId = null;
                let postId = null;

                // Extract group ID from URL path
                const groupMatch = urlObj.pathname.match(/\/groups\/([^\/\?]+)/);
                if (groupMatch && groupMatch[1]) {
                    groupId = groupMatch[1];
                }

                // Extract post ID using different patterns
                if (urlObj.pathname.includes('/posts/')) {
                    // Format: /groups/{groupId}/posts/{postId}
                    const postMatch = urlObj.pathname.match(/\/posts\/([^\/\?]+)/);
                    if (postMatch && postMatch[1]) {
                        postId = postMatch[1];
                    }
                } else if (urlObj.pathname.includes('/permalink/')) {
                    // Format: /groups/{groupId}/permalink/{postId}
                    const postMatch = urlObj.pathname.match(/\/permalink\/([^\/\?]+)/);
                    if (postMatch && postMatch[1]) {
                        postId = postMatch[1];
                    }
                } else if (urlObj.searchParams.has('story_fbid')) {
                    // Format: ?story_fbid={postId}&id={groupId}
                    postId = urlObj.searchParams.get('story_fbid');
                    if (!groupId && urlObj.searchParams.has('id')) {
                        groupId = urlObj.searchParams.get('id');
                    }
                } else if (urlObj.pathname.includes('/photo.php') || urlObj.pathname.includes('/video.php')) {
                    // Format: /photo.php?fbid={postId} or /video.php?fbid={postId}
                    if (urlObj.searchParams.has('fbid')) {
                        postId = urlObj.searchParams.get('fbid');
                    }
                } else if (urlObj.pathname.includes('/feed/')) {
                    // Format: /groups/{groupId}/feed/?item_id={postId}
                    if (urlObj.searchParams.has('item_id')) {
                        postId = urlObj.searchParams.get('item_id');
                    }
                }

                // If we have both group ID and post ID, construct the canonical URL
                if (groupId && postId) {
                    return `${urlObj.origin}/groups/${groupId}/posts/${postId}`;
                }

                // If we only have post ID but no group ID, try to get group ID from current page URL
                if (postId && !groupId) {
                    const currentUrl = window.location.href;
                    const currentGroupMatch = currentUrl.match(/\/groups\/([^\/\?]+)/);
                    if (currentGroupMatch && currentGroupMatch[1]) {
                        groupId = currentGroupMatch[1];
                        return `${urlObj.origin}/groups/${groupId}/posts/${postId}`;
                    }
                }

                // Special case for URLs that already have the correct format but might have tracking params
                if (urlObj.pathname.includes('/groups/') && urlObj.pathname.includes('/posts/')) {
                    // Just clean the URL by removing tracking parameters
                    return `${urlObj.origin}${urlObj.pathname}`;
                }

                // If we couldn't extract both group ID and post ID, return the original URL with tracking removed
                // Remove tracking parameters
                let cleanSearch = urlObj.search;
                const trackingParams = ['fbclid', '__cft__', '__tn__'];

                for (const param of trackingParams) {
                    // Remove parameter if it's the first one (with ?)
                    const firstParamRegex = new RegExp(`\\?${param}=[^&]+(&?)`, 'g');
                    cleanSearch = cleanSearch.replace(firstParamRegex, (match, p1) => p1 ? '?' : '');

                    // Remove parameter if it's not the first one (with &)
                    const otherParamRegex = new RegExp(`&${param}=[^&]+`, 'g');
                    cleanSearch = cleanSearch.replace(otherParamRegex, '');
                }

                // Remove hash fragments (often used for tracking)
                urlObj.hash = '';
                urlObj.search = cleanSearch;

                return urlObj.toString();
            } catch (e) {
                console.error('Error cleaning URL:', e);
                return url;
            }
        }
    };

    // Main extraction functions
    const extractor = {
        extractNextBatch: async function() {
            console.log('Extracting next batch of posts...');

            // Find post elements
            const postElements = helpers.findPostElements();
            console.log(`Found ${postElements.length} post elements to process`);

            if (postElements.length === 0) {
                state.failureCount++;
                console.log(`No post elements found (failure ${state.failureCount}/${config.maxFailures})`);

                // Try recovery if we've had too many failures
                if (state.failureCount >= config.maxFailures) {
                    console.log('Too many failures, trying recovery...');

                    // Click "See More" buttons
                    const clickCount = await helpers.clickSeeMoreButtons();
                    console.log(`Clicked ${clickCount} "See More" buttons`);

                    // Reset failure count
                    state.failureCount = 0;
                }

                // Scroll down to load more content
                await helpers.scrollDown();
                return [];
            }

            // Reset failure count on success
            state.failureCount = 0;

            // Extract post links from the elements
            const newLinks = helpers.extractPostLinks(postElements);
            console.log(`Extracted ${newLinks.length} post links`);

            // Filter out duplicates
            const uniqueNewLinks = newLinks.filter(url => !state.collectedUrls.includes(url));
            console.log(`Found ${uniqueNewLinks.length} new unique post links`);

            // Add to collected URLs
            state.collectedUrls.push(...uniqueNewLinks);

            // Scroll down to load more content
            await helpers.scrollDown();

            return uniqueNewLinks;
        }
    };

    // Public API
    return {
        // Initialize the extractor
        init: function(options) {
            // Apply custom options
            if (options) {
                Object.assign(config, options);
                console.log('Applied custom options:', options);
            }

            // Reset state
            state.collectedUrls = [];
            state.processedElements = new Set();
            state.failureCount = 0;
            state.isRunning = false;
            state.startTime = null;
            state.lastScrollPosition = 0;

            console.log('Facebook Direct Post Extractor initialized with config:', config);

            return this;
        },

        // Start the extraction process
        start: async function() {
            console.log('Starting direct post extraction process...');

            // Reset state
            state.collectedUrls = [];
            state.processedElements = new Set();
            state.failureCount = 0;
            state.isRunning = true;
            state.startTime = Date.now();

            // Click "See More" buttons before starting
            await helpers.clickSeeMoreButtons();

            while (state.isRunning && state.collectedUrls.length < config.maxPosts) {
                // Extract next batch of posts
                const newUrls = await extractor.extractNextBatch();

                // Log progress
                console.log(`Total collected: ${state.collectedUrls.length}/${config.maxPosts}`);

                // If we didn't find any new URLs and we've scrolled enough, stop
                if (newUrls.length === 0 && state.failureCount >= config.maxFailures) {
                    console.log('No new URLs found after multiple attempts, stopping extraction');
                    break;
                }

                // Add a delay between batches
                await helpers.randomDelay();
            }

            // Log completion
            const elapsedSeconds = Math.round((Date.now() - state.startTime) / 1000);
            console.log(`Extraction complete! Collected ${state.collectedUrls.length} unique URLs in ${elapsedSeconds} seconds`);

            return state.collectedUrls;
        },

        // Stop the extraction process
        stop: function() {
            state.isRunning = false;
            console.log('Stopping extraction process...');
        },

        // Get the collected URLs
        getUrls: function() {
            return state.collectedUrls;
        },

        // Process a single batch and return the results
        extractNextBatch: async function() {
            if (state.collectedUrls.length >= config.maxPosts) {
                console.log(`Already reached maximum post count (${config.maxPosts})`);
                return [];
            }

            // Extract next batch of posts
            const newUrls = await extractor.extractNextBatch();

            return newUrls;
        },

        // Update configuration
        setConfig: function(newConfig) {
            Object.assign(config, newConfig);
            console.log('Updated configuration:', config);
            return true;
        }
    };
})();

// Make the extractor available globally
window.FacebookDirectPostExtractor = FacebookDirectPostExtractor;
console.log('Facebook Direct Post Extractor initialized and available globally');
