/**
 * Facebook Member Extractor
 * 
 * This script extracts members from Facebook groups by navigating to the members page
 * and scrolling to load more members.
 */

function initFacebookMemberExtractor() {
    'use strict';
    
    // Store extracted members
    const extractedMembers = [];
    const seenMemberIds = new Set();
    
    // Configuration
    const config = {
        scrollDelay: 1500,       // Delay between scrolls in ms
        scrollStep: 800,         // Scroll step in pixels
        maxScrolls: 50,          // Maximum number of scrolls
        maxMembers: 1000,        // Maximum number of members to extract
        waitAfterScroll: 1000,   // Wait time after scroll in ms
        debug: false             // Debug mode
    };
    
    // Helper functions
    const helpers = {
        // Delay function
        delay: function(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        },
        
        // Random delay function
        randomDelay: function() {
            const min = config.scrollDelay * 0.8;
            const max = config.scrollDelay * 1.2;
            const delay = Math.floor(Math.random() * (max - min + 1)) + min;
            return this.delay(delay);
        },
        
        // Scroll function
        scroll: async function() {
            const currentPos = window.scrollY;
            const remaining = document.documentElement.scrollHeight - window.innerHeight - currentPos;
            
            if (remaining > 0) {
                const scrollTo = currentPos + Math.min(remaining, config.scrollStep);
                window.scrollTo({
                    top: scrollTo,
                    behavior: 'smooth'
                });
                
                // Wait for content to load
                await this.delay(config.waitAfterScroll);
                
                // Check if we're at the bottom
                if (window.scrollY + window.innerHeight >= document.documentElement.scrollHeight - 10) {
                    console.log('Reached bottom of page');
                    return false;
                }
                
                return true;
            }
            
            return false;
        },
        
        // Click "See More" buttons
        clickSeeMoreButtons: function() {
            const seeMoreButtons = document.querySelectorAll('div[role="button"]:not([aria-hidden="true"]):not([aria-disabled="true"]), span[role="button"]');
            let clicked = 0;
            
            for (const button of seeMoreButtons) {
                const text = button.textContent.toLowerCase();
                if (text.includes('see more') || text.includes('show more') || text.includes('see all')) {
                    try {
                        button.click();
                        clicked++;
                        console.log('Clicked "See More" button');
                    } catch (e) {
                        console.error('Error clicking button:', e);
                    }
                }
            }
            
            return clicked;
        },
        
        // Navigate to members page
        navigateToMembersPage: async function() {
            // Check if we're already on a members page
            if (window.location.href.includes('/members')) {
                console.log('Already on members page');
                return true;
            }
            
            // Try to find members link
            const membersLinks = Array.from(document.querySelectorAll('a[href*="/members"]'));
            
            if (membersLinks.length > 0) {
                // Find the most likely members link
                const membersLink = membersLinks.find(link => {
                    const text = link.textContent.toLowerCase();
                    return text.includes('member') || text.includes('members');
                }) || membersLinks[0];
                
                console.log('Found members link:', membersLink.href);
                
                // Navigate to members page
                window.location.href = membersLink.href;
                
                // Wait for page to load
                await this.delay(5000);
                return true;
            }
            
            // Try to construct members URL
            try {
                const groupId = window.location.href.match(/groups\/(\d+)/)?.[1];
                if (groupId) {
                    const membersUrl = `https://www.facebook.com/groups/${groupId}/members`;
                    console.log('Constructed members URL:', membersUrl);
                    
                    // Navigate to members page
                    window.location.href = membersUrl;
                    
                    // Wait for page to load
                    await this.delay(5000);
                    return true;
                }
            } catch (e) {
                console.error('Error constructing members URL:', e);
            }
            
            console.error('Could not navigate to members page');
            return false;
        }
    };
    
    // Member extraction functions
    const extractors = {
        // Find member elements
        findMemberElements: function() {
            // Different selectors to try
            const selectors = [
                // Modern Facebook group members list
                'div[data-pagelet="GroupProfileGrid"] > div > div > div',
                // Member rows in the members list
                'div[role="row"]',
                // Generic member containers
                'div.x1qughib.x1yc453h',
                // Fallback to any div with a link to a profile
                'div:has(a[href*="/user/"]), div:has(a[href*="/profile.php"])'
            ];
            
            let results = [];
            
            // Try each selector
            for (const selector of selectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        console.log(`Found ${elements.length} member elements with selector: ${selector}`);
                        results = Array.from(elements);
                        break;
                    }
                } catch (e) {
                    console.error(`Error with selector ${selector}:`, e);
                }
            }
            
            return results;
        },
        
        // Extract member data from element
        extractMemberData: function(element) {
            try {
                // Find profile link
                const profileLink = element.querySelector('a[href*="/user/"], a[href*="/profile.php"], a[href*="facebook.com/"]');
                
                if (!profileLink) {
                    return null;
                }
                
                const profileUrl = profileLink.href;
                
                // Extract member ID from profile URL
                let memberId = '';
                
                // Try different URL patterns
                if (profileUrl.includes('/user/')) {
                    memberId = profileUrl.match(/\/user\/([^\/\?]+)/)?.[1] || '';
                } else if (profileUrl.includes('/profile.php')) {
                    memberId = profileUrl.match(/[?&]id=(\d+)/)?.[1] || '';
                } else {
                    // Extract username from facebook.com/username
                    const usernameMatch = profileUrl.match(/facebook\.com\/([^\/\?]+)/);
                    if (usernameMatch && !['groups', 'pages', 'events', 'marketplace'].includes(usernameMatch[1])) {
                        memberId = usernameMatch[1];
                    }
                }
                
                if (!memberId) {
                    return null;
                }
                
                // Extract member name
                const nameElement = profileLink.querySelector('span') || profileLink;
                const memberName = nameElement.textContent.trim();
                
                // Extract additional info if available
                const roleElement = element.querySelector('div[aria-label*="admin"], div[aria-label*="moderator"]');
                const role = roleElement ? roleElement.getAttribute('aria-label') : '';
                
                return {
                    id: memberId,
                    name: memberName,
                    profileUrl: profileUrl,
                    role: role
                };
            } catch (e) {
                console.error('Error extracting member data:', e);
                return null;
            }
        },
        
        // Extract all visible members
        extractVisibleMembers: function() {
            const memberElements = this.findMemberElements();
            const newMembers = [];
            
            for (const element of memberElements) {
                try {
                    const memberData = this.extractMemberData(element);
                    
                    if (memberData && memberData.id && !seenMemberIds.has(memberData.id)) {
                        // Add to results
                        extractedMembers.push(memberData);
                        seenMemberIds.add(memberData.id);
                        newMembers.push(memberData);
                        
                        if (config.debug) {
                            console.log('Extracted member:', memberData);
                        }
                    }
                } catch (e) {
                    console.error('Error processing member element:', e);
                }
            }
            
            return newMembers;
        }
    };
    
    // Main extraction function
    async function extractMembers(options = {}) {
        // Apply options
        Object.assign(config, options);
        
        console.log('Starting member extraction with config:', config);
        
        // Navigate to members page if needed
        const onMembersPage = await helpers.navigateToMembersPage();
        if (!onMembersPage) {
            console.error('Failed to navigate to members page');
            return { success: false, members: [], error: 'Failed to navigate to members page' };
        }
        
        // Reset extraction state
        extractedMembers.length = 0;
        seenMemberIds.clear();
        
        // Initial extraction
        console.log('Extracting initial members...');
        const initialMembers = extractors.extractVisibleMembers();
        console.log(`Initially found ${initialMembers.length} members`);
        
        // Scroll and extract more members
        let scrollCount = 0;
        let noNewMembersCount = 0;
        
        while (scrollCount < config.maxScrolls && extractedMembers.length < config.maxMembers) {
            // Scroll down
            console.log(`Scroll ${scrollCount + 1}/${config.maxScrolls}`);
            const scrolled = await helpers.scroll();
            
            if (!scrolled) {
                console.log('Could not scroll further');
                break;
            }
            
            // Click "See More" buttons
            helpers.clickSeeMoreButtons();
            
            // Wait for content to load
            await helpers.randomDelay();
            
            // Extract new members
            const newMembers = extractors.extractVisibleMembers();
            console.log(`Found ${newMembers.length} new members (total: ${extractedMembers.length})`);
            
            // Check if we found new members
            if (newMembers.length === 0) {
                noNewMembersCount++;
                console.log(`No new members found (${noNewMembersCount}/3)`);
                
                // If we haven't found new members in 3 consecutive scrolls, stop
                if (noNewMembersCount >= 3) {
                    console.log('No new members found in 3 consecutive scrolls, stopping extraction');
                    break;
                }
            } else {
                noNewMembersCount = 0;
            }
            
            // Check if we've reached the maximum number of members
            if (extractedMembers.length >= config.maxMembers) {
                console.log(`Reached maximum number of members (${config.maxMembers}), stopping extraction`);
                break;
            }
            
            scrollCount++;
        }
        
        console.log(`Extraction completed. Found ${extractedMembers.length} members`);
        
        return {
            success: true,
            members: extractedMembers,
            count: extractedMembers.length
        };
    }
    
    // Return public API
    return {
        extractMembers,
        getExtractedMembers: () => extractedMembers,
        getMemberCount: () => extractedMembers.length,
        setConfig: (newConfig) => Object.assign(config, newConfig),
        getConfig: () => ({ ...config })
    };
}

// Export the function for use in Selenium
if (typeof module !== 'undefined') {
    module.exports = initFacebookMemberExtractor;
}
