/**
 * Facebook Posts Scraper
 *
 * This script extracts Facebook posts data including:
 * - Author
 * - Content
 * - Timestamp
 * - Reactions
 * - Media links
 * - Post URL
 */

function initFacebookScraper(options) {
    // تهيئة الخيارات
    options = options || {};

    console.log('Initializing Facebook Scraper with options:', options);

    // إعدادات التكوين - محسنة للأداء
    const config = {
        // خطوة التمرير بالبكسل - زيادة للتمرير بشكل أسرع
        scrollStep: options.scrollStep || 1000,

        // تأخير التمرير بالملي ثانية - تقليل للتمرير بشكل أسرع
        scrollDelay: options.scrollDelay || 1000,

        // الحد الأقصى لعدد مرات التمرير - زيادة لاستخراج المزيد من المنشورات
        maxScrolls: options.maxScrolls || 10,

        // الحد الأقصى لعدد مرات التمرير بدون تغيير قبل التوقف
        maxSameHeightCount: options.maxSameHeightCount || 3,

        // مدة الانتظار بعد التمرير بالملي ثانية
        waitAfterScroll: options.waitAfterScroll || 1000,

        // حجم الدفعة لمعالجة المنشورات
        batchSize: options.batchSize || 10,

        // وضع التصحيح
        debug: options.debug || false,

        // استخراج عدواني
        aggressiveExtraction: options.aggressiveExtraction || true
    };

    // تسجيل الإعدادات النهائية
    console.log('Facebook Scraper configured with:', config);

    // المحددات الذكية - سيتم تحديثها من المحددات المكتشفة
    let selectors = {
        post: 'div[role="article"]',
        author: 'a[role="link"][tabindex="0"], a.x1i10hfl',
        content: 'div[data-ad-preview="message"], div.xdj266r',
        time: 'a[href*="/posts/"] > abbr, a.x1i10hfl.xjbqb8w',
        reactions: 'span.x193iq5w, div.x1n2onr6.x1vqgdyp',
        media: 'img.x1ey2m1c.xds687c.x5yr21d',
        postLink: 'a[href*="/posts/"], a[href*="/permalink/"]'
    };

    // محاولة استخدام المحددات المكتشفة إذا كانت متاحة
    if (window.facebookPostSelectorDetector && window.facebookPostSelectorDetector.getDetectedSelectors) {
        try {
            const detectedSelectors = window.facebookPostSelectorDetector.getDetectedSelectors();
            if (detectedSelectors && detectedSelectors.selectors) {
                console.log('Using detected selectors:', detectedSelectors.selectors);
                // تحديث المحددات بالمحددات المكتشفة
                for (const key in detectedSelectors.selectors) {
                    if (detectedSelectors.selectors[key] && detectedSelectors.selectors[key].length > 0) {
                        selectors[key] = detectedSelectors.selectors[key].join(', ');
                    }
                }
            }
        } catch (e) {
            console.error('Error using detected selectors:', e);
        }
    }

    // وظيفة التمرير التلقائي المحسنة
    async function autoScroll(iterations) {
        // Use default value if not provided
        iterations = iterations || config.maxScrolls;
        return new Promise(resolve => {
            let scrolled = 0;
            let lastHeight = document.documentElement.scrollHeight;
            let sameHeightCount = 0;

            // وظيفة للنقر على أزرار "عرض المزيد"
            const clickSeeMoreButtons = () => {
                try {
                    // البحث عن أزرار "عرض المزيد"
                    const seeMoreButtons = document.querySelectorAll(
                        'div[role="button"][tabindex="0"], a[role="button"], span[role="button"]'
                    );

                    let clicked = false;
                    for (const button of seeMoreButtons) {
                        const text = button.textContent.toLowerCase();
                        if (text.includes('see more') || text.includes('show more') ||
                            text.includes('view more') || text.includes('see all')) {
                            try {
                                // النقر فقط إذا كان الزر مرئيًا
                                const rect = button.getBoundingClientRect();
                                if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
                                    button.click();
                                    console.log('Clicked a "See More" button');
                                    clicked = true;
                                    break;
                                }
                            } catch (e) {
                                console.error('Error clicking button:', e);
                            }
                        }
                    }
                    return clicked;
                } catch (e) {
                    console.error('Error trying to click "See More" buttons:', e);
                    return false;
                }
            };

            // وظيفة لعد المنشورات الحالية
            const countCurrentPosts = () => {
                return document.querySelectorAll(selectors.post).length;
            };

            // التمرير بشكل أسرع
            const scrollInterval = setInterval(() => {
                // النقر على أزرار "عرض المزيد" قبل التمرير
                clickSeeMoreButtons();

                // التمرير للأسفل
                window.scrollBy(0, config.scrollStep);
                scrolled++;

                // التحقق من ارتفاع الصفحة
                const currentHeight = document.documentElement.scrollHeight;
                if (currentHeight === lastHeight) {
                    sameHeightCount++;
                } else {
                    sameHeightCount = 0;
                    lastHeight = currentHeight;
                }

                // عد المنشورات الحالية
                const currentPostCount = countCurrentPosts();

                // التوقف إذا تم الوصول إلى الحد الأقصى من التمريرات
                // أو إذا لم يتغير ارتفاع الصفحة لعدة تمريرات
                if (scrolled >= iterations || sameHeightCount >= 3) {
                    // محاولة أخيرة للنقر على أزرار "عرض المزيد"
                    clickSeeMoreButtons();

                    clearInterval(scrollInterval);
                    console.log(`Scrolling finished after ${scrolled} scrolls with ${currentPostCount} posts found`);

                    // الانتظار قليلاً قبل الانتهاء للسماح بتحميل أي محتوى متبقي
                    setTimeout(resolve, 1000);
                }
            }, config.scrollDelay);
        });
    }

    // استخراج رابط المنشور بشكل مباشر
    function extractPostUrlDirectly(post) {
        try {
            // البحث عن جميع الروابط في المنشور
            const allLinks = post.querySelectorAll('a[href]');
            let postUrl = null;

            // البحث عن روابط المنشورات باستخدام أنماط متعددة
            const urlPatterns = [
                { pattern: /\/posts\/\d+/, priority: 1 },
                { pattern: /\/permalink\/\d+/, priority: 2 },
                { pattern: /story_fbid=\d+/, priority: 3 },
                { pattern: /fbid=\d+/, priority: 4 },
                { pattern: /photo\.php\?fbid=\d+/, priority: 5 },
                { pattern: /photo\/\?fbid=\d+/, priority: 6 },
                { pattern: /videos\/\d+/, priority: 7 }
            ];

            // تخزين الروابط المطابقة مع أولوياتها
            const matchedLinks = [];

            for (const link of allLinks) {
                const href = link.href;
                if (!href) continue;

                for (const { pattern, priority } of urlPatterns) {
                    if (href.match(pattern)) {
                        matchedLinks.push({ url: href, priority });
                        break;
                    }
                }
            }

            // فرز الروابط حسب الأولوية
            matchedLinks.sort((a, b) => a.priority - b.priority);

            // استخدام الرابط ذو الأولوية الأعلى
            if (matchedLinks.length > 0) {
                postUrl = matchedLinks[0].url;
                console.log(`Found post URL directly: ${postUrl}`);
            }

            // تنظيف الرابط إذا تم العثور عليه
            if (postUrl) {
                try {
                    // التحقق من أن الرابط ليس رابط صورة مباشرة أو فيديو
                    if (postUrl.includes('/photo.php') ||
                        postUrl.includes('/photo/?fbid=') ||
                        postUrl.includes('/video.php') ||
                        postUrl.includes('/watch/') ||
                        postUrl.includes('/reel/')) {

                        // استخراج معرف المجموعة من URL الحالي
                        const currentUrl = window.location.href;
                        const groupIdMatch = currentUrl.match(/groups\/([^\/\?]+)/);
                        const groupId = groupIdMatch ? groupIdMatch[1] : null;

                        // استخراج معرف الصورة أو الفيديو
                        let fbid = null;
                        if (postUrl.includes('fbid=')) {
                            const fbidMatch = postUrl.match(/fbid=([^&]+)/);
                            fbid = fbidMatch ? fbidMatch[1] : null;
                        } else if (postUrl.includes('v=')) {
                            const vMatch = postUrl.match(/v=([^&]+)/);
                            fbid = vMatch ? vMatch[1] : null;
                        }

                        // إذا كان لدينا كل من معرف المجموعة ومعرف الصورة/الفيديو، يمكننا إنشاء رابط المنشور
                        if (groupId && fbid) {
                            postUrl = `https://web.facebook.com/groups/${groupId}/posts/${fbid}`;
                            console.log(`Converted media URL to post URL: ${postUrl}`);
                        } else {
                            // إذا لم نتمكن من تحويل الرابط، نتجاهله
                            console.log(`Could not convert media URL to post URL: ${postUrl}`);
                            return null; // رفض الرابط
                        }
                    } else {
                        const url = new URL(postUrl);

                        // التحقق من أن الرابط هو رابط منشور
                        if (url.pathname.includes('/posts/') || url.pathname.includes('/permalink/')) {
                            // الاحتفاظ فقط بالأجزاء الأساسية من الرابط
                            postUrl = `${url.origin}${url.pathname}`;
                        } else if (url.searchParams.has('story_fbid') && url.searchParams.has('id')) {
                            // تحويل روابط story_fbid إلى روابط قياسية
                            const storyFbid = url.searchParams.get('story_fbid');
                            const id = url.searchParams.get('id');
                            postUrl = `${url.origin}/groups/${id}/posts/${storyFbid}`;
                        } else if (!url.pathname.includes('/posts/') && !url.pathname.includes('/permalink/') && !url.searchParams.has('story_fbid')) {
                            // إذا لم يكن الرابط يحتوي على '/posts/' أو '/permalink/' أو 'story_fbid'، فهو ليس رابط منشور
                            console.log(`Not a post URL: ${postUrl}`);
                            return null; // رفض الرابط
                        }

                        // التحقق من أن الرابط لا يحتوي على معرفات تتبع أو معلمات إضافية
                        if (url.search && url.search.includes('__cft__')) {
                            // إزالة معلمات التتبع
                            url.search = '';
                            postUrl = url.toString();
                        }
                    }
                } catch (e) {
                    console.error("Error cleaning URL:", e);
                    return null; // رفض الرابط في حالة حدوث خطأ
                }
            }

            return postUrl;
        } catch (e) {
            console.error("Error extracting post URL directly:", e);
            return null;
        }
    }

    // استخراج بيانات المنشور - نسخة محسنة
    function extractPostData(post) {
        // وظيفة مساعدة لاستخراج البيانات من عنصر واحد
        const getData = function(selector, attr) {
            // Use default value if not provided
            attr = attr || 'text';
            try {
                const element = post.querySelector(selector);
                if (!element) return null;

                if (attr === 'text') return element.textContent.trim();
                if (attr === 'src') return element.getAttribute('src');
                if (attr === 'datetime') return element.getAttribute('datetime') || element.getAttribute('data-utime');
                if (attr === 'href') return element.getAttribute('href');

                return null;
            } catch (e) {
                console.error(`Error getting data with selector ${selector}:`, e);
                return null;
            }
        };

        // وظيفة مساعدة لاستخراج البيانات من جميع العناصر المطابقة
        const getAllData = function(selector, attr) {
            // Use default value if not provided
            attr = attr || 'text';
            try {
                const elements = post.querySelectorAll(selector);
                if (!elements || elements.length === 0) return [];

                const results = [];
                for (const element of elements) {
                    if (attr === 'text') {
                        const text = element.textContent.trim();
                        if (text) results.push(text);
                    } else if (attr === 'href') {
                        const href = element.getAttribute('href');
                        if (href) results.push(href);
                    } else if (attr === 'src') {
                        const src = element.getAttribute('src');
                        if (src) results.push(src);
                    }
                }
                return results;
            } catch (e) {
                console.error(`Error getting all data with selector ${selector}:`, e);
                return [];
            }
        };

        // استخراج رابط المنشور - طريقة محسنة
        let postUrl = null;

        // طريقة 1: استخدام المحدد الرئيسي
        // استخدام جميع المحددات الممكنة للروابط
        const possibleLinkSelectors = [
            selectors.postLink,
            'a[href*="/posts/"]',
            'a[href*="/permalink/"]',
            'a[href*="story_fbid="]',
            'a[href*="fbid="]',
            'a[role="link"][href*="/posts/"]',
            'a[role="link"][href*="/permalink/"]',
            'span.x4k7w5x a[href*="/posts/"]',
            'span.x1yrsyyn a[href*="/posts/"]',
            'span.x4k7w5x a[href*="/permalink/"]',
            'span.x1yrsyyn a[href*="/permalink/"]'
        ];

        // تجربة كل محدد حتى نجد رابط
        for (const selector of possibleLinkSelectors) {
            const url = getData(selector, 'href');
            if (url) {
                postUrl = url;
                console.log(`Found post URL using selector ${selector}: ${postUrl}`);
                break;
            }
        }

        // طريقة 2: البحث عن روابط الوقت
        if (!postUrl) {
            const timeLinks = post.querySelectorAll('a[href] abbr, a[href] span[data-sigil="timestamp"]');
            for (const timeLink of timeLinks) {
                const parent = timeLink.closest('a[href]');
                if (parent && parent.href) {
                    postUrl = parent.href;
                    console.log(`Found post URL using timestamp link: ${postUrl}`);
                    break;
                }
            }
        }

        // طريقة 3: البحث عن جميع الروابط المحتملة
        if (!postUrl) {
            // الحصول على جميع الروابط في المنشور
            const allLinks = post.querySelectorAll('a[href]');

            // البحث عن روابط المنشورات
            for (const link of allLinks) {
                const href = link.href;
                if (href && (
                    href.includes('/posts/') ||
                    href.includes('/permalink/') ||
                    href.includes('story_fbid=') ||
                    href.includes('fbid=')
                )) {
                    postUrl = href;
                    console.log(`Found post URL using link search: ${postUrl}`);
                    break;
                }
            }
        }

        // تنظيف الرابط إذا تم العثور عليه
        if (postUrl) {
            try {
                // تحويل روابط الصور إلى روابط المنشورات
                if (postUrl.includes('/photo.php') || postUrl.includes('/photo/?fbid=')) {
                    // استخراج معرف المجموعة من URL الحالي
                    const currentUrl = window.location.href;
                    const groupIdMatch = currentUrl.match(/groups\/([^\/\?]+)/);
                    const groupId = groupIdMatch ? groupIdMatch[1] : null;

                    // استخراج معرف الصورة
                    let fbid = null;
                    if (postUrl.includes('fbid=')) {
                        const fbidMatch = postUrl.match(/fbid=([^&]+)/);
                        fbid = fbidMatch ? fbidMatch[1] : null;
                    }

                    // إذا كان لدينا كل من معرف المجموعة ومعرف الصورة، يمكننا إنشاء رابط المنشور
                    if (groupId && fbid) {
                        postUrl = `https://web.facebook.com/groups/${groupId}/posts/${fbid}`;
                        console.log(`Converted photo URL to post URL: ${postUrl}`);
                    }
                } else {
                    const url = new URL(postUrl);

                    // الاحتفاظ فقط بالأجزاء الأساسية من الرابط
                    if (url.pathname.includes('/posts/') || url.pathname.includes('/permalink/')) {
                        postUrl = `${url.origin}${url.pathname}`;
                    } else if (url.searchParams.has('story_fbid') && url.searchParams.has('id')) {
                        // تحويل روابط story_fbid إلى روابط قياسية
                        const storyFbid = url.searchParams.get('story_fbid');
                        const id = url.searchParams.get('id');
                        postUrl = `${url.origin}/groups/${id}/posts/${storyFbid}`;
                    }
                }
            } catch (e) {
                console.error("Error cleaning URL:", e);
            }
        }

        return {
            author: getData(selectors.author),
            content: getData(selectors.content),
            timestamp: getData(selectors.time, 'datetime') || getData(selectors.time),
            reactions: getData(selectors.reactions),
            media: getData(selectors.media, 'src'),
            url: postUrl,
            elementText: post.textContent.trim().replace(/\s+/g, ' ').substring(0, 200) + '...'
        };
    }

    // استخراج المنشورات المرئية - نسخة محسنة
    function scrapeVisiblePosts() {
        // استخدام محددات متعددة للعثور على المنشورات
        let posts = [];

        // استخدام المحدد الرئيسي
        const mainPosts = Array.from(document.querySelectorAll(selectors.post));
        posts = [...mainPosts];
        console.log(`Found ${mainPosts.length} posts using primary selector`);

        // استخدام محددات بديلة إذا لم يتم العثور على منشورات كافية
        if (posts.length < 5) {
            const altSelectors = [
                'div[role="article"]',
                'div.x1yztbdb',
                'div.x1lliihq',
                'div.x1n2onr6',
                'div.x78zum5',
                'div[data-pagelet*="FeedUnit"]',
                'div.xdj266r'
            ];

            for (const selector of altSelectors) {
                if (!selectors.post.includes(selector)) {
                    const altPosts = Array.from(document.querySelectorAll(selector));
                    if (altPosts.length > 0) {
                        console.log(`Found ${altPosts.length} posts using alternative selector: ${selector}`);
                        posts = [...posts, ...altPosts];
                    }
                }
            }

            // إزالة العناصر المكررة
            posts = Array.from(new Set(posts));
        }

        console.log(`Total posts found: ${posts.length}`);

        // استخراج بيانات المنشورات
        const extractedPosts = [];
        for (const post of posts) {
            try {
                const postData = extractPostData(post);
                if (postData && postData.url) {
                    extractedPosts.push(postData);
                } else if (postData) {
                    // محاولة استخراج الرابط بطريقة مختلفة إذا فشلت الطريقة الأولى
                    const extractedUrl = extractPostUrlDirectly(post);
                    if (extractedUrl) {
                        postData.url = extractedUrl;
                        extractedPosts.push(postData);
                    }
                }
            } catch (e) {
                console.error('Error extracting post data:', e);
            }
        }

        console.log(`Successfully extracted data from ${extractedPosts.length} posts`);
        return extractedPosts;
    }

    // استخراج المنشورات مع التمرير - محسنة للأداء
    async function scrapeWithScrolling(iterations) {
        // Use default value if not provided
        iterations = iterations || 10;
        // تعديل عدد مرات التمرير إذا تم تمريره
        const scrollCount = iterations > 0 ? iterations : config.maxScrolls;

        // التحقق من المنشورات الموجودة قبل التمرير
        const initialPosts = document.querySelectorAll(selectors.post).length;
        console.log(`تم العثور على ${initialPosts} منشور قبل التمرير`);

        // تسجيل المحددات المستخدمة للتشخيص
        console.log('Using post selectors:', selectors);

        // التمرير لتحميل المزيد من المنشورات
        console.log(`بدء التمرير لتحميل المزيد من المنشورات (${scrollCount} مرات)...`);

        try {
            // استخدام مهلة زمنية للتمرير لتجنب المشاكل
            const scrollTimeout = 30000; // 30 ثانية
            const scrollPromise = autoScroll(scrollCount);
            const timeoutPromise = new Promise(resolve => setTimeout(resolve, scrollTimeout));

            // استخدام Promise.race لتجنب الانتظار لفترة طويلة جدًا
            await Promise.race([scrollPromise, timeoutPromise]);

            // التحقق مما إذا كان التمرير قد انتهى بسبب المهلة الزمنية
            const currentPosts = document.querySelectorAll(selectors.post).length;
            if (currentPosts === initialPosts) {
                console.log('انتهت مهلة التمرير دون العثور على منشورات جديدة');
            } else {
                console.log(`تم العثور على ${currentPosts - initialPosts} منشور جديد بعد التمرير`);
            }
        } catch (e) {
            console.error('حدث خطأ أثناء التمرير:', e);
        }

        // استخراج البيانات
        console.log('جاري استخراج البيانات...');

        // استخدام معالجة الدفعات لتجنب المشاكل مع الصفحات الكبيرة
        const allPosts = Array.from(document.querySelectorAll(selectors.post));
        console.log(`تم العثور على ${allPosts.length} منشور إجمالي`);

        // معالجة المنشورات في دفعات لتجنب المشاكل مع الصفحات الكبيرة
        const batchSize = 10; // عدد المنشورات في كل دفعة
        const results = [];

        for (let i = 0; i < allPosts.length; i += batchSize) {
            const batch = allPosts.slice(i, i + batchSize);
            console.log(`معالجة الدفعة ${Math.floor(i / batchSize) + 1}/${Math.ceil(allPosts.length / batchSize)}`);

            // معالجة كل منشور في الدفعة
            for (const post of batch) {
                try {
                    const postData = extractPostData(post);
                    if (postData && postData.url) {
                        results.push(postData);
                    }
                } catch (e) {
                    console.error('حدث خطأ أثناء معالجة المنشور:', e);
                }
            }
        }

        // إزالة المنشورات المكررة
        const uniqueUrls = new Set();
        const uniqueResults = [];

        for (const post of results) {
            if (post.url && !uniqueUrls.has(post.url)) {
                uniqueUrls.add(post.url);
                uniqueResults.push(post);
            }
        }

        // عرض النتائج
        console.log(`تم استخراج ${uniqueResults.length} منشور فريد من أصل ${results.length} منشور`);

        // حفظ النتائج في متغير global للاستخدام في وحدة التحكم
        window.fbScrapedData = uniqueResults;

        return uniqueResults;
    }

    // Return the public API
    return {
        scrapeVisiblePosts,
        scrapeWithScrolling,
        config,
        selectors
    };
}

// Export the function for use in Selenium
if (typeof module !== 'undefined') {
    module.exports = initFacebookScraper;
}
