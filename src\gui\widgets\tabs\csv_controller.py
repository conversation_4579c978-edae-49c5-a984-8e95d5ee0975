import os
import logging
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot

from core.utils.csv_reader import CSVReader

# Setup logger
logger = logging.getLogger('zpammer.gui.csv_controller')

class CSVController(QObject):
    """
    Controller for handling CSV files in the GUI.
    
    This class handles:
    - Loading CSV files
    - Saving CSV files
    - Validating CSV data
    """
    
    # Define signals
    data_loaded = pyqtSignal(list, list)  # headers, data
    log_message = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def load_csv(self, file_path=None, expected_headers=None):
        """
        Load data from a CSV file.
        
        Args:
            file_path (str): Path to the CSV file.
            expected_headers (list, optional): List of expected headers.
            
        Returns:
            tuple: (headers, data) where headers is a list of column names and data is a list of rows.
        """
        if file_path is None:
            file_path = os.path.join("Files", "Accounts.csv")
        
        self.log_message.emit(f"[INFO] Loading CSV file: {file_path}")
        
        headers, data = CSVReader.read_csv(file_path, expected_headers)
        
        if headers:
            self.log_message.emit(f"[INFO] Loaded {len(data)} rows from {file_path}")
            self.data_loaded.emit(headers, data)
        else:
            self.log_message.emit(f"[ERROR] Failed to load CSV file: {file_path}")
        
        return headers, data
    
    def save_csv(self, file_path, headers, data):
        """
        Save data to a CSV file.
        
        Args:
            file_path (str): Path to the CSV file.
            headers (list): List of column names.
            data (list): List of rows.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        self.log_message.emit(f"[INFO] Saving CSV file: {file_path}")
        
        success = CSVReader.write_csv(file_path, headers, data)
        
        if success:
            self.log_message.emit(f"[INFO] Saved {len(data)} rows to {file_path}")
        else:
            self.log_message.emit(f"[ERROR] Failed to save CSV file: {file_path}")
        
        return success
    
    def load_accounts(self, file_path=None):
        """
        Load accounts from a CSV file.
        
        Args:
            file_path (str, optional): Path to the CSV file.
            
        Returns:
            list: List of account dictionaries.
        """
        if file_path is None:
            file_path = os.path.join("Files", "Accounts.csv")
        
        self.log_message.emit(f"[INFO] Loading accounts from: {file_path}")
        
        accounts = CSVReader.read_accounts_csv(file_path)
        
        if accounts:
            self.log_message.emit(f"[INFO] Loaded {len(accounts)} accounts from {file_path}")
        else:
            self.log_message.emit(f"[WARNING] No accounts found in {file_path}")
        
        return accounts
