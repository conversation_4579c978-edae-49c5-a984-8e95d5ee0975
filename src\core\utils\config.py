import os
import json
import logging

# Setup logger
logger = logging.getLogger('zpammer.config')

class Config:
    """
    Configuration manager for Zpammer GUI.

    This class handles:
    - Loading configuration from file
    - Saving configuration to file
    - Providing default configuration values
    """

    DEFAULT_CONFIG = {
        "general": {
            "theme": "light",
            "language": "en",
            "log_level": "info",
            "start_with_windows": False,
            "minimize_to_tray": False,
        },
        "browser": {
            "chrome_path": "",
            "user_data_path": "",
            "chromedriver_path": "",
            "headless_mode": False,
            "auto_close": True,
        },
        "nstbrowser": {
            "api_key": "fc63ee6b-0785-4b2a-a179-d6ae22c88479",  # Default API key
            "host": "127.0.0.1",
            "port": 8848,
            "executable_path": "",
            "profiles_path": "",
            "auto_close": True,
        },
        "spammer": {
            "comments_per_profile": 3,
            "delay": 5,
            "random_typing": True,
            "skip_commented": True,
        },
        "paths": {
            "profiles": "Files/profile_ids.txt",
            "posts": "Files/post_links.txt",
            "comments": "Files/spam_comments.txt",
            "accounts": "Files/Accounts.csv",
            "database": "Files/comments.db",
        },
    }

    def __init__(self, config_path=None):
        """
        Initialize the configuration manager.

        Args:
            config_path (str, optional): Path to the configuration file.
        """
        if config_path is None:
            # Try multiple possible locations for config.json
            possible_paths = [
                os.path.join("data", "config.json"),
                os.path.join("Files", "config.json"),
                "config.json",
                os.path.join("..", "Files", "config.json"),
                "paths_dependencies.json"  # Original file for backward compatibility
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    self.config_path = path
                    break
            else:
                # If no config file found, use the first path as default
                self.config_path = possible_paths[0]
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        else:
            self.config_path = config_path

        self.config = self.load()

    def load(self):
        """
        Load configuration from file.

        Returns:
            dict: Configuration dictionary.
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_path}")

                # Merge with default config to ensure all keys exist
                merged_config = self.DEFAULT_CONFIG.copy()
                self._deep_update(merged_config, config)
                return merged_config
            else:
                logger.warning(f"Configuration file {self.config_path} not found. Using default configuration.")

                # Save default configuration
                try:
                    os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                    with open(self.config_path, "w", encoding="utf-8") as f:
                        json.dump(self.DEFAULT_CONFIG, f, indent=4)
                    logger.info(f"Created default configuration at {self.config_path}")
                except Exception as e:
                    logger.error(f"Failed to create default configuration: {e}")

                return self.DEFAULT_CONFIG.copy()
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return self.DEFAULT_CONFIG.copy()

    def save(self):
        """
        Save configuration to file.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Saved configuration to {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False

    def get(self, section, key, default=None):
        """
        Get a configuration value.

        Args:
            section (str): Configuration section.
            key (str): Configuration key.
            default (any, optional): Default value if key is not found.

        Returns:
            any: Configuration value.
        """
        try:
            return self.config[section][key]
        except KeyError:
            if default is not None:
                return default

            # Try to get from default config
            try:
                return self.DEFAULT_CONFIG[section][key]
            except KeyError:
                logger.warning(f"Configuration key {section}.{key} not found.")
                return None

    def set(self, section, key, value):
        """
        Set a configuration value.

        Args:
            section (str): Configuration section.
            key (str): Configuration key.
            value (any): Configuration value.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            if section not in self.config:
                self.config[section] = {}

            self.config[section][key] = value
            return True
        except Exception as e:
            logger.error(f"Failed to set configuration value: {e}")
            return False

    def _deep_update(self, d, u):
        """
        Recursively update a dictionary.

        Args:
            d (dict): Dictionary to update.
            u (dict): Dictionary with updates.

        Returns:
            dict: Updated dictionary.
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                self._deep_update(d[k], v)
            else:
                d[k] = v
        return d
