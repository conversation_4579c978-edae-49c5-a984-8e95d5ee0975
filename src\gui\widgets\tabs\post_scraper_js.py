"""
JavaScript scripts for post scraper functionality.
"""

import os

# Load the Facebook scraper scripts
FACEBOOK_SCRAPER_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_scraper.js")
with open(FACEBOOK_SCRAPER_SCRIPT_PATH, "r", encoding="utf-8") as f:
    FACEBOOK_SCRAPER_SCRIPT = f.read()

# Load the Facebook XHR scraper script
FACEBOOK_XHR_SCRAPER_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_xhr_scraper.js")
with open(FACEBOOK_XHR_SCRAPER_SCRIPT_PATH, "r", encoding="utf-8") as f:
    FACEBOOK_XHR_SCRAPER_SCRIPT = f.read()

# Load the Facebook XHR interceptor script
FACEBOOK_XHR_INTERCEPTOR_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_xhr_interceptor.js")
with open(FACEBOOK_XHR_INTERCEPTOR_SCRIPT_PATH, "r", encoding="utf-8") as f:
    FACEBOOK_XHR_INTERCEPTOR_SCRIPT = f.read()

# Load the Facebook XHR posts extractor script
FACEBOOK_XHR_POSTS_EXTRACTOR_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_xhr_posts_extractor.js")
with open(FACEBOOK_XHR_POSTS_EXTRACTOR_SCRIPT_PATH, "r", encoding="utf-8") as f:
    FACEBOOK_XHR_POSTS_EXTRACTOR_SCRIPT = f.read()

# Facebook Adaptive Selectors script has been replaced by Facebook Post Link Extractor

# Load the Advanced Facebook Post Extractor script
ADVANCED_FACEBOOK_POST_EXTRACTOR_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "advanced_facebook_post_extractor.js")
with open(ADVANCED_FACEBOOK_POST_EXTRACTOR_SCRIPT_PATH, "r", encoding="utf-8") as f:
    ADVANCED_FACEBOOK_POST_EXTRACTOR_SCRIPT = f.read()

# Load the Facebook Post Link Extractor script
FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_post_link_extractor.js")
with open(FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT_PATH, "r", encoding="utf-8") as f:
    FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT = f.read()

# Load the New Facebook Post Link Extractor script
NEW_FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_post_link_extractor_new.js")
with open(NEW_FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT_PATH, "r", encoding="utf-8") as f:
    NEW_FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT = f.read()

# Load the Facebook Date Click Extractor script
FACEBOOK_DATE_CLICK_EXTRACTOR_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_date_click_extractor.js")
with open(FACEBOOK_DATE_CLICK_EXTRACTOR_SCRIPT_PATH, "r", encoding="utf-8") as f:
    FACEBOOK_DATE_CLICK_EXTRACTOR_SCRIPT = f.read()

# Load the Facebook Direct Post Extractor script
FACEBOOK_DIRECT_POST_EXTRACTOR_SCRIPT_PATH = os.path.join(os.path.dirname(__file__), "js", "facebook_direct_post_extractor.js")
with open(FACEBOOK_DIRECT_POST_EXTRACTOR_SCRIPT_PATH, "r", encoding="utf-8") as f:
    FACEBOOK_DIRECT_POST_EXTRACTOR_SCRIPT = f.read()

# Script to initialize the Facebook scraper and return the API
INIT_FACEBOOK_SCRAPER = """
const scraper = initFacebookScraper();
return scraper;
"""

# Script to scrape visible posts
SCRAPE_VISIBLE_POSTS = """
const scraper = initFacebookScraper();
return scraper.scrapeVisiblePosts();
"""

# Script to scrape posts with scrolling
SCRAPE_WITH_SCROLLING = """
const scraper = initFacebookScraper();
return await scraper.scrapeWithScrolling(arguments[0]);
"""

# Script to extract post links only
EXTRACT_POST_LINKS = """
const scraper = initFacebookScraper();
const posts = await scraper.scrapeWithScrolling(arguments[0]);
// Filter out null URLs and return only valid post links
return posts.map(post => post.url).filter(url => url !== null && url !== undefined);
"""

# Script to extract post data with links
EXTRACT_POST_DATA = """
const scraper = initFacebookScraper();
const posts = await scraper.scrapeWithScrolling(arguments[0]);
return posts.filter(post => post.url !== null);
"""

# Script to initialize the Facebook XHR scraper and return the API
INIT_FACEBOOK_XHR_SCRAPER = """
const scraper = initFacebookXHRScraper();
return scraper;
"""

# Script to scrape posts using XHR interception
SCRAPE_POSTS_XHR = """
const scraper = initFacebookXHRScraper();
return await scraper.scrapePosts(arguments[0]);
"""

# Script to get extracted posts from XHR scraper
GET_EXTRACTED_POSTS_XHR = """
return window.fbScraper ? window.fbScraper.getExtractedPosts() : [];
"""

# Script to initialize the Facebook XHR interceptor and return the API
INIT_FACEBOOK_XHR_INTERCEPTOR = """
window.fbInterceptor = initFacebookXHRInterceptor();
return window.fbInterceptor;
"""

# Script to scrape posts and comments using XHR interception
SCRAPE_POSTS_AND_COMMENTS_XHR = """
return await window.fbInterceptor.scrapePostsAndComments(arguments[0]);
"""

# Script to get all content from XHR interceptor
GET_ALL_CONTENT_XHR = """
return window.fbInterceptor ? window.fbInterceptor.getAllContent() : [];
"""

# Script to initialize the Facebook XHR posts extractor
INIT_FACEBOOK_XHR_POSTS_EXTRACTOR = """
window.fbPostsExtractor = initFacebookXHRPostsExtractor();
return window.fbPostsExtractor;
"""

# Script to scrape posts and comments using the new XHR posts extractor
SCRAPE_POSTS_AND_COMMENTS_NEW_XHR = """
return await window.fbPostsExtractor.scrapePostsAndComments(arguments[0]);
"""

# Script to get all content from the new XHR posts extractor
GET_ALL_CONTENT_NEW_XHR = """
return window.fbPostsExtractor ? window.fbPostsExtractor.getAllContent() : [];
"""

# Script to get CSV content from the new XHR posts extractor
GET_CSV_CONTENT_NEW_XHR = """
return window.fbPostsExtractor ? window.fbPostsExtractor.createCSV('facebook_posts_and_comments.csv') : '';
"""

# Script to initialize the Advanced Facebook Post Extractor
INIT_ADVANCED_FACEBOOK_POST_EXTRACTOR = """
try {
    // Verificar que la función FacebookPostLinkExtractor existe
    if (typeof FacebookPostLinkExtractor !== 'object') {
        console.error('FacebookPostLinkExtractor object not found');
        return false;
    }

    // Establecer el número máximo de posts a extraer
    FacebookPostLinkExtractor.setMaxPosts(arguments[0] || 3000);

    // Guardar una referencia global para acceder desde otros scripts
    window.fbAdvancedExtractor = FacebookPostLinkExtractor;

    console.log('Facebook Post Link Extractor initialized successfully');
    return true;
} catch (e) {
    console.error('Error initializing Facebook Post Link Extractor:', e);
    return false;
}
"""

# Script to start the Advanced Facebook Post Extractor
START_ADVANCED_FACEBOOK_POST_EXTRACTOR = """
try {
    if (!window.fbAdvancedExtractor) {
        console.error('Facebook Post Link Extractor not initialized');
        return false;
    }

    // Iniciar la extracción
    window.fbAdvancedExtractor.start();
    return true;
} catch (e) {
    console.error('Error starting Facebook Post Link Extractor:', e);
    return false;
}
"""

# Script to get the collected posts from the Advanced Facebook Post Extractor
GET_ADVANCED_FACEBOOK_POSTS = """
try {
    if (!window.fbAdvancedExtractor) {
        console.error('Facebook Post Link Extractor not initialized');
        return [];
    }

    return window.fbAdvancedExtractor.getCollectedPosts();
} catch (e) {
    console.error('Error getting posts from Facebook Post Link Extractor:', e);
    return [];
}
"""

# Facebook Adaptive Selectors have been replaced by Facebook Post Link Extractor

# All Facebook Adaptive Selectors scripts have been replaced by Facebook Post Link Extractor

# Script to initialize the New Facebook Post Link Extractor
INIT_NEW_FACEBOOK_POST_LINK_EXTRACTOR = """
try {
    // Check if the FacebookPostLinkExtractor object exists in the window object
    if (typeof window.FacebookPostLinkExtractor !== 'object') {
        console.error('FacebookPostLinkExtractor object not found in window object');
        return false;
    }

    // Set enhanced configuration
    const config = {
        // Timing settings
        minDelay: arguments[0]?.minDelay || 1500,
        maxDelay: arguments[0]?.maxDelay || 3000,

        // Extraction limits
        maxAttempts: arguments[0]?.maxAttempts || 100,
        maxPosts: arguments[0]?.maxPosts || 3000,

        // Scrolling behavior
        scrollStep: arguments[0]?.scrollStep || 1000,

        // Duplicate handling
        skipDuplicates: arguments[0]?.skipDuplicates !== undefined ? arguments[0].skipDuplicates : true
    };

    // Apply configuration
    const result = window.FacebookPostLinkExtractor.setConfig(config);

    // Make sure fbPostLinkExtractor is also set for backward compatibility
    window.fbPostLinkExtractor = window.FacebookPostLinkExtractor;

    console.log('New Facebook Post Link Extractor initialized successfully with enhanced config:', JSON.stringify(config));
    return result;
} catch (e) {
    console.error('Error initializing New Facebook Post Link Extractor:', e);
    return false;
}
"""

# Script to extract next batch of posts with the New Facebook Post Link Extractor
EXTRACT_NEXT_BATCH_NEW_EXTRACTOR = """
try {
    // Check for both possible references to the extractor
    const extractor = window.FacebookPostLinkExtractor || window.fbPostLinkExtractor;

    if (!extractor) {
        console.error('New Facebook Post Link Extractor not initialized');
        return [];
    }

    if (typeof extractor.extractNextBatch !== 'function') {
        console.error('extractNextBatch method not found on extractor');
        console.log('Available methods:', Object.keys(extractor));
        return [];
    }

    return await extractor.extractNextBatch();
} catch (e) {
    console.error('Error extracting posts with New Facebook Post Link Extractor:', e);
    return [];
}
"""

# Script to get all collected posts from the New Facebook Post Link Extractor
GET_ALL_POSTS_NEW_EXTRACTOR = """
try {
    // Check for both possible references to the extractor
    const extractor = window.FacebookPostLinkExtractor || window.fbPostLinkExtractor;

    if (!extractor) {
        console.error('New Facebook Post Link Extractor not initialized');
        return [];
    }

    if (typeof extractor.getCollectedPosts !== 'function') {
        console.error('getCollectedPosts method not found on extractor');
        console.log('Available methods:', Object.keys(extractor));
        return [];
    }

    return extractor.getCollectedPosts();
} catch (e) {
    console.error('Error getting posts from New Facebook Post Link Extractor:', e);
    return [];
}
"""

# Script to initialize the Facebook Date Click Extractor
INIT_DATE_CLICK_EXTRACTOR = """
try {
    // Check if the FacebookDateClickExtractor object exists
    if (typeof window.FacebookDateClickExtractor !== 'object') {
        console.error('FacebookDateClickExtractor object not found');
        return false;
    }

    // Initialize with configuration
    const config = {
        // Timing settings
        minDelay: arguments[0]?.minDelay || 1500,
        maxDelay: arguments[0]?.maxDelay || 3000,

        // Extraction limits
        maxPosts: arguments[0]?.maxPosts || 3000,

        // Scrolling behavior
        scrollStep: arguments[0]?.scrollStep || 800,
        maxScrolls: arguments[0]?.maxScrolls || 50,

        // Batch size for processing
        batchSize: arguments[0]?.batchSize || 5
    };

    // Initialize the extractor
    window.fbDateClickExtractor = window.FacebookDateClickExtractor.init(config);

    console.log('Facebook Date Click Extractor initialized successfully with config:', JSON.stringify(config));
    return true;
} catch (e) {
    console.error('Error initializing Facebook Date Click Extractor:', e);
    return false;
}
"""

# Script to extract next batch with the Date Click Extractor
EXTRACT_NEXT_BATCH_DATE_CLICK = """
try {
    const extractor = window.fbDateClickExtractor || window.FacebookDateClickExtractor;

    if (!extractor) {
        console.error('Facebook Date Click Extractor not initialized');
        return [];
    }

    if (typeof extractor.extractNextBatch !== 'function') {
        console.error('extractNextBatch method not found on extractor');
        console.log('Available methods:', Object.keys(extractor));
        return [];
    }

    return await extractor.extractNextBatch();
} catch (e) {
    console.error('Error extracting posts with Facebook Date Click Extractor:', e);
    return [];
}
"""

# Script to get all collected URLs from the Date Click Extractor
GET_ALL_URLS_DATE_CLICK = """
try {
    const extractor = window.fbDateClickExtractor || window.FacebookDateClickExtractor;

    if (!extractor) {
        console.error('Facebook Date Click Extractor not initialized');
        return [];
    }

    if (typeof extractor.getUrls !== 'function') {
        console.error('getUrls method not found on extractor');
        console.log('Available methods:', Object.keys(extractor));
        return [];
    }

    return extractor.getUrls();
} catch (e) {
    console.error('Error getting URLs from Facebook Date Click Extractor:', e);
    return [];
}
"""

# Script to initialize the Facebook Direct Post Extractor
INIT_DIRECT_POST_EXTRACTOR = """
try {
    // Check if the FacebookDirectPostExtractor object exists
    if (typeof window.FacebookDirectPostExtractor !== 'object') {
        console.error('FacebookDirectPostExtractor object not found');
        return false;
    }

    // Initialize with configuration
    const config = {
        // Timing settings
        minDelay: arguments[0]?.minDelay || 1500,
        maxDelay: arguments[0]?.maxDelay || 3000,

        // Extraction limits
        maxPosts: arguments[0]?.maxPosts || 3000,

        // Scrolling behavior
        scrollStep: arguments[0]?.scrollStep || 800,
        maxScrolls: arguments[0]?.maxScrolls || 50,
    };

    // Initialize the extractor
    window.fbDirectPostExtractor = window.FacebookDirectPostExtractor.init(config);

    console.log('Facebook Direct Post Extractor initialized successfully with config:', JSON.stringify(config));
    return true;
} catch (e) {
    console.error('Error initializing Facebook Direct Post Extractor:', e);
    return false;
}
"""

# Script to extract next batch with the Direct Post Extractor
EXTRACT_NEXT_BATCH_DIRECT = """
try {
    const extractor = window.fbDirectPostExtractor || window.FacebookDirectPostExtractor;

    if (!extractor) {
        console.error('Facebook Direct Post Extractor not initialized');
        return [];
    }

    if (typeof extractor.extractNextBatch !== 'function') {
        console.error('extractNextBatch method not found on extractor');
        console.log('Available methods:', Object.keys(extractor));
        return [];
    }

    return await extractor.extractNextBatch();
} catch (e) {
    console.error('Error extracting posts with Facebook Direct Post Extractor:', e);
    return [];
}
"""

# Script to get all collected URLs from the Direct Post Extractor
GET_ALL_URLS_DIRECT = """
try {
    const extractor = window.fbDirectPostExtractor || window.FacebookDirectPostExtractor;

    if (!extractor) {
        console.error('Facebook Direct Post Extractor not initialized');
        return [];
    }

    if (typeof extractor.getUrls !== 'function') {
        console.error('getUrls method not found on extractor');
        console.log('Available methods:', Object.keys(extractor));
        return [];
    }

    return extractor.getUrls();
} catch (e) {
    console.error('Error getting URLs from Facebook Direct Post Extractor:', e);
    return [];
}
"""
