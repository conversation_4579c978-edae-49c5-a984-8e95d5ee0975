import logging
import re
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QThread
from core.utils.database import Database

# Configure logger
logger = logging.getLogger(__name__)

class MemberScraperWorker(QThread):
    """Worker thread for running the member scraper process."""

    # Define signals
    progress_updated = pyqtSignal(int, int, str)
    log_message = pyqtSignal(str)
    finished = pyqtSignal(dict)

    def __init__(self, group_links, settings, browser_manager=None):
        super().__init__()
        self.group_links = group_links
        self.settings = settings
        self.browser_manager = browser_manager
        self.running = False
        # Usar check_same_thread=False para permitir el uso de la base de datos en diferentes hilos
        self.db = Database(check_same_thread=False)
        self.extracted_members = []

    def process_group_link(self, group_link):
        """
        Procesa y normaliza un enlace de grupo de Facebook para asegurar que apunte a la página de miembros.

        Args:
            group_link (str): Enlace del grupo de Facebook.

        Returns:
            str: Enlace normalizado a la página de miembros del grupo.
        """
        # Eliminar espacios en blanco
        group_link = group_link.strip()

        # Verificar si el enlace ya es una URL completa
        if not group_link.startswith('http'):
            group_link = 'https://web.facebook.com/' + group_link.lstrip('/')

        # Extraer el ID del grupo
        group_id = None

        # Patrón para extraer ID de grupo de diferentes formatos de URL
        patterns = [
            r'facebook\.com/groups/([^/\?]+)',  # Formato estándar: facebook.com/groups/123456789
            r'facebook\.com/group\.php\?id=([^&]+)',  # Formato antiguo: facebook.com/group.php?id=123456789
            r'/groups/([^/\?]+)'  # Formato relativo: /groups/123456789
        ]

        for pattern in patterns:
            match = re.search(pattern, group_link)
            if match:
                group_id = match.group(1)
                break

        # Si no se pudo extraer un ID, devolver el enlace original
        if not group_id:
            self.log_message.emit(f"[WARNING] Could not extract group ID from {group_link}, using as is")
            return group_link

        # Construir URL a la página de miembros
        members_url = f"https://web.facebook.com/groups/{group_id}/members"
        self.log_message.emit(f"[INFO] Normalized group link: {members_url}")

        return members_url

    def run(self):
        """Run the member scraper process."""
        self.running = True
        total = len(self.group_links)
        total_members_extracted = 0
        total_new_members = 0

        # Get profile ID from settings
        profile_id = self.settings.get("profile_id")
        if not profile_id:
            self.log_message.emit("[ERROR] No profile ID specified in settings")
            self.running = False
            self.finished.emit({"success": False, "error": "No profile ID specified"})
            return

        # Get browser instance
        from core.utils.browser import BrowserManager
        if not self.browser_manager:
            self.browser_manager = BrowserManager()

        # Configure headless mode
        headless_mode = self.settings.get("headless_mode", False)

        # Informar al usuario sobre el modo seleccionado
        if headless_mode:
            self.log_message.emit("[INFO] Using headless (invisible) browser mode")
        else:
            self.log_message.emit("[INFO] Using visible browser mode")

        # Configurar el modo headless en el browser manager
        if "nstbrowser" in self.browser_manager.config:
            self.browser_manager.config["nstbrowser"]["headless_mode"] = headless_mode

        # Get browser instance
        driver = self.browser_manager.get_browser_for_profile(profile_id)
        if not driver:
            self.log_message.emit("[ERROR] Failed to get browser instance")
            self.running = False
            self.finished.emit({"success": False, "error": "Failed to get browser instance"})
            return

        try:
            # Load the direct member extractor script
            import os
            script_path = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                "js", "facebook_member_extractor_direct.js"
            )

            # Verificar que el archivo existe
            if not os.path.exists(script_path):
                self.log_message.emit(f"[ERROR] Script file not found: {script_path}")
                self.running = False
                self.finished.emit({"success": False, "error": "Script file not found"})
                return

            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    member_extractor_script = f.read()

                # Log script size for debugging
                self.log_message.emit(f"[INFO] Loaded direct extraction script ({len(member_extractor_script)} bytes)")

                # Cargar el script en la página con más robustez
                self.log_message.emit("[INFO] Loading direct extraction script...")

                # Intentar cargar el script varias veces si es necesario
                max_attempts = 3
                for attempt in range(1, max_attempts + 1):
                    try:
                        # Limpiar cualquier instancia anterior
                        driver.execute_script("""
                        window.extractFacebookGroupMembers = undefined;
                        window.runExtraction = undefined;
                        window._lastExtractionResult = null;
                        window._lastExtractionError = null;
                        window.currentExtractedCount = 0;
                        """)

                        # Cargar el script
                        driver.execute_script(member_extractor_script)

                        # Verificar que el script se cargó correctamente
                        extractor_loaded = driver.execute_script(
                            "return typeof window.extractFacebookGroupMembers === 'function';"
                        )

                        run_function_loaded = driver.execute_script(
                            "return typeof window.runExtraction === 'function';"
                        )

                        if extractor_loaded and run_function_loaded:
                            self.log_message.emit("[INFO] Direct extraction script loaded successfully")
                            break
                        else:
                            self.log_message.emit(f"[WARNING] Script load attempt {attempt}/{max_attempts} failed")
                            if attempt < max_attempts:
                                # Esperar un poco antes de reintentar
                                time.sleep(2)
                    except Exception as e:
                        self.log_message.emit(f"[WARNING] Script load attempt {attempt}/{max_attempts} error: {str(e)}")
                        if attempt < max_attempts:
                            time.sleep(2)

                # Verificar si el script se cargó correctamente después de todos los intentos
                extractor_loaded = driver.execute_script(
                    "return typeof window.extractFacebookGroupMembers === 'function';"
                )

                run_function_loaded = driver.execute_script(
                    "return typeof window.runExtraction === 'function';"
                )

                if not extractor_loaded or not run_function_loaded:
                    self.log_message.emit("[ERROR] Failed to load direct extraction script after multiple attempts")
                    self.running = False
                    self.finished.emit({"success": False, "error": "Failed to load extraction script"})
                    return

            except Exception as e:
                self.log_message.emit(f"[ERROR] Failed to load script: {str(e)}")
                self.running = False
                self.finished.emit({"success": False, "error": f"Failed to load script: {str(e)}"})
                return

            # Configure the extractor
            members_per_group = self.settings.get("members_per_group", 100)
            scroll_delay = self.settings.get("scroll_delay", 2) * 1000  # Convert to milliseconds

            config = {
                "membersToExtract": members_per_group,
                "minDelay": scroll_delay * 0.8,
                "maxDelay": scroll_delay * 1.2,
                "scrollStep": 800,
                "maxEmptyScrolls": 5,
                "maxScrolls": 50,  # Limit the number of scrolls to avoid infinite loops
                "debug": True  # Enable debug logging
            }

            self.log_message.emit(f"[INFO] Configuring extractor with: {config}")

            # Process each group
            for i, group_link in enumerate(self.group_links):
                if not self.running:
                    break

                # Update progress
                self.progress_updated.emit(i + 1, total, f"Scraping group {i + 1}/{total}")
                self.log_message.emit(f"[INFO] Scraping group: {group_link}")

                try:
                    # Procesar y normalizar el enlace del grupo
                    processed_group_link = self.process_group_link(group_link)
                    self.log_message.emit(f"[INFO] Navigating to {processed_group_link}")
                    driver.get(processed_group_link)

                    # Esperar a que la página cargue con un tiempo de espera más largo
                    import time
                    self.log_message.emit(f"[INFO] Waiting for page to load...")
                    time.sleep(10)  # Aumentar el tiempo de espera para asegurar que la página cargue completamente

                    # Start extraction
                    self.log_message.emit(f"[INFO] Extracting members from {group_link}...")

                    # Verificar que la página está lista
                    page_ready = driver.execute_script(
                        "return document.readyState === 'complete';"
                    )

                    if not page_ready:
                        self.log_message.emit("[INFO] Waiting for page to be fully loaded...")
                        import time
                        time.sleep(5)  # Esperar un poco más si la página no está lista

                    # Start extraction using the direct method
                    self.log_message.emit("[INFO] Starting direct extraction process...")

                    # Ejecutar la función de extracción directa
                    try:
                        # Iniciar la extracción y esperar los resultados
                        self.log_message.emit("[INFO] Executing extraction function...")

                        # Verificar que la función de extracción está disponible
                        # Intentar varias veces si es necesario
                        max_check_attempts = 3
                        extraction_available = False

                        for check_attempt in range(1, max_check_attempts + 1):
                            try:
                                # Verificar la disponibilidad de la función
                                extraction_available = driver.execute_script("""
                                return typeof window.runExtraction === 'function';
                                """)

                                if extraction_available:
                                    self.log_message.emit("[INFO] Extraction function is available")
                                    break
                                else:
                                    self.log_message.emit(f"[WARNING] Extraction function check attempt {check_attempt}/{max_check_attempts} failed")

                                    # Intentar recargar el script
                                    if check_attempt < max_check_attempts:
                                        self.log_message.emit("[INFO] Attempting to reload extraction script...")
                                        driver.execute_script(member_extractor_script)
                                        time.sleep(2)
                            except Exception as e:
                                self.log_message.emit(f"[WARNING] Function check attempt {check_attempt}/{max_check_attempts} error: {str(e)}")
                                if check_attempt < max_check_attempts:
                                    time.sleep(2)

                        if not extraction_available:
                            self.log_message.emit("[ERROR] Extraction function not available after multiple attempts")
                            continue

                        # Ejecutar la extracción y esperar los resultados
                        self.log_message.emit("[INFO] Running extraction function...")

                        # Ejecutar la función de extracción y esperar a que termine
                        try:
                            # Usar execute_script para iniciar la extracción
                            driver.execute_script("window.runExtraction(arguments[0]);", config)

                            # Esperar a que la extracción termine
                            import time
                            max_wait_time = 300  # 5 minutos
                            start_time = time.time()
                            extraction_complete = False

                            while not extraction_complete and time.time() - start_time < max_wait_time:
                                # Verificar si la extracción ha terminado
                                result = driver.execute_script("""
                                return window._lastExtractionResult || null;
                                """)

                                if result is not None:
                                    extraction_complete = True
                                    self.log_message.emit("[INFO] Extraction process completed")
                                    break

                                # Mostrar progreso
                                current_count = driver.execute_script("""
                                try {
                                    return window.currentExtractedCount || 0;
                                } catch (e) {
                                    return 0;
                                }
                                """)

                                self.log_message.emit(f"[INFO] Extracting members... {current_count} found so far")

                                # Esperar antes de verificar nuevamente
                                time.sleep(5)

                            if not extraction_complete:
                                self.log_message.emit("[WARNING] Extraction timed out")
                                continue
                        except Exception as e:
                            self.log_message.emit(f"[ERROR] Error during extraction execution: {str(e)}")
                            continue

                        if not result:
                            self.log_message.emit(f"[WARNING] Failed to extract members from {group_link}: No data returned")
                            continue

                    except Exception as e:
                        self.log_message.emit(f"[ERROR] Error during extraction: {str(e)}")
                        # Intentar obtener más información sobre el error
                        try:
                            js_error = driver.execute_script(
                                "return window._lastExtractionError || 'No error details available';"
                            )
                            self.log_message.emit(f"[ERROR] JavaScript error: {js_error}")
                        except:
                            pass
                        continue

                    # Get all members from all categories
                    all_members = []

                    # Verificar la estructura de los resultados
                    if result.get('success') and 'data' in result:
                        data = result['data']
                        if 'members' in data:
                            # Usar la lista plana de miembros únicos si está disponible
                            if 'all' in data['members']:
                                all_members = data['members']['all']
                                self.log_message.emit(f"[INFO] Using flat list of {len(all_members)} unique members")
                            else:
                                # Compatibilidad con versiones anteriores
                                if 'admins' in data['members']:
                                    all_members.extend(data['members']['admins'])
                                if 'moderators' in data['members']:
                                    all_members.extend(data['members']['moderators'])
                                if 'regular' in data['members']:
                                    all_members.extend(data['members']['regular'])
                                self.log_message.emit(f"[INFO] Using combined lists with {len(all_members)} members (may contain duplicates)")

                        # Mostrar estadísticas
                        if 'metadata' in data:
                            total = data['metadata'].get('totalMembers', 0)
                            unique = data['metadata'].get('uniqueMembers', len(all_members))
                            self.log_message.emit(f"[INFO] Extraction statistics: {total} total members found, {unique} unique members")
                    elif not result.get('success'):
                        error = result.get('error', 'Unknown error')
                        self.log_message.emit(f"[ERROR] Extraction failed: {error}")
                        continue

                    if not all_members:
                        self.log_message.emit(f"[WARNING] No members found in {group_link}")
                        continue

                    # Eliminar duplicados basados en ID (por si acaso)
                    unique_members = {}
                    for member in all_members:
                        if member.get('id') and member.get('id') not in unique_members:
                            unique_members[member.get('id')] = member

                    # Convertir de nuevo a lista
                    all_members = list(unique_members.values())

                    self.log_message.emit(f"[INFO] Extracted {len(all_members)} unique members from {group_link}")

                    # Use all_members as our members list
                    members = all_members

                    # Save members to database
                    new_members = 0
                    group_id = None

                    # Save group to database
                    self.log_message.emit(f"[INFO] Saving group {group_link} to database...")
                    save_result = self.db.save_group(group_link)

                    if save_result.get('status') in ['new', 'updated']:
                        group_data = save_result.get('group', {})
                        group_id = group_data.get("id")
                        self.log_message.emit(f"[INFO] Group saved with ID: {group_id} (Status: {save_result.get('status')})")
                    else:
                        error_msg = save_result.get('error', 'Unknown error')
                        self.log_message.emit(f"[WARNING] Failed to save group {group_link} to database: {error_msg}")
                        group_id = None

                    # Process each member
                    for member in members:
                        if not self.running:
                            break

                        member_id = member.get("id")
                        member_name = member.get("name")
                        member_role = member.get("role")
                        member_profile_url = member.get("profileUrl")

                        # If no ID but we have a profile URL, try to extract ID from URL
                        if not member_id and member_profile_url:
                            # Try to extract ID from profile URL
                            import re
                            id_match = re.search(r'/user/([^/]+)|id=([0-9]+)|facebook\.com/([^/\?]+)', member_profile_url)
                            if id_match:
                                # Use the first non-None group
                                member_id = next((g for g in id_match.groups() if g is not None), '')

                        if not member_id:
                            continue

                        # Filtrar elementos de UI que puedan haberse colado
                        ui_element_ids = ['notifications', 'groups', 'friends', 'help', 'messages']
                        if member_id in ui_element_ids:
                            self.log_message.emit(f"[INFO] Skipping UI element with ID: {member_id}")
                            continue

                        # Filtrar nombres que parecen ser elementos de UI
                        ui_element_names = ['See all', 'Learn More', 'See More', 'View More', 'Load More',
                                          'Unread', 'Mark as read', 'Confirm', 'Delete', 'Cancel',
                                          'Friend Request', 'mutual friend', 'Notification', 'Welcome']

                        if member_name and any(ui_name in member_name for ui_name in ui_element_names):
                            self.log_message.emit(f"[INFO] Skipping UI element: {member_name} (ID: {member_id})")
                            continue

                        # Asegurarse de que tenemos un nombre válido
                        if not member_name or not member_name.strip():
                            member_name = f"User {member_id}"

                        # Asegurarse de que tenemos un ID válido
                        if not member_id or not member_id.strip():
                            self.log_message.emit(f"[WARNING] Member has no ID, skipping: {member_name}")
                            continue

                        # Asegurarse de que el group_id es un entero o None
                        if group_id is not None:
                            try:
                                group_id = int(group_id)
                            except (ValueError, TypeError):
                                self.log_message.emit(f"[WARNING] Invalid group ID: {group_id}, using None")
                                group_id = None

                        # Guardar el miembro en la base de datos con información adicional
                        self.log_message.emit(f"[INFO] Saving member {member_name} (ID: {member_id}) to database...")
                        save_result = self.db.save_member(member_id, name=member_name, group_id=group_id)

                        # Añadir información de rol al objeto miembro
                        member['role_type'] = 'admin' if member_role and 'Admin' in member_role else \
                                           'moderator' if member_role and 'Moderator' in member_role else 'regular'

                        # Siempre añadir el miembro a la lista de miembros extraídos
                        self.extracted_members.append(member)

                        # Procesar el resultado de la operación de guardado
                        if isinstance(save_result, dict):
                            status = save_result.get('status')
                            if status == 'new':
                                new_members += 1
                                self.log_message.emit(f"[INFO] Added new member: {member_name} (ID: {member_id})")
                            elif status == 'updated':
                                self.log_message.emit(f"[INFO] Updated existing member: {member_name} (ID: {member_id})")
                            elif status == 'error':
                                error_msg = save_result.get('error', 'Unknown error')
                                self.log_message.emit(f"[WARNING] Failed to save member: {member_name} (ID: {member_id}). Error: {error_msg}")
                            else:
                                self.log_message.emit(f"[INFO] Member operation completed with status: {status}")
                        else:
                            # Compatibilidad con versiones anteriores
                            if save_result == 'new':
                                new_members += 1
                                self.log_message.emit(f"[INFO] Added new member: {member_name} (ID: {member_id})")
                            elif save_result == 'updated':
                                self.log_message.emit(f"[INFO] Updated existing member: {member_name} (ID: {member_id})")
                            else:
                                self.log_message.emit(f"[WARNING] Failed to save member: {member_name} (ID: {member_id})")

                            # Log every 10 members
                            if new_members % 10 == 0:
                                self.log_message.emit(f"[INFO] Saved {new_members} new members from {group_link}")

                    # Update statistics
                    total_members_extracted += len(members)
                    total_new_members += new_members

                    self.log_message.emit(f"[INFO] Saved {new_members} new members from {group_link} (total: {len(members)})")

                except Exception as e:
                    self.log_message.emit(f"[ERROR] Error scraping group {group_link}: {str(e)}")

            # Close the browser
            self.log_message.emit("[INFO] Closing browser...")
            self.browser_manager.close_browser(driver)

            # Finish
            self.log_message.emit(f"[INFO] Member scraping completed. Extracted {total_members_extracted} members, {total_new_members} new.")

        except Exception as e:
            self.log_message.emit(f"[ERROR] Error in member scraper: {str(e)}")
            # Try to close the browser
            try:
                self.browser_manager.close_browser(driver)
            except:
                pass

        self.running = False
        # Obtener los IDs de los miembros extraídos para enviarlos a la UI
        member_ids = [member.get('id') for member in self.extracted_members if member.get('id')]

        self.finished.emit({
            "success": True,
            "total_members": total_members_extracted,
            "new_members": total_new_members,
            "members": self.extracted_members,
            "member_ids": member_ids
        })

    def stop(self):
        """Stop the member scraper process."""
        self.running = False
        self.log_message.emit("[INFO] Stopping member scraper...")


class MemberScraperController(QObject):
    """Controller for the member scraper tab."""

    # Define signals
    progress_updated = pyqtSignal(int, int, str)
    log_message = pyqtSignal(str)
    finished = pyqtSignal(dict)  # Signal emitted when scraper finishes

    def __init__(self, parent=None):
        super().__init__(parent)

        # Create database instance con check_same_thread=False para permitir el uso en diferentes hilos
        self.db = Database(check_same_thread=False)

        # Verificar que la tabla members existe
        self.verify_database_tables()

        # Initialize worker
        self.worker = None

        # Initialize browser manager
        from core.utils.browser import BrowserManager
        self.browser_manager = BrowserManager()

    def verify_database_tables(self):
        """
        Verifica que las tablas necesarias existen en la base de datos.
        Si no existen, las crea.
        """
        try:
            # Verificar si la tabla members existe
            cursor = self.db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='members'")
            if cursor and not cursor.fetchone():
                self.log_message.emit("[INFO] Creating members table...")
                self.db.execute('''
                CREATE TABLE IF NOT EXISTS members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    member_id TEXT UNIQUE NOT NULL,
                    name TEXT,
                    group_id INTEGER,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (group_id) REFERENCES groups (id)
                )
                ''', commit=True)
                self.log_message.emit("[INFO] Members table created successfully")

            # Verificar si la tabla groups existe
            cursor = self.db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='groups'")
            if cursor and not cursor.fetchone():
                self.log_message.emit("[INFO] Creating groups table...")
                self.db.execute('''
                CREATE TABLE IF NOT EXISTS groups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_url TEXT UNIQUE NOT NULL,
                    name TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                ''', commit=True)
                self.log_message.emit("[INFO] Groups table created successfully")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to verify database tables: {e}")

    def verify_database_integrity(self):
        """
        Verifica la integridad de la base de datos y trata de solucionar problemas.

        Returns:
            bool: True si la verificación fue exitosa, False en caso contrario.
        """
        try:
            self.log_message.emit("[INFO] Verifying database integrity...")

            # Usar el método de verificación de integridad de la base de datos
            results = self.db.verify_database_integrity()

            # Procesar los resultados
            if results['status'] == 'success':
                self.log_message.emit(f"[INFO] {results['message']}")
                return True
            elif results['status'] == 'warning':
                self.log_message.emit(f"[WARNING] {results['message']}")

                # Mostrar problemas encontrados
                for issue in results['issues']:
                    self.log_message.emit(f"[WARNING] Issue: {issue}")

                # Mostrar soluciones aplicadas
                for fix in results['fixes']:
                    self.log_message.emit(f"[INFO] Fix applied: {fix}")

                return True
            else:  # status == 'error'
                self.log_message.emit(f"[ERROR] {results['message']}")

                # Mostrar problemas encontrados
                for issue in results['issues']:
                    self.log_message.emit(f"[ERROR] Issue: {issue}")

                # Mostrar soluciones aplicadas
                for fix in results['fixes']:
                    self.log_message.emit(f"[INFO] Fix applied: {fix}")

                # Intentar crear las tablas necesarias como último recurso
                self.log_message.emit("[INFO] Attempting to create necessary tables as last resort...")
                self.verify_database_tables()

                return False
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to verify database integrity: {e}")
            return False

    def process_group_link(self, group_link):
        """
        Procesa y normaliza un enlace de grupo de Facebook para asegurar que apunte a la página de miembros.

        Args:
            group_link (str): Enlace del grupo de Facebook.

        Returns:
            str: Enlace normalizado a la página de miembros del grupo.
        """
        import re

        # Eliminar espacios en blanco
        group_link = group_link.strip()

        # Verificar si el enlace ya es una URL completa
        if not group_link.startswith('http'):
            group_link = 'https://web.facebook.com/' + group_link.lstrip('/')

        # Extraer el ID del grupo
        group_id = None

        # Patrón para extraer ID de grupo de diferentes formatos de URL
        patterns = [
            r'facebook\.com/groups/([^/\?]+)',  # Formato estándar: facebook.com/groups/123456789
            r'facebook\.com/group\.php\?id=([^&]+)',  # Formato antiguo: facebook.com/group.php?id=123456789
            r'/groups/([^/\?]+)'  # Formato relativo: /groups/123456789
        ]

        for pattern in patterns:
            match = re.search(pattern, group_link)
            if match:
                group_id = match.group(1)
                break

        # Si no se pudo extraer un ID, devolver el enlace original
        if not group_id:
            self.log_message.emit(f"[WARNING] Could not extract group ID from {group_link}, using as is")
            return group_link

        # Construir URL a la página de miembros
        members_url = f"https://web.facebook.com/groups/{group_id}/members"
        self.log_message.emit(f"[INFO] Normalized group link: {members_url}")

        return members_url

    def load_group_links(self, file_path=None):
        """
        Load group links from a file or database.

        Args:
            file_path (str, optional): Path to the group links file.

        Returns:
            list: List of group links.
        """
        if file_path:
            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if content:
                    # Split content into lines and filter empty lines
                    group_links = [line.strip() for line in content.split('\n') if line.strip()]

                    # Save group links to database
                    count = 0
                    for group_url in group_links:
                        if self.db.save_group(group_url):
                            count += 1

                    self.log_message.emit(f"[INFO] Imported {count} group links from file to database")
                else:
                    self.log_message.emit(f"[WARNING] No group links found in {file_path}")
                    group_links = []
            except Exception as e:
                self.log_message.emit(f"[ERROR] Failed to import group links from file: {e}")
                group_links = []

        # Get group links from database
        groups_data = self.db.get_groups()
        group_links = [g['group_url'] for g in groups_data]

        self.log_message.emit(f"[INFO] Loaded {len(group_links)} group links from database")
        return group_links

    def load_members(self, file_path=None):
        """
        Load members from a file or database.

        Args:
            file_path (str, optional): Path to the members file.

        Returns:
            list: List of member IDs.
        """
        if file_path:
            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()

                if content:
                    # Split content into lines and filter empty lines
                    members = [line.strip() for line in content.split('\n') if line.strip()]

                    # Save members to database
                    count = 0
                    for member_id in members:
                        if self.db.save_member(member_id):
                            count += 1

                    self.log_message.emit(f"[INFO] Imported {count} members from file to database")
                else:
                    self.log_message.emit(f"[WARNING] No members found in {file_path}")
                    members = []
            except Exception as e:
                self.log_message.emit(f"[ERROR] Failed to import members from file: {e}")
                members = []

        # Get members from database
        members_data = self.db.get_members()

        # Create a list of member names or IDs (prefer name if available)
        members = []
        for m in members_data:
            if m.get('name') and m['name'].strip():
                members.append(m['name'])
            else:
                members.append(m['member_id'])

        self.log_message.emit(f"[INFO] Loaded {len(members)} members from database")
        return members

    def save_group_links(self, group_links, file_path=None):
        """
        Save group links to the database and optionally to a file.

        Args:
            group_links (list): List of group links.
            file_path (str, optional): Path to the group links file.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Save to database
            count = 0
            for group_url in group_links:
                if self.db.save_group(group_url):
                    count += 1

            self.log_message.emit(f"[INFO] Saved {count} group links to database")

            # Optionally save to file
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(group_links))
                self.log_message.emit(f"[INFO] Saved {len(group_links)} group links to {file_path}")

            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save group links: {e}")
            return False

    def save_members(self, members, file_path=None):
        """
        Save members to the database and optionally to a file.

        Args:
            members (list): List of member IDs.
            file_path (str, optional): Path to the members file.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Save to database
            count = 0
            for member_id in members:
                if self.db.save_member(member_id):
                    count += 1

            self.log_message.emit(f"[INFO] Saved {count} members to database")

            # Optionally save to file
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(members))
                self.log_message.emit(f"[INFO] Saved {len(members)} members to {file_path}")

            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save members: {e}")
            return False

    def start_scraper(self, group_links, settings):
        """
        Start the member scraper process.

        Args:
            group_links (list): List of group links to scrape.
            settings (dict): Scraper settings.

        Returns:
            bool: True if started successfully, False otherwise.
        """
        if self.worker and self.worker.isRunning():
            self.log_message.emit("[WARNING] Member scraper is already running")
            return False

        # Verificar la integridad de la base de datos antes de iniciar
        self.log_message.emit("[INFO] Performing database integrity check before starting...")
        db_ok = self.verify_database_integrity()
        if not db_ok:
            self.log_message.emit("[WARNING] Database integrity check found issues, but we'll try to continue")

        # Verificar que tenemos enlaces de grupo válidos
        if not group_links or not isinstance(group_links, list) or len(group_links) == 0:
            self.log_message.emit("[ERROR] No valid group links provided")
            return False

        # Validar la configuración
        if not settings or not isinstance(settings, dict):
            self.log_message.emit("[ERROR] Invalid settings provided")
            return False

        # Asegurarse de que el browser_manager está disponible
        if not hasattr(self, 'browser_manager') or self.browser_manager is None:
            try:
                from core.utils.browser import BrowserManager
                self.browser_manager = BrowserManager()
                self.log_message.emit("[INFO] Created new browser manager instance")
            except Exception as e:
                self.log_message.emit(f"[ERROR] Failed to create browser manager: {e}")
                return False

        try:
            # Create and start worker thread
            self.worker = MemberScraperWorker(group_links, settings, self.browser_manager)
            self.worker.progress_updated.connect(self.on_progress_updated)
            self.worker.log_message.connect(self.log_message)
            self.worker.finished.connect(self.on_scraper_finished)  # Connect worker's finished signal to controller's handler
            self.worker.start()

            self.log_message.emit("[INFO] Member scraper started")
            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to start member scraper: {e}")
            return False

    def stop_scraper(self):
        """
        Stop the member scraper process and close all browsers.

        Returns:
            bool: True if stopped successfully, False otherwise.
        """
        if not self.worker or not self.worker.isRunning():
            self.log_message.emit("[WARNING] Member scraper is not running")
            return False

        # Stop worker thread
        self.worker.stop()
        self.worker.wait()

        # Cerrar todos los navegadores
        self.close_all_browsers()

        self.log_message.emit("[INFO] Member scraper stopped and browsers closed")
        return True

    @pyqtSlot(int, int, str)
    def on_progress_updated(self, current, total, message):
        """Handle progress update from worker thread."""
        self.progress_updated.emit(current, total, message)

    @pyqtSlot(dict)
    def on_scraper_finished(self, result):
        """Handle scraper finished signal.

        Args:
            result (dict): Result dictionary from the worker thread.
        """
        if result.get("success"):
            total_members = result.get("total_members", 0)
            new_members = result.get("new_members", 0)
            self.log_message.emit(f"[INFO] Member scraper finished successfully. Extracted {total_members} members, {new_members} new.")
        else:
            error = result.get("error", "Unknown error")
            self.log_message.emit(f"[ERROR] Member scraper failed: {error}")

        # Forward the result to the UI
        self.finished.emit(result)

    def close_all_browsers(self):
        """
        Close all active browser instances and kill any remaining Chrome processes.

        Returns:
            bool: True if successful, False otherwise.
        """
        if hasattr(self, 'browser_manager') and self.browser_manager is not None:
            self.log_message.emit("[INFO] Closing all browser instances...")
            success = self.browser_manager.close_all_browsers()

            if success:
                self.log_message.emit("[INFO] All browser instances closed successfully")
            else:
                self.log_message.emit("[WARNING] Some browser instances could not be closed")

                # Intentar matar procesos de Chrome como último recurso
                try:
                    self.log_message.emit("[INFO] Attempting to kill all Chrome processes...")
                    self.browser_manager.kill_nstchrome_processes()
                    self.log_message.emit("[INFO] Chrome processes killed")
                except Exception as e:
                    self.log_message.emit(f"[ERROR] Failed to kill Chrome processes: {e}")

            return success
        else:
            self.log_message.emit("[WARNING] No browser manager available")
            return False
