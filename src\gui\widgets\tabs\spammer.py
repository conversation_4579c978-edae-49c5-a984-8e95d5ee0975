from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTextEdit, QListWidget, QSpinBox,
                            QCheckBox, QProgressBar, QSplitter, QFileDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# Import the updated spammer tab
from .spammer_updated import SpammerTab

class SpammerWorker(QThread):
    """Worker thread for running the spammer in the background."""

    # Define signals
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    log_message = pyqtSignal(str)
    finished_successfully = pyqtSignal()

    def __init__(self, profiles, posts, comments, settings):
        super().__init__()
        self.profiles = profiles
        self.posts = posts
        self.comments = comments
        self.settings = settings
        self.is_running = False

    def run(self):
        """Run the spammer process."""
        self.is_running = True
        self.log_message.emit("[INFO] Spammer process started")

        # Simulate spamming process
        total_profiles = len(self.profiles)
        for i, profile in enumerate(self.profiles):
            if not self.is_running:
                break

            self.status_updated.emit(f"Processing profile {i+1}/{total_profiles}")
            self.log_message.emit(f"[INFO] Processing profile: {profile}")

            # Update progress
            progress = int((i / total_profiles) * 100)
            self.progress_updated.emit(progress)

            # Simulate work
            self.msleep(1000)  # Sleep for 1 second

        if self.is_running:
            self.progress_updated.emit(100)
            self.status_updated.emit("Completed")
            self.log_message.emit("[SUCCESS] Spammer process completed successfully")
            self.finished_successfully.emit()
        else:
            self.status_updated.emit("Stopped")
            self.log_message.emit("[WARNING] Spammer process stopped by user")

        self.is_running = False

    def stop(self):
        """Stop the spammer process."""
        self.is_running = False


# This class is replaced by the SpammerTab in spammer_updated.py
# Keeping this file for backward compatibility
class OldSpammerTab(QWidget):
    """Spammer tab for managing comment spamming operations."""

    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.worker = None

    def setup_ui(self):
        """Create and configure the UI components."""
        main_layout = QHBoxLayout(self)

        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - configuration
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Profiles section
        self.profiles_group = QGroupBox("Profiles")
        profiles_layout = QVBoxLayout()
        self.profiles_list = QListWidget()

        profiles_buttons = QHBoxLayout()
        self.btn_load_profiles = QPushButton("Load")
        self.btn_edit_profiles = QPushButton("Edit")
        self.btn_refresh_profiles = QPushButton("Refresh")

        profiles_buttons.addWidget(self.btn_load_profiles)
        profiles_buttons.addWidget(self.btn_edit_profiles)
        profiles_buttons.addWidget(self.btn_refresh_profiles)

        profiles_layout.addWidget(self.profiles_list)
        profiles_layout.addLayout(profiles_buttons)
        self.profiles_group.setLayout(profiles_layout)

        # Posts section
        self.posts_group = QGroupBox("Posts")
        posts_layout = QVBoxLayout()
        self.posts_list = QListWidget()

        posts_buttons = QHBoxLayout()
        self.btn_load_posts = QPushButton("Load")
        self.btn_edit_posts = QPushButton("Edit")
        self.btn_refresh_posts = QPushButton("Refresh")

        posts_buttons.addWidget(self.btn_load_posts)
        posts_buttons.addWidget(self.btn_edit_posts)
        posts_buttons.addWidget(self.btn_refresh_posts)

        posts_layout.addWidget(self.posts_list)
        posts_layout.addLayout(posts_buttons)
        self.posts_group.setLayout(posts_layout)

        # Comments section
        self.comments_group = QGroupBox("Comments")
        comments_layout = QVBoxLayout()
        self.comments_list = QListWidget()

        comments_buttons = QHBoxLayout()
        self.btn_load_comments = QPushButton("Load")
        self.btn_edit_comments = QPushButton("Edit")
        self.btn_refresh_comments = QPushButton("Refresh")

        comments_buttons.addWidget(self.btn_load_comments)
        comments_buttons.addWidget(self.btn_edit_comments)
        comments_buttons.addWidget(self.btn_refresh_comments)

        comments_layout.addWidget(self.comments_list)
        comments_layout.addLayout(comments_buttons)
        self.comments_group.setLayout(comments_layout)

        # Add groups to left layout
        left_layout.addWidget(self.profiles_group)
        left_layout.addWidget(self.posts_group)
        left_layout.addWidget(self.comments_group)

        # Right panel - controls and output
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Settings section
        self.settings_group = QGroupBox("Spammer Settings")
        settings_layout = QVBoxLayout()

        # Comments per profile
        comments_per_profile_layout = QHBoxLayout()
        comments_per_profile_layout.addWidget(QLabel("Comments per profile:"))
        self.comments_per_profile = QSpinBox()
        self.comments_per_profile.setValue(3)
        self.comments_per_profile.setRange(1, 100)
        comments_per_profile_layout.addWidget(self.comments_per_profile)
        settings_layout.addLayout(comments_per_profile_layout)

        # Delay between comments
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("Delay between comments (seconds):"))
        self.delay_spin = QSpinBox()
        self.delay_spin.setValue(5)
        self.delay_spin.setRange(1, 60)
        delay_layout.addWidget(self.delay_spin)
        settings_layout.addLayout(delay_layout)

        # Checkboxes for options
        self.random_typing = QCheckBox("Random typing simulation")
        self.skip_commented = QCheckBox("Skip already commented posts")
        self.headless_mode = QCheckBox("Headless mode")
        self.use_first_profile_for_selectors = QCheckBox("Use first profile to detect selectors")
        self.use_first_profile_for_commenting = QCheckBox("Use first profile for commenting too")

        # Set default values
        self.random_typing.setChecked(True)
        self.skip_commented.setChecked(True)
        self.use_first_profile_for_selectors.setChecked(True)
        self.use_first_profile_for_commenting.setChecked(True)

        # Add tooltips
        self.use_first_profile_for_selectors.setToolTip("Use the first profile to detect selectors for all posts before starting the commenting process")
        self.use_first_profile_for_commenting.setToolTip("After using the first profile for selector detection, also use it for commenting")

        # Connect signals
        self.use_first_profile_for_selectors.stateChanged.connect(self.on_selector_detection_changed)

        # Add widgets to layout
        settings_layout.addWidget(self.random_typing)
        settings_layout.addWidget(self.skip_commented)
        settings_layout.addWidget(self.headless_mode)
        settings_layout.addWidget(self.use_first_profile_for_selectors)
        settings_layout.addWidget(self.use_first_profile_for_commenting)

        self.settings_group.setLayout(settings_layout)

        # Control section
        self.control_group = QGroupBox("Control")
        control_layout = QVBoxLayout()

        self.btn_start = QPushButton("Start Spammer")
        self.btn_start.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")

        self.btn_stop = QPushButton("Stop Spammer")
        self.btn_stop.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        self.btn_stop.setEnabled(False)

        control_layout.addWidget(self.btn_start)
        control_layout.addWidget(self.btn_stop)

        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        control_layout.addLayout(progress_layout)

        # Status label
        self.status_label = QLabel("Status: Idle")
        control_layout.addWidget(self.status_label)

        self.control_group.setLayout(control_layout)

        # Log section
        self.log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        self.log_group.setLayout(log_layout)

        # Add groups to right layout
        right_layout.addWidget(self.settings_group)
        right_layout.addWidget(self.control_group)
        right_layout.addWidget(self.log_group)

        # Add panels to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # Set initial splitter sizes (40% left, 60% right)
        splitter.setSizes([400, 600])

        # Connect signals
        self.connect_signals()

        # Load initial data
        self.load_sample_data()

    def connect_signals(self):
        """Connect signals to slots."""
        # Button signals
        self.btn_load_profiles.clicked.connect(self.on_load_profiles)
        self.btn_edit_profiles.clicked.connect(self.on_edit_profiles)
        self.btn_refresh_profiles.clicked.connect(self.on_refresh_profiles)

        self.btn_load_posts.clicked.connect(self.on_load_posts)
        self.btn_edit_posts.clicked.connect(self.on_edit_posts)
        self.btn_refresh_posts.clicked.connect(self.on_refresh_posts)

        self.btn_load_comments.clicked.connect(self.on_load_comments)
        self.btn_edit_comments.clicked.connect(self.on_edit_comments)
        self.btn_refresh_comments.clicked.connect(self.on_refresh_comments)

        self.btn_start.clicked.connect(self.start)
        self.btn_stop.clicked.connect(self.stop)

    def load_sample_data(self):
        """Load sample data for demonstration."""
        # Sample profiles
        self.profiles_list.clear()
        self.profiles_list.addItems([f"Profile_{i}" for i in range(1, 6)])

        # Sample posts
        self.posts_list.clear()
        self.posts_list.addItems([
            "https://facebook.com/post/123456789",
            "https://facebook.com/post/987654321",
            "https://facebook.com/post/456789123"
        ])

        # Sample comments
        self.comments_list.clear()
        self.comments_list.addItems([
            "Great post! Thanks for sharing.",
            "This is amazing! Keep up the good work.",
            "I love this content, very informative."
        ])

        # Log initial message
        self.log_message("[INFO] Sample data loaded")

    def log_message(self, message):
        """Add a message to the log."""
        self.log_text.append(message)
        # Auto-scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def on_load_profiles(self):
        """Handle load profiles button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Profiles File", "", "Text Files (*.txt);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Loading profiles from {file_path}")
            # Here you would actually load the profiles from the file

    def on_edit_profiles(self):
        """Handle edit profiles button click."""
        self.log_message("[INFO] Opening profiles editor")
        # Here you would open the profiles editor

    def on_refresh_profiles(self):
        """Handle refresh profiles button click."""
        self.log_message("[INFO] Refreshing profiles list")
        # Here you would refresh the profiles list

    def on_load_posts(self):
        """Handle load posts button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Posts File", "", "Text Files (*.txt);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Loading posts from {file_path}")
            # Here you would actually load the posts from the file

    def on_edit_posts(self):
        """Handle edit posts button click."""
        self.log_message("[INFO] Opening posts editor")
        # Here you would open the posts editor

    def on_refresh_posts(self):
        """Handle refresh posts button click."""
        self.log_message("[INFO] Refreshing posts list")
        # Here you would refresh the posts list

    def on_load_comments(self):
        """Handle load comments button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Comments File", "", "Text Files (*.txt);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Loading comments from {file_path}")
            # Here you would actually load the comments from the file

    def on_edit_comments(self):
        """Handle edit comments button click."""
        self.log_message("[INFO] Opening comments editor")
        # Here you would open the comments editor

    def on_refresh_comments(self):
        """Handle refresh comments button click."""
        self.log_message("[INFO] Refreshing comments list")
        # Here you would refresh the comments list

    def start(self):
        """Start the spammer process."""
        if self.worker is not None and self.worker.isRunning():
            self.log_message("[WARNING] Spammer is already running")
            return

        # Get profiles, posts, and comments from lists
        profiles = [self.profiles_list.item(i).text() for i in range(self.profiles_list.count())]
        posts = [self.posts_list.item(i).text() for i in range(self.posts_list.count())]
        comments = [self.comments_list.item(i).text() for i in range(self.comments_list.count())]

        # Get settings
        settings = {
            "comments_per_profile": self.comments_per_profile.value(),
            "delay": self.delay_spin.value(),
            "random_typing": self.random_typing.isChecked(),
            "skip_commented": self.skip_commented.isChecked(),
            "headless_mode": self.headless_mode.isChecked(),
            "use_first_profile_for_selectors": self.use_first_profile_for_selectors.isChecked(),
            "use_first_profile_for_commenting": self.use_first_profile_for_commenting.isChecked(),
            "selectors_detection_complete": False  # Reset this flag on each start
        }

        # Validate inputs
        if not profiles:
            self.log_message("[ERROR] No profiles loaded")
            return

        if not posts:
            self.log_message("[ERROR] No posts loaded")
            return

        if not comments:
            self.log_message("[ERROR] No comments loaded")
            return

        # Create and start worker thread
        self.worker = SpammerWorker(profiles, posts, comments, settings)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.status_updated.connect(self.update_status)
        self.worker.log_message.connect(self.log_message)
        self.worker.finished_successfully.connect(self.on_worker_finished)
        self.worker.finished.connect(self.on_worker_stopped)

        self.worker.start()

        # Update UI
        self.btn_start.setEnabled(False)
        self.btn_stop.setEnabled(True)
        self.log_message("[INFO] Spammer started")

    def stop(self):
        """Stop the spammer process."""
        if self.worker is not None and self.worker.isRunning():
            self.worker.stop()
            self.log_message("[WARNING] Stopping spammer...")
        else:
            self.log_message("[WARNING] No spammer process to stop")

    def update_progress(self, value):
        """Update the progress bar."""
        self.progress_bar.setValue(value)

    def update_status(self, status):
        """Update the status label."""
        self.status_label.setText(f"Status: {status}")

    def on_worker_finished(self):
        """Handle worker finished signal."""
        self.btn_start.setEnabled(True)
        self.btn_stop.setEnabled(False)

    def on_worker_stopped(self):
        """Handle worker stopped signal."""
        self.btn_start.setEnabled(True)
        self.btn_stop.setEnabled(False)

    def on_selector_detection_changed(self, state):
        """Handle selector detection checkbox state change."""
        # Enable/disable the use_first_profile_for_commenting checkbox based on the state of use_first_profile_for_selectors
        self.use_first_profile_for_commenting.setEnabled(state == Qt.Checked)
