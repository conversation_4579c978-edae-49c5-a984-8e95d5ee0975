from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QListWidget,
                            QPushButton, QLabel, QLineEdit, QMessageBox, QSplitter, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal

class BulkEditorDialog(QDialog):
    """Dialog for bulk editing of items (profiles, posts, comments, etc.)."""
    
    # Define signals
    items_updated = pyqtSignal(list)  # Signal emitted when items are updated
    
    def __init__(self, title, items=None, item_type="items", parent=None):
        """
        Initialize the bulk editor dialog.
        
        Args:
            title (str): Dialog title.
            items (list, optional): Initial list of items.
            item_type (str, optional): Type of items being edited (profiles, posts, comments).
            parent (QWidget, optional): Parent widget.
        """
        super().__init__(parent)
        
        self.setWindowTitle(title)
        self.resize(800, 600)
        self.item_type = item_type
        self.items = items or []
        
        # Setup UI
        self.setup_ui()
        
        # Populate list with initial items
        self.populate_list()
    
    def setup_ui(self):
        """Create and configure the UI components."""
        layout = QVBoxLayout(self)
        
        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # Top section - Bulk input
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        
        # Bulk input section
        bulk_label = QLabel(f"Paste multiple {self.item_type} (one per line):")
        self.bulk_text = QTextEdit()
        self.bulk_text.setPlaceholderText(f"Enter or paste multiple {self.item_type} here, one per line...")
        
        top_layout.addWidget(bulk_label)
        top_layout.addWidget(self.bulk_text)
        
        # Bulk actions
        bulk_actions = QHBoxLayout()
        self.btn_add_bulk = QPushButton(f"Add All {self.item_type}")
        self.btn_add_bulk.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        
        bulk_actions.addWidget(self.btn_add_bulk)
        bulk_actions.addStretch()
        
        top_layout.addLayout(bulk_actions)
        
        # Bottom section - Item list and single item input
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        
        # Current items list
        list_label = QLabel(f"Current {self.item_type}:")
        self.items_list = QListWidget()
        self.items_list.setSelectionMode(QListWidget.ExtendedSelection)
        
        bottom_layout.addWidget(list_label)
        bottom_layout.addWidget(self.items_list)
        
        # Single item input
        single_layout = QHBoxLayout()
        self.single_input = QLineEdit()
        self.single_input.setPlaceholderText(f"Enter a single {self.item_type[:-1]}...")
        self.btn_add_single = QPushButton("Add")
        self.btn_remove_selected = QPushButton("Remove Selected")
        self.btn_remove_selected.setStyleSheet("background-color: #f44336; color: white; font-weight: bold;")
        
        single_layout.addWidget(self.single_input)
        single_layout.addWidget(self.btn_add_single)
        single_layout.addWidget(self.btn_remove_selected)
        
        bottom_layout.addLayout(single_layout)
        
        # Add widgets to splitter
        splitter.addWidget(top_widget)
        splitter.addWidget(bottom_widget)
        
        # Set initial splitter sizes (40% top, 60% bottom)
        splitter.setSizes([300, 400])
        
        # Dialog buttons
        buttons_layout = QHBoxLayout()
        self.btn_save = QPushButton("Save")
        self.btn_save.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        self.btn_cancel = QPushButton("Cancel")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.btn_save)
        buttons_layout.addWidget(self.btn_cancel)
        
        layout.addLayout(buttons_layout)
        
        # Connect signals
        self.btn_add_bulk.clicked.connect(self.add_bulk_items)
        self.btn_add_single.clicked.connect(self.add_single_item)
        self.btn_remove_selected.clicked.connect(self.remove_selected_items)
        self.btn_save.clicked.connect(self.accept)
        self.btn_cancel.clicked.connect(self.reject)
        self.single_input.returnPressed.connect(self.add_single_item)
    
    def populate_list(self):
        """Populate the list with initial items."""
        self.items_list.clear()
        self.items_list.addItems(self.items)
    
    def add_bulk_items(self):
        """Add items from the bulk text area."""
        text = self.bulk_text.toPlainText().strip()
        if not text:
            return
        
        # Split text by newlines and filter out empty lines
        new_items = [line.strip() for line in text.split('\n') if line.strip()]
        
        # Check for CSV format (simple detection)
        if len(new_items) > 0 and ',' in new_items[0]:
            reply = QMessageBox.question(
                self,
                "CSV Format Detected",
                "It looks like you pasted CSV data. Do you want to extract the first column only?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # Extract first column from CSV
                new_items = [line.split(',')[0].strip() for line in new_items]
        
        # Add new items to the list
        for item in new_items:
            if item and item not in self.get_current_items():
                self.items_list.addItem(item)
        
        # Clear the bulk text area
        self.bulk_text.clear()
    
    def add_single_item(self):
        """Add a single item from the input field."""
        item_text = self.single_input.text().strip()
        if not item_text:
            return
        
        # Check if item already exists
        current_items = self.get_current_items()
        if item_text not in current_items:
            self.items_list.addItem(item_text)
        
        # Clear the input field
        self.single_input.clear()
        self.single_input.setFocus()
    
    def remove_selected_items(self):
        """Remove selected items from the list."""
        selected_items = self.items_list.selectedItems()
        if not selected_items:
            return
        
        reply = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to remove {len(selected_items)} selected {self.item_type}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for item in reversed(selected_items):  # Reverse to avoid index shifting
                self.items_list.takeItem(self.items_list.row(item))
    
    def get_current_items(self):
        """
        Get the current list of items.
        
        Returns:
            list: List of items.
        """
        return [self.items_list.item(i).text() for i in range(self.items_list.count())]
    
    def accept(self):
        """Handle dialog acceptance."""
        # Get the current items
        items = self.get_current_items()
        
        # Emit the items_updated signal
        self.items_updated.emit(items)
        
        # Call the parent class's accept method
        super().accept()
