import os
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTextEdit, QListWidget, QProgressBar,
                            QCheckBox, QSplitter, QFileDialog, QMessageBox, QInputDialog)
from PyQt5.QtCore import Qt, pyqtSlot

# Import dialogs
from ...dialogs.bulk_editor import BulkEditorDialog
from ...dialogs.csv_editor import CSVEditorDialog

# Import controller
from .logginer_controller import LogginerController

class LogginerTab(QWidget):
    """Logginer tab for managing account login operations."""

    def __init__(self):
        super().__init__()

        # Create controller
        self.controller = LogginerController()

        # Connect controller signals
        self.controller.progress_updated.connect(self.on_progress_updated)
        self.controller.log_message.connect(self.log_message)

        self.setup_ui()

    def setup_ui(self):
        """Create and configure the UI components."""
        main_layout = QHBoxLayout(self)

        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - configuration
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Not logged in accounts section
        self.not_logged_group = QGroupBox("Not Logged In Accounts (0)")
        not_logged_layout = QVBoxLayout()
        self.not_logged_list = QListWidget()

        not_logged_buttons = QHBoxLayout()
        self.btn_import_not_logged = QPushButton("Import File")
        self.btn_add_not_logged = QPushButton("Add New")
        self.btn_bulk_not_logged = QPushButton("Edit Accounts")
        self.btn_bulk_not_logged.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")

        not_logged_buttons.addWidget(self.btn_import_not_logged)
        not_logged_buttons.addWidget(self.btn_add_not_logged)
        not_logged_buttons.addWidget(self.btn_bulk_not_logged)

        not_logged_layout.addWidget(self.not_logged_list)
        not_logged_layout.addLayout(not_logged_buttons)
        self.not_logged_group.setLayout(not_logged_layout)

        # Accounts section
        self.accounts_group = QGroupBox("Accounts (0)")
        accounts_layout = QVBoxLayout()
        self.accounts_list = QListWidget()

        accounts_buttons = QHBoxLayout()
        self.btn_import_accounts = QPushButton("Import File")
        self.btn_add_account = QPushButton("Add New")
        self.btn_bulk_accounts = QPushButton("Edit Accounts")
        self.btn_bulk_accounts.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")

        accounts_buttons.addWidget(self.btn_import_accounts)
        accounts_buttons.addWidget(self.btn_add_account)
        accounts_buttons.addWidget(self.btn_bulk_accounts)

        accounts_layout.addWidget(self.accounts_list)
        accounts_layout.addLayout(accounts_buttons)
        self.accounts_group.setLayout(accounts_layout)

        # Add groups to left layout
        left_layout.addWidget(self.not_logged_group)
        left_layout.addWidget(self.accounts_group)

        # Right panel - controls and output
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Settings section
        self.settings_group = QGroupBox("Logginer Settings")
        settings_layout = QVBoxLayout()

        # Checkboxes for options
        self.auto_save = QCheckBox("Automatically save successful logins")
        self.headless_mode = QCheckBox("Headless mode")

        self.auto_save.setChecked(True)

        settings_layout.addWidget(self.auto_save)
        settings_layout.addWidget(self.headless_mode)

        self.settings_group.setLayout(settings_layout)

        # Control section
        self.control_group = QGroupBox("Control")
        control_layout = QVBoxLayout()

        self.btn_start = QPushButton("Start Logginer")
        self.btn_start.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")

        self.btn_stop = QPushButton("Stop Logginer")
        self.btn_stop.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 8px;")
        self.btn_stop.setEnabled(False)

        control_layout.addWidget(self.btn_start)
        control_layout.addWidget(self.btn_stop)

        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        control_layout.addLayout(progress_layout)

        # Status label
        self.status_label = QLabel("Status: Idle")
        control_layout.addWidget(self.status_label)

        self.control_group.setLayout(control_layout)

        # Results section
        self.results_group = QGroupBox("Results")
        results_layout = QVBoxLayout()

        # Statistics
        stats_layout = QVBoxLayout()
        self.accounts_processed_label = QLabel("Accounts Processed: 0/0")
        self.successful_logins_label = QLabel("Successful Logins: 0")
        self.failed_logins_label = QLabel("Failed Logins: 0")
        stats_layout.addWidget(self.accounts_processed_label)
        stats_layout.addWidget(self.successful_logins_label)
        stats_layout.addWidget(self.failed_logins_label)
        results_layout.addLayout(stats_layout)

        self.results_group.setLayout(results_layout)

        # Log section
        self.log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        self.log_group.setLayout(log_layout)

        # Add groups to right layout
        right_layout.addWidget(self.settings_group)
        right_layout.addWidget(self.control_group)
        right_layout.addWidget(self.results_group)
        right_layout.addWidget(self.log_group)

        # Add panels to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # Set initial splitter sizes (40% left, 60% right)
        splitter.setSizes([400, 600])

        # Connect signals
        self.connect_signals()

        # Load sample data
        self.load_sample_data()

    def update_item_counts(self):
        """Update the item counts in group titles."""
        # Get counts from database
        not_logged_count = self.controller.db.get_profiles_count(status="not_logged_in")
        accounts_count = self.controller.db.get_accounts_count()

        # Update UI
        self.not_logged_group.setTitle(f"Not Logged In Accounts ({not_logged_count})")
        self.accounts_group.setTitle(f"Accounts ({accounts_count})")

        # Update list widgets if they don't match the database
        if self.not_logged_list.count() != not_logged_count:
            self.refresh_not_logged_list()

        if self.accounts_list.count() != accounts_count:
            self.refresh_accounts_list()

    def connect_signals(self):
        """Connect signals to slots."""
        # Button signals for not logged in accounts
        self.btn_import_not_logged.clicked.connect(self.on_import_not_logged)
        self.btn_add_not_logged.clicked.connect(self.on_add_not_logged)
        self.btn_bulk_not_logged.clicked.connect(self.on_bulk_not_logged)

        # Button signals for accounts
        self.btn_import_accounts.clicked.connect(self.on_import_accounts)
        self.btn_add_account.clicked.connect(self.on_add_account)
        self.btn_bulk_accounts.clicked.connect(self.on_bulk_accounts)

        # Control buttons
        self.btn_start.clicked.connect(self.start)
        self.btn_stop.clicked.connect(self.stop)

    def refresh_not_logged_list(self):
        """Refresh the not logged in accounts list from the database."""
        not_logged_in = self.controller.load_not_logged_in()
        self.not_logged_list.clear()
        self.not_logged_list.addItems(not_logged_in)

    def refresh_accounts_list(self):
        """Refresh the accounts list from the database."""
        accounts = self.controller.load_accounts()
        self.accounts_list.clear()
        self.accounts_list.addItems(accounts)

    def load_sample_data(self):
        """Load sample data for demonstration."""
        # Load data from database
        self.refresh_not_logged_list()
        self.refresh_accounts_list()

        # Update item counts
        self.update_item_counts()

        # Log initial message
        self.log_message("[INFO] Sample data loaded")

    def log_message(self, message):
        """Add a message to the log."""
        self.log_text.append(message)
        # Auto-scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def on_import_not_logged(self):
        """Handle import not logged in accounts button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Not Logged In File", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing not logged in accounts from {file_path}")

            try:
                # Import not logged in accounts directly to database
                not_logged_in = self.controller.load_not_logged_in(file_path)

                # Refresh the not logged in accounts list
                self.refresh_not_logged_list()

                # Update item counts
                self.update_item_counts()

                # Show success message
                QMessageBox.information(self, "Import Successful", f"Successfully imported {len(not_logged_in)} not logged in accounts.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import not logged in accounts: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import not logged in accounts: {e}")

    def on_add_not_logged(self):
        """Handle add not logged in account button click."""
        account, ok = QInputDialog.getText(
            self, "Add Not Logged In Account", "Enter account ID:"
        )

        if ok and account.strip():
            account_id = account.strip()

            # Check if account exists in database
            existing_account = self.controller.db.get_profile(account_id)

            if not existing_account or existing_account.get('status') != 'not_logged_in':
                # Save account to database
                if self.controller.db.save_profile(account_id, status="not_logged_in"):
                    self.log_message(f"[INFO] Added new not logged in account: {account_id}")

                    # Refresh the not logged in accounts list
                    self.refresh_not_logged_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add not logged in account: {account_id}")
                    QMessageBox.critical(self, "Error", f"Failed to add not logged in account '{account_id}'.")
            else:
                self.log_message(f"[WARNING] Not logged in account already exists: {account_id}")
                QMessageBox.warning(self, "Duplicate Account", f"Account '{account_id}' already exists.")

    def on_bulk_not_logged(self):
        """Handle bulk edit not logged in accounts button click."""
        self.log_message("[INFO] Opening bulk not logged in accounts editor")

        # Get not logged in accounts from database
        accounts_data = self.controller.db.get_profiles(status="not_logged_in")
        current_accounts = [p['profile_id'] for p in accounts_data]

        # Create and show bulk editor dialog
        dialog = BulkEditorDialog("Bulk Edit Not Logged In Accounts", current_accounts, "not logged in accounts", self)

        # Connect the items_updated signal
        dialog.items_updated.connect(self.on_not_logged_updated)

        # Show dialog
        dialog.exec_()

    @pyqtSlot(list)
    def on_not_logged_updated(self, accounts):
        """Handle not logged in accounts updated from bulk editor."""
        self.log_message(f"[INFO] Updating not logged in accounts list with {len(accounts)} accounts")

        # Save not logged in accounts to database
        self.controller.save_not_logged_in(accounts)

        # Refresh the not logged in accounts list
        self.refresh_not_logged_list()

        # Update item counts
        self.update_item_counts()

    def on_import_accounts(self):
        """Handle import accounts button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Accounts File", "Files", "CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing accounts from {file_path}")

            try:
                # Import accounts directly to database
                accounts = self.controller.load_accounts(file_path)

                # Refresh the accounts list
                self.refresh_accounts_list()

                # Update item counts
                self.update_item_counts()

                # Show success message
                QMessageBox.information(self, "Import Successful", f"Successfully imported {len(accounts)} accounts.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import accounts: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import accounts: {e}")

    def on_add_account(self):
        """Handle add account button click."""
        account, ok = QInputDialog.getText(
            self, "Add Account", "Enter account ID:"
        )

        if ok and account.strip():
            account_id = account.strip()

            # Check if account exists in database
            existing_account = self.controller.db.get_profile(account_id)

            if not existing_account:
                # Save account to database
                if self.controller.db.save_profile(account_id):
                    self.log_message(f"[INFO] Added new account: {account_id}")

                    # Refresh the accounts list
                    self.refresh_accounts_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add account: {account_id}")
                    QMessageBox.critical(self, "Error", f"Failed to add account '{account_id}'.")
            else:
                self.log_message(f"[WARNING] Account already exists: {account_id}")
                QMessageBox.warning(self, "Duplicate Account", f"Account '{account_id}' already exists.")

    def on_bulk_accounts(self):
        """Handle bulk edit accounts button click."""
        self.log_message("[INFO] Opening accounts editor")

        # Default accounts file path
        file_path = os.path.join("Files", "Accounts.csv")

        # Open CSV editor dialog
        dialog = CSVEditorDialog(file_path, "Accounts Editor", self)
        if dialog.exec_():
            self.log_message(f"[INFO] Accounts file saved: {file_path}")

            # Import accounts from the saved file
            try:
                accounts = self.controller.load_accounts(file_path)
                self.log_message(f"[INFO] Loaded {len(accounts)} accounts from {file_path}")

                # Refresh the accounts list
                self.refresh_accounts_list()

                # Update item counts
                self.update_item_counts()
            except Exception as e:
                self.log_message(f"[ERROR] Failed to load accounts from {file_path}: {e}")

    def start(self):
        """Start the logginer process."""
        # Get accounts from list
        accounts = [self.not_logged_list.item(i).text() for i in range(self.not_logged_list.count())]

        # Get settings
        settings = {
            "auto_save": self.auto_save.isChecked(),
            "headless_mode": self.headless_mode.isChecked()
        }

        # Validate inputs
        if not accounts:
            QMessageBox.warning(self, "Warning", "No accounts to log in.")
            return

        # Update UI
        self.btn_start.setEnabled(False)
        self.btn_stop.setEnabled(True)
        self.status_label.setText("Status: Running")
        self.progress_bar.setValue(0)

        # Start logginer process
        self.controller.start_logginer(accounts, settings)

    def stop(self):
        """Stop the logginer process."""
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Are you sure you want to stop the logginer?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Update UI
            self.btn_start.setEnabled(True)
            self.btn_stop.setEnabled(False)
            self.status_label.setText("Status: Stopped")

            # Stop logginer process
            self.controller.stop_logginer()

            # Log stop
            self.log_message("[WARNING] Logginer stopped by user")

    @pyqtSlot(int, int, str)
    def on_progress_updated(self, current, total, message):
        """Handle progress update from controller."""
        # Update progress bar
        progress = int(current / total * 100) if total > 0 else 0
        self.progress_bar.setValue(progress)

        # Update status label
        self.status_label.setText(f"Status: {message}")
