/**
 * Facebook Post Links Extractor via Date/Time Click Method
 *
 * This script extracts post links by:
 * 1. Finding date/time elements in posts
 * 2. Clicking on them to navigate to the post
 * 3. Copying the URL from the browser
 * 4. Navigating back to the group
 * 5. Repeating for all visible posts
 */

const FacebookDateClickExtractor = (function() {
    // Configuration
    const config = {
        // Delay between actions (ms)
        minDelay: 1000,
        maxDelay: 3000,

        // Maximum number of posts to extract
        maxPosts: 3000,

        // Scroll settings
        scrollStep: 800,
        maxScrolls: 50,

        // Batch size for processing posts
        batchSize: 5,

        // Maximum number of consecutive failures before trying recovery
        maxFailures: 3,

        // Whether to skip already processed posts
        skipProcessed: true,

        // Original URL to return to after each post
        originalUrl: window.location.href,

        // Whether we're running in headless mode
        headlessMode: false,

        // Whether to use aggressive extraction methods
        aggressiveMode: false
    };

    // State tracking
    const state = {
        collectedUrls: [],
        processedElements: new WeakSet(),
        currentBatch: 0,
        failureCount: 0,
        isRunning: false,
        startTime: null,
        lastScrollPosition: 0
    };

    // Helper functions
    const helpers = {
        delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

        randomDelay: async function() {
            const delay = config.minDelay + Math.random() * (config.maxDelay - config.minDelay);
            console.log(`Waiting ${(delay/1000).toFixed(1)} seconds...`);
            await this.delay(delay);
        },

        scrollDown: async function() {
            const currentPosition = window.scrollY;

            // Always scroll forward, never backward
            const scrollAmount = Math.floor(window.innerHeight * (0.7 + Math.random() * 0.3)); // Increased scroll amount
            const newPosition = currentPosition + scrollAmount;

            console.log(`Scrolling down by ${scrollAmount}px to position ${newPosition}`);

            // First try smooth scrolling
            window.scrollTo({
                top: newPosition,
                behavior: 'smooth'
            });

            // Wait a bit for smooth scroll to complete
            await this.delay(500);

            // Check if scroll actually happened
            if (window.scrollY < newPosition - 100) {
                // If smooth scroll didn't work well, try direct scroll
                console.log('Smooth scroll not effective, using direct scroll');
                window.scrollTo(0, newPosition);
            }

            // Save last scroll position
            state.lastScrollPosition = window.scrollY;

            // Wait for content to load - longer wait to ensure all content loads
            await this.delay(2000 + Math.random() * 1000);

            // Try to force load more content by scrolling a tiny bit more
            window.scrollBy(0, 10);
            await this.delay(500);
            window.scrollBy(0, -5);
            await this.delay(500);
        },

        clickSeeMoreButtons: async function() {
            // Selectors for buttons that might be "See More" buttons
            const seeMoreButtons = [
                'div[role="button"]:not([aria-hidden="true"]):not([aria-disabled="true"])',
                'span[role="button"]',
                'a[role="button"]',
                'div.x1i10hfl[role="button"]',  // Facebook's dynamic class for buttons
                'div.x1n2onr6[role="button"]',  // Another Facebook dynamic class
                'div[aria-expanded="false"]',    // Collapsed content indicators
                'a.see_more_link',              // Classic see more link
                'a.UFIPagerLink',               // Comments expander
                'a.UFICommentLink'              // Reply expander
            ];

            // "See More" text in multiple languages
            const seeMoreTexts = [
                // English
                'see more', 'show more', 'view more', 'more comments', 'more replies', 'more results', 'load more',

                // Arabic
                'عرض المزيد', 'اقرأ المزيد', 'المزيد من التعليقات', 'مشاهدة المزيد', 'تحميل المزيد',

                // Spanish
                'ver más', 'mostrar más', 'más comentarios', 'cargar más',

                // French
                'voir plus', 'afficher plus', 'plus de commentaires', 'charger plus',

                // German
                'mehr anzeigen', 'weitere kommentare', 'mehr laden',

                // Portuguese
                'ver mais', 'mostrar mais', 'mais comentários', 'carregar mais',

                // Turkish
                'daha fazla', 'daha fazla yorum', 'daha fazla göster',

                // Indonesian
                'lihat selengkapnya', 'tampilkan lebih banyak', 'muat lebih banyak',

                // Russian
                'показать больше', 'еще комментарии', 'загрузить еще'
            ];

            let clicked = 0;

            // First try to find buttons by their aria-label
            try {
                const expandButtons = document.querySelectorAll('[aria-label*="expand"], [aria-label*="more"], [aria-label*="show"]');
                for (const button of expandButtons) {
                    try {
                        button.click();
                        clicked++;
                        console.log('Clicked expand button by aria-label');
                    } catch (e) {
                        console.error('Error clicking expand button:', e);
                    }
                }
            } catch (e) {
                console.error('Error finding expand buttons by aria-label:', e);
            }

            // Then try by selectors and text content
            for (const selector of seeMoreButtons) {
                try {
                    const buttons = document.querySelectorAll(selector);
                    for (const button of buttons) {
                        const text = button.textContent.toLowerCase();

                        // Check if the button text includes any of the "see more" phrases
                        const isSeeMoreButton = seeMoreTexts.some(phrase => text.includes(phrase.toLowerCase()));

                        if (isSeeMoreButton) {
                            try {
                                button.click();
                                clicked++;
                                console.log(`Clicked "See More" button with text: "${text.substring(0, 20)}..."`);

                                // Add a small delay to avoid clicking too many buttons at once
                                await new Promise(resolve => setTimeout(resolve, 100));
                            } catch (e) {
                                console.error('Error clicking button:', e);
                            }
                        }
                    }
                } catch (e) {
                    console.error(`Error with selector ${selector}:`, e);
                }
            }

            // Also try to find "See More" links by looking for small text links
            try {
                const smallLinks = document.querySelectorAll('a, span');
                for (const link of smallLinks) {
                    // Skip if it's a large element (likely not a "see more" link)
                    if (link.offsetWidth > 200 || link.offsetHeight > 50) continue;

                    const text = link.textContent.toLowerCase().trim();
                    if (text.length < 30) { // Only consider short text links
                        const isSeeMoreLink = seeMoreTexts.some(phrase => text.includes(phrase.toLowerCase()));

                        if (isSeeMoreLink) {
                            try {
                                link.click();
                                clicked++;
                                console.log(`Clicked "See More" link with text: "${text}"`);

                                // Add a small delay to avoid clicking too many links at once
                                await new Promise(resolve => setTimeout(resolve, 100));
                            } catch (e) {
                                console.error('Error clicking link:', e);
                            }
                        }
                    }
                }
            } catch (e) {
                console.error('Error finding small links:', e);
            }

            return clicked;
        },

        findDateElements: function() {
            // Comprehensive selectors for date/time elements
            const dateSelectors = [
                // Standard Facebook date elements with data attributes
                'a[href*="/posts/"] abbr[data-utime]',
                'a[href*="/permalink/"] abbr[data-utime]',
                'a[role="link"] abbr[data-utime]',
                'a[href*="/posts/"] span[data-sigil="timestamp"]',
                'a[href*="/permalink/"] span[data-sigil="timestamp"]',
                'span[data-testid="post-time"]',
                'span[data-testid="timestamp"]',

                // Facebook dynamic class selectors (these change frequently)
                'a[role="link"] span.x4k7w5x',
                'a[role="link"] span.x1yrsyyn',
                'a[role="link"] span.xrbpyxo',
                'a[role="link"] span.x1lliihq',
                'a[role="link"] span.x1iorvi4',
                'a[role="link"] span.x1lku1pv',
                'a[role="link"] span.x1a1m0xk',

                // General time indicators
                'span.timestampContent',
                'span.timestamp',
                'a[role="link"] span:not([class])',  // Unclassed spans in links that might contain dates

                // Fallback to any abbr element inside a link
                'a[role="link"] abbr',

                // Direct post link selectors (for headless mode)
                'a[href*="/posts/"]',
                'a[href*="/permalink/"]',
                'a[href*="story_fbid="]',
                'a[href*="fbid="]',
                'a[href*="/photo.php"]',
                'a[href*="/video.php"]'
            ];

            // Time-related text patterns in multiple languages
            const timePatterns = [
                // Short time formats (English)
                's ago', 'm ago', 'h ago', 'd ago', 'w ago', 'mo ago', 'y ago',
                'sec', 'min', 'hr', 'hrs', 'day', 'days', 'wk', 'wks', 'mo', 'mos', 'yr', 'yrs',

                // Numeric time formats (all languages)
                '1s', '2s', '3s', '4s', '5s', '10s', '15s', '20s', '30s', '45s', '60s',
                '1m', '2m', '3m', '4m', '5m', '10m', '15m', '20m', '30m', '45m', '60m',
                '1h', '2h', '3h', '4h', '5h', '6h', '8h', '10h', '12h', '24h',
                '1d', '2d', '3d', '4d', '5d', '6d', '7d', '14d', '30d',
                '1w', '2w', '3w', '4w',
                '1y', '2y', '3y', '4y', '5y', '10y',

                // English
                'seconds ago', 'second ago', 'minutes ago', 'minute ago', 'hours ago', 'hour ago',
                'yesterday at', 'yesterday', 'days ago', 'day ago', 'weeks ago', 'week ago',
                'months ago', 'month ago', 'years ago', 'year ago', 'just now',

                // Arabic
                'ثانية', 'ثواني', 'دقيقة', 'دقائق', 'ساعة', 'ساعات', 'يوم', 'أيام',
                'أسبوع', 'أسابيع', 'شهر', 'أشهر', 'سنة', 'سنوات', 'الأمس', 'البارحة', 'منذ',

                // Short time formats (Arabic)
                'ث', 'د', 'س', 'ي', 'أس', 'ش', 'سن',

                // French
                'secondes', 'seconde', 'minutes', 'minute', 'heures', 'heure',
                'hier à', 'hier', 'jours', 'jour', 'semaines', 'semaine',
                'mois', 'années', 'année', 'à l\'instant',

                // Spanish
                'segundos', 'segundo', 'minutos', 'minuto', 'horas', 'hora',
                'ayer a las', 'ayer', 'días', 'día', 'semanas', 'semana',
                'meses', 'mes', 'años', 'año', 'ahora mismo',

                // German
                'sekunden', 'sekunde', 'minuten', 'minute', 'stunden', 'stunde',
                'gestern um', 'gestern', 'tagen', 'tag', 'wochen', 'woche',
                'monaten', 'monat', 'jahren', 'jahr', 'gerade eben',

                // Turkish
                'saniye', 'dakika', 'saat', 'dün', 'gün', 'hafta', 'ay', 'yıl', 'önce',

                // Indonesian
                'detik', 'menit', 'jam', 'kemarin', 'hari', 'minggu', 'bulan', 'tahun', 'yang lalu',

                // Russian
                'секунд', 'минут', 'час', 'вчера', 'дней', 'день', 'недел', 'месяц', 'год', 'назад',

                // Portuguese
                'segundos', 'segundo', 'minutos', 'minuto', 'horas', 'hora',
                'ontem às', 'ontem', 'dias', 'dia', 'semanas', 'semana',
                'meses', 'mês', 'anos', 'ano', 'agora mesmo',

                // Common date formats
                'am', 'pm', 'AM', 'PM', ':', '/',

                // Months in multiple languages
                'january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december',
                'jan', 'feb', 'mar', 'apr', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec',
                'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre',
                'janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre',
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            // Regular expressions to match common time formats
            const timeRegexes = [
                /\b\d+\s*s\b/i,      // 10s, 20 s
                /\b\d+\s*m\b/i,      // 12m, 30 m
                /\b\d+\s*h\b/i,      // 1h, 2 h
                /\b\d+\s*d\b/i,      // 3d, 5 d
                /\b\d+\s*w\b/i,      // 2w, 3 w
                /\b\d+\s*mo\b/i,     // 1mo, 6 mo
                /\b\d+\s*y\b/i,      // 1y, 10 y
                /\b\d+\s*min\b/i,    // 5min, 10 min
                /\b\d+\s*hr\b/i,     // 1hr, 2 hr
                /\b\d+\s*sec\b/i,    // 30sec, 45 sec
                /\b\d+\s*day\b/i,    // 1day, 2 day
                /\b\d+\s*week\b/i,   // 1week, 3 week
                /\b\d+\s*month\b/i,  // 1month, 6 month
                /\b\d+\s*year\b/i,   // 1year, 5 year
                /\b\d+\s*دقيقة\b/i,  // Arabic minutes
                /\b\d+\s*ساعة\b/i,   // Arabic hours
                /\b\d+\s*يوم\b/i,    // Arabic days
                /\b\d+\s*شهر\b/i,    // Arabic months
                /\b\d+\s*سنة\b/i     // Arabic years
            ];

            // Find all date elements using selectors
            let dateElements = [];

            // If in headless mode, prioritize direct post link selectors
            if (config.headlessMode || config.aggressiveMode) {
                console.log('Using headless/aggressive mode for finding date elements');

                // First try direct post link selectors
                const directLinkSelectors = [
                    'a[href*="/posts/"]',
                    'a[href*="/permalink/"]',
                    'a[href*="story_fbid="]',
                    'a[href*="fbid="]',
                    'a[href*="/photo.php"]',
                    'a[href*="/video.php"]'
                ];

                for (const selector of directLinkSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            console.log(`Found ${elements.length} direct link elements with selector: ${selector}`);
                            dateElements = [...dateElements, ...Array.from(elements)];
                        }
                    } catch (e) {
                        console.error(`Error with direct link selector ${selector}:`, e);
                    }
                }

                // If we found direct links, return them immediately
                if (dateElements.length > 0) {
                    console.log(`Using ${dateElements.length} direct link elements in headless/aggressive mode`);
                    return dateElements;
                }
            }

            // Standard approach for non-headless mode or if direct links failed
            for (const selector of dateSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        console.log(`Found ${elements.length} date elements with selector: ${selector}`);
                        dateElements = [...dateElements, ...Array.from(elements)];
                    }
                } catch (e) {
                    console.error(`Error with selector ${selector}:`, e);
                }
            }

            // Find additional elements by text content
            try {
                // Get all potential elements that might contain date/time text
                const allTextElements = document.querySelectorAll('span, div, a');

                for (const element of allTextElements) {
                    // Skip if already in our collection
                    if (dateElements.includes(element)) continue;

                    // Skip elements that are too large to be time indicators
                    if (element.textContent.length > 30) continue;

                    const text = element.textContent.toLowerCase().trim();

                    // Check if the element contains any time-related pattern
                    const hasTimePattern = timePatterns.some(pattern =>
                        text.includes(pattern.toLowerCase())
                    );

                    // Check if the element matches any time regex
                    const hasTimeRegex = timeRegexes.some(regex =>
                        regex.test(text)
                    );

                    if (hasTimePattern || hasTimeRegex) {
                        // For text elements, find the closest link ancestor
                        let linkElement = element.closest('a[href]');

                        // If no direct parent link, try to find a nearby link
                        if (!linkElement) {
                            // Try parent elements up to 3 levels
                            let parent = element.parentElement;
                            for (let i = 0; i < 3 && parent; i++) {
                                const links = parent.querySelectorAll('a[href]');
                                if (links.length > 0) {
                                    linkElement = links[0];
                                    break;
                                }
                                parent = parent.parentElement;
                            }

                            // If still no link, try siblings
                            if (!linkElement && element.parentElement) {
                                const siblings = element.parentElement.children;
                                for (const sibling of siblings) {
                                    if (sibling.tagName === 'A' && sibling.href) {
                                        linkElement = sibling;
                                        break;
                                    }
                                }
                            }
                        }

                        if (linkElement) {
                            console.log(`Found date element by text pattern: "${text}"`);
                            dateElements.push(linkElement);
                        }
                    }
                }
            } catch (e) {
                console.error('Error finding elements by text content:', e);
            }

            // Remove duplicates by finding the closest link ancestor
            const uniqueLinks = new Map();

            for (const element of dateElements) {
                // Find the closest link ancestor
                const linkAncestor = element.closest('a[href]');
                if (!linkAncestor) continue;

                // Skip if we've already processed this element
                if (state.processedElements.has(linkAncestor)) continue;

                // Use the link as the key to avoid duplicates
                if (!uniqueLinks.has(linkAncestor)) {
                    uniqueLinks.set(linkAncestor, element);
                }
            }

            console.log(`Found ${uniqueLinks.size} unique date elements with link ancestors`);
            return Array.from(uniqueLinks.keys());
        },

        isPostLink: function(url) {
            if (!url) return false;

            // Skip messenger links and notification links
            if (url.includes('/messages/') || url.includes('/notifications/')) {
                return false;
            }

            // Check if it's a post link using various patterns
            return url.includes('/posts/') ||
                   url.includes('/permalink/') ||
                   url.includes('story_fbid=') ||
                   url.includes('fbid=') ||
                   url.includes('/photo.php') ||
                   url.includes('/video.php') ||
                   url.includes('/reel/') ||
                   url.includes('/watch/') ||
                   (url.includes('/groups/') && url.includes('/feed/') && url.includes('item_id=')) ||
                   (url.includes('/groups/') && url.includes('/permalink/')) ||
                   (url.includes('/groups/') && url.includes('/feed/item/')) ||
                   // Match post ID patterns in the URL
                   /\/\d{15,}/.test(url) || // Long numeric IDs are usually post IDs
                   /pfbid[\w]{22,}/.test(url); // pfbid format used in newer Facebook URLs
        },

        cleanPostUrl: function(url) {
            if (!url) return null;

            try {
                // Create URL object to manipulate the URL
                const urlObj = new URL(url);

                // First, extract the group ID and post ID if present in the URL
                let groupId = null;
                let postId = null;

                // Extract group ID from URL path
                const groupMatch = urlObj.pathname.match(/\/groups\/([^\/\?]+)/);
                if (groupMatch && groupMatch[1]) {
                    groupId = groupMatch[1];
                }

                // Extract post ID using different patterns
                if (urlObj.pathname.includes('/posts/')) {
                    // Format: /groups/{groupId}/posts/{postId}
                    const postMatch = urlObj.pathname.match(/\/posts\/([^\/\?]+)/);
                    if (postMatch && postMatch[1]) {
                        postId = postMatch[1];
                    }
                } else if (urlObj.pathname.includes('/permalink/')) {
                    // Format: /groups/{groupId}/permalink/{postId}
                    const postMatch = urlObj.pathname.match(/\/permalink\/([^\/\?]+)/);
                    if (postMatch && postMatch[1]) {
                        postId = postMatch[1];
                    }
                } else if (urlObj.searchParams.has('story_fbid')) {
                    // Format: ?story_fbid={postId}&id={groupId}
                    postId = urlObj.searchParams.get('story_fbid');
                    if (!groupId && urlObj.searchParams.has('id')) {
                        groupId = urlObj.searchParams.get('id');
                    }
                } else if (urlObj.pathname.includes('/photo.php') || urlObj.pathname.includes('/video.php')) {
                    // Format: /photo.php?fbid={postId} or /video.php?fbid={postId}
                    if (urlObj.searchParams.has('fbid')) {
                        postId = urlObj.searchParams.get('fbid');
                    }
                } else if (urlObj.pathname.includes('/feed/')) {
                    // Format: /groups/{groupId}/feed/?item_id={postId}
                    if (urlObj.searchParams.has('item_id')) {
                        postId = urlObj.searchParams.get('item_id');
                    }
                }

                // If we have both group ID and post ID, construct the canonical URL
                if (groupId && postId) {
                    return `${urlObj.origin}/groups/${groupId}/posts/${postId}`;
                }

                // If we only have post ID but no group ID, try to get group ID from current page URL
                if (postId && !groupId) {
                    const currentUrl = config.originalUrl || window.location.href;
                    const currentGroupMatch = currentUrl.match(/\/groups\/([^\/\?]+)/);
                    if (currentGroupMatch && currentGroupMatch[1]) {
                        groupId = currentGroupMatch[1];
                        return `${urlObj.origin}/groups/${groupId}/posts/${postId}`;
                    }
                }

                // Special case for URLs that already have the correct format but might have tracking params
                if (urlObj.pathname.includes('/groups/') && urlObj.pathname.includes('/posts/')) {
                    // Just clean the URL by removing tracking parameters
                    return `${urlObj.origin}${urlObj.pathname}`;
                }

                // If we couldn't extract both group ID and post ID, return the original URL with tracking removed
                // Remove tracking parameters
                let cleanSearch = urlObj.search;
                const trackingParams = ['fbclid', '__cft__', '__tn__'];

                for (const param of trackingParams) {
                    // Remove parameter if it's the first one (with ?)
                    const firstParamRegex = new RegExp(`\\?${param}=[^&]+(&?)`, 'g');
                    cleanSearch = cleanSearch.replace(firstParamRegex, (match, p1) => p1 ? '?' : '');

                    // Remove parameter if it's not the first one (with &)
                    const otherParamRegex = new RegExp(`&${param}=[^&]+`, 'g');
                    cleanSearch = cleanSearch.replace(otherParamRegex, '');
                }

                // Remove hash fragments (often used for tracking)
                urlObj.hash = '';
                urlObj.search = cleanSearch;

                return urlObj.toString();
            } catch (e) {
                console.error('Error cleaning URL:', e);
                return url;
            }
        }
    };

    // Main extraction functions
    const extractor = {
        processDateElement: async function(linkElement) {
            if (!linkElement || !linkElement.href) {
                console.log('Invalid link element');
                return null;
            }

            // Skip if we've already processed this element
            if (config.skipProcessed && state.processedElements.has(linkElement)) {
                console.log('Skipping already processed element');
                return null;
            }

            try {
                // Check if the link is already a post link
                if (helpers.isPostLink(linkElement.href)) {
                    console.log('Direct post link found:', linkElement.href);
                    state.processedElements.add(linkElement);
                    return helpers.cleanPostUrl(linkElement.href);
                }

                // If in headless mode, try to extract post info directly from the link
                if (config.headlessMode || config.aggressiveMode) {
                    console.log('Using headless/aggressive mode for processing link:', linkElement.href);

                    try {
                        // Try to extract group ID and post ID from the link href
                        const urlObj = new URL(linkElement.href);

                        // Check if it's a group URL or might lead to a post
                        if (urlObj.pathname.includes('/groups/') ||
                            urlObj.pathname.includes('/posts/') ||
                            urlObj.pathname.includes('/permalink/') ||
                            urlObj.search.includes('story_fbid=') ||
                            urlObj.search.includes('fbid=')) {

                            // Mark as processed to avoid reprocessing
                            state.processedElements.add(linkElement);

                            // Clean the URL and return it
                            const cleanUrl = helpers.cleanPostUrl(linkElement.href);
                            console.log('Extracted post URL in headless mode:', cleanUrl);
                            return cleanUrl;
                        }
                    } catch (parseError) {
                        console.error('Error parsing link in headless mode:', parseError);
                    }
                }

                // Standard approach for non-headless mode or if direct extraction failed
                // Store the current URL to return to
                const returnUrl = config.originalUrl;

                // Click the link to navigate to the post
                console.log('Clicking date element to navigate to post...');
                linkElement.click();

                // Wait for navigation
                await helpers.delay(3000);

                // Get the current URL (should be the post URL)
                const postUrl = window.location.href;
                console.log('Navigated to:', postUrl);

                // Check if it's a valid post URL
                if (helpers.isPostLink(postUrl)) {
                    // Clean the URL
                    const cleanUrl = helpers.cleanPostUrl(postUrl);
                    console.log('Extracted post URL:', cleanUrl);

                    // Navigate back to the original page
                    console.log('Navigating back to original page...');
                    window.location.href = returnUrl;

                    // Wait for navigation back
                    await helpers.delay(3000);

                    // Mark as processed
                    state.processedElements.add(linkElement);

                    return cleanUrl;
                } else {
                    // Try to extract post ID from the URL even if it's not recognized as a post link
                    // This is a fallback for cases where the URL format is not recognized
                    try {
                        const urlObj = new URL(postUrl);

                        // Check if it's a group URL
                        if (urlObj.pathname.includes('/groups/')) {
                            // Try to extract group ID and post ID
                            const groupMatch = urlObj.pathname.match(/\/groups\/([^\/\?]+)/);
                            let groupId = groupMatch ? groupMatch[1] : null;

                            // Look for post ID in various parts of the URL
                            let postId = null;

                            // Check for numeric IDs in the path
                            const numericIdMatch = urlObj.pathname.match(/\/(\d{15,})/);
                            if (numericIdMatch) {
                                postId = numericIdMatch[1];
                            }

                            // Check for pfbid format
                            const pfbidMatch = urlObj.pathname.match(/\/(pfbid[\w]{22,})/);
                            if (!postId && pfbidMatch) {
                                postId = pfbidMatch[1];
                            }

                            // If we have both group ID and post ID, construct a clean URL
                            if (groupId && postId) {
                                const constructedUrl = `${urlObj.origin}/groups/${groupId}/posts/${postId}`;
                                console.log('Constructed post URL from parts:', constructedUrl);

                                // Navigate back to the original page
                                console.log('Navigating back to original page...');
                                window.location.href = returnUrl;

                                // Wait for navigation back
                                await helpers.delay(3000);

                                // Mark as processed
                                state.processedElements.add(linkElement);

                                return constructedUrl;
                            }
                        }
                    } catch (parseError) {
                        console.error('Error parsing URL:', parseError);
                    }

                    console.log('Not a valid post URL:', postUrl);

                    // Navigate back to the original page
                    console.log('Navigating back to original page...');
                    window.location.href = returnUrl;

                    // Wait for navigation back
                    await helpers.delay(3000);

                    return null;
                }
            } catch (e) {
                console.error('Error processing date element:', e);

                // Try to navigate back to the original page
                try {
                    console.log('Attempting to navigate back to original page after error...');
                    window.location.href = config.originalUrl;
                    await helpers.delay(3000);
                } catch (navError) {
                    console.error('Error navigating back:', navError);
                }

                return null;
            }
        },

        processBatch: async function() {
            // First try to click "See More" buttons to expand content
            const clickCount = await helpers.clickSeeMoreButtons();
            if (clickCount > 0) {
                console.log(`Clicked ${clickCount} "See More" buttons`);
                // Wait a bit for content to expand
                await helpers.delay(1000);
            }

            // Find date elements
            let dateLinks = helpers.findDateElements();
            console.log(`Found ${dateLinks.length} date elements to process`);

            // If we didn't find any date elements, try scrolling and searching again
            if (dateLinks.length === 0) {
                state.failureCount++;
                console.log(`No date elements found (failure ${state.failureCount}/${config.maxFailures})`);

                // Scroll down to load more content
                await helpers.scrollDown();

                // Try to find date elements again after scrolling
                dateLinks = helpers.findDateElements();
                console.log(`After scrolling, found ${dateLinks.length} date elements to process`);

                // If we still didn't find any date elements, try recovery
                if (dateLinks.length === 0) {
                    // Try recovery if we've had too many failures
                    if (state.failureCount >= config.maxFailures) {
                        console.log('Too many failures, trying aggressive recovery...');

                        // Try a more aggressive scroll
                        for (let i = 0; i < 3; i++) {
                            await helpers.scrollDown();
                            await helpers.delay(1000);
                        }

                        // Try to click "See More" buttons again
                        const moreClickCount = await helpers.clickSeeMoreButtons();
                        if (moreClickCount > 0) {
                            console.log(`Clicked ${moreClickCount} more "See More" buttons`);
                            await helpers.delay(1000);
                        }

                        // Try to find date elements one more time
                        dateLinks = helpers.findDateElements();
                        console.log(`After recovery, found ${dateLinks.length} date elements to process`);

                        // Reset failure count
                        state.failureCount = 0;
                    }

                    // If we still didn't find any date elements, return empty array
                    if (dateLinks.length === 0) {
                        return [];
                    }
                } else {
                    // Reset failure count since we found date elements after scrolling
                    state.failureCount = 0;
                }
            } else {
                // Reset failure count since we found date elements
                state.failureCount = 0;
            }

            // Reset failure count on success
            state.failureCount = 0;

            // Process a batch of date elements
            const batchSize = Math.min(config.batchSize, dateLinks.length);
            const batch = dateLinks.slice(0, batchSize);

            console.log(`Processing batch of ${batch.length} date elements`);

            const batchResults = [];
            for (let i = 0; i < batch.length; i++) {
                const link = batch[i];

                console.log(`Processing date element ${i+1}/${batch.length}`);
                const postUrl = await this.processDateElement(link);

                if (postUrl) {
                    batchResults.push(postUrl);
                    console.log(`Successfully extracted URL: ${postUrl}`);
                }

                // Add a small delay between processing elements
                if (i < batch.length - 1) {
                    await helpers.delay(1000);
                }
            }

            console.log(`Batch processing complete. Extracted ${batchResults.length} URLs`);

            // Scroll down to load more content
            await helpers.scrollDown();

            return batchResults;
        }
    };

    // Public API
    return {
        // Initialize the extractor
        init: function(options) {
            // Apply custom options
            if (options) {
                Object.assign(config, options);
                console.log('Applied custom options:', options);

                // If headless mode is enabled, automatically enable aggressive mode
                if (config.headlessMode) {
                    config.aggressiveMode = true;
                    console.log('Headless mode detected, enabling aggressive mode');
                }
            }

            // Store the original URL
            config.originalUrl = window.location.href;
            console.log('Original URL:', config.originalUrl);

            // Log the current configuration
            console.log('Extractor initialized with config:', {
                headlessMode: config.headlessMode,
                aggressiveMode: config.aggressiveMode,
                maxPosts: config.maxPosts,
                maxScrolls: config.maxScrolls,
                batchSize: config.batchSize
            });

            return this;
        },

        // Start the extraction process
        start: async function() {
            console.log('Starting date click extraction process...');

            // Reset state
            state.collectedUrls = [];
            state.currentBatch = 0;
            state.failureCount = 0;
            state.isRunning = true;
            state.startTime = Date.now();

            // Click "See More" buttons before starting
            await helpers.clickSeeMoreButtons();

            while (state.isRunning && state.collectedUrls.length < config.maxPosts) {
                state.currentBatch++;
                console.log(`Processing batch ${state.currentBatch}...`);

                // Process a batch of date elements
                const batchUrls = await extractor.processBatch();

                // Add unique URLs to the collection
                for (const url of batchUrls) {
                    if (!state.collectedUrls.includes(url)) {
                        state.collectedUrls.push(url);

                        // Log progress
                        if (state.collectedUrls.length % 10 === 0) {
                            console.log(`Collected ${state.collectedUrls.length} unique URLs so far`);
                        }

                        // Check if we've reached the maximum
                        if (state.collectedUrls.length >= config.maxPosts) {
                            console.log(`Reached maximum post count (${config.maxPosts}), stopping extraction`);
                            break;
                        }
                    }
                }

                // Add a delay between batches
                await helpers.randomDelay();
            }

            // Log completion
            const elapsedSeconds = Math.round((Date.now() - state.startTime) / 1000);
            console.log(`Extraction complete! Collected ${state.collectedUrls.length} unique URLs in ${elapsedSeconds} seconds`);

            return state.collectedUrls;
        },

        // Stop the extraction process
        stop: function() {
            state.isRunning = false;
            console.log('Stopping extraction process...');
        },

        // Get the collected URLs
        getUrls: function() {
            return state.collectedUrls;
        },

        // Process a single batch and return the results
        extractNextBatch: async function() {
            if (state.collectedUrls.length >= config.maxPosts) {
                console.log(`Already reached maximum post count (${config.maxPosts})`);
                return [];
            }

            state.currentBatch++;
            console.log(`Processing batch ${state.currentBatch}...`);

            // Process a batch of date elements
            const batchUrls = await extractor.processBatch();

            // Add unique URLs to the collection
            const newUrls = [];
            for (const url of batchUrls) {
                if (!state.collectedUrls.includes(url)) {
                    state.collectedUrls.push(url);
                    newUrls.push(url);
                }
            }

            console.log(`Added ${newUrls.length} new unique URLs (total: ${state.collectedUrls.length})`);
            return newUrls;
        },

        // Update configuration
        setConfig: function(newConfig) {
            Object.assign(config, newConfig);
            console.log('Updated configuration:', config);
            return true;
        }
    };
})();

// Make the extractor available globally
window.FacebookDateClickExtractor = FacebookDateClickExtractor;
console.log('Facebook Date Click Extractor initialized and available globally');
