import os
import datetime
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTextEdit, QListWidget, QProgressBar,
                            QCheckBox, QSpinBox, QSplitter, QFileDialog, QMessageBox, QInputDialog,
                            QTabWidget, QLineEdit, QComboBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QDateEdit, QFormLayout, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSlot, QDate
from PyQt5.QtGui import QColor, QPalette

# Import for charts
try:
    from PyQt5.QtChart import QChart, QChartView, QLineSeries, QBarSet, QBarSeries, QValueAxis, QDateTimeAxis, QBarCategoryAxis
    from PyQt5.QtGui import QPainter
    CHARTS_AVAILABLE = True
except ImportError:
    CHARTS_AVAILABLE = False

from core.utils.database import Database

class StatisticsTab(QWidget):
    """Statistics tab for displaying database statistics and search functionality."""

    def __init__(self):
        super().__init__()
        
        # Create database instance
        self.db = Database()
        
        # Setup UI
        self.setup_ui()
        
        # Load initial data
        self.load_statistics()
    
    def setup_ui(self):
        """Setup the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Create tabs
        self.tabs = QTabWidget()
        self.tabs.addTab(self.create_overview_tab(), "Overview")
        self.tabs.addTab(self.create_search_tab(), "Search")
        self.tabs.addTab(self.create_backup_tab(), "Backup & Restore")
        
        main_layout.addWidget(self.tabs)
    
    def create_overview_tab(self):
        """Create the overview tab with statistics."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Statistics group
        stats_group = QGroupBox("Database Statistics")
        stats_layout = QGridLayout()
        
        # Create labels for statistics
        self.stats_labels = {
            "profiles_total": QLabel("0"),
            "profiles_active": QLabel("0"),
            "profiles_banned": QLabel("0"),
            "profiles_not_logged_in": QLabel("0"),
            "posts_total": QLabel("0"),
            "posts_active": QLabel("0"),
            "comments_total": QLabel("0"),
            "comments_active": QLabel("0"),
            "accounts_total": QLabel("0"),
            "accounts_active": QLabel("0"),
            "groups_total": QLabel("0"),
            "groups_active": QLabel("0"),
            "members_total": QLabel("0"),
            "members_active": QLabel("0"),
            "database_size": QLabel("0 B")
        }
        
        # Add labels to layout
        row = 0
        stats_layout.addWidget(QLabel("<b>Profiles:</b>"), row, 0)
        stats_layout.addWidget(QLabel("Total:"), row, 1)
        stats_layout.addWidget(self.stats_labels["profiles_total"], row, 2)
        stats_layout.addWidget(QLabel("Active:"), row, 3)
        stats_layout.addWidget(self.stats_labels["profiles_active"], row, 4)
        
        row += 1
        stats_layout.addWidget(QLabel(""), row, 0)
        stats_layout.addWidget(QLabel("Banned:"), row, 1)
        stats_layout.addWidget(self.stats_labels["profiles_banned"], row, 2)
        stats_layout.addWidget(QLabel("Not Logged In:"), row, 3)
        stats_layout.addWidget(self.stats_labels["profiles_not_logged_in"], row, 4)
        
        row += 1
        stats_layout.addWidget(QLabel("<b>Posts:</b>"), row, 0)
        stats_layout.addWidget(QLabel("Total:"), row, 1)
        stats_layout.addWidget(self.stats_labels["posts_total"], row, 2)
        stats_layout.addWidget(QLabel("Active:"), row, 3)
        stats_layout.addWidget(self.stats_labels["posts_active"], row, 4)
        
        row += 1
        stats_layout.addWidget(QLabel("<b>Comments:</b>"), row, 0)
        stats_layout.addWidget(QLabel("Total:"), row, 1)
        stats_layout.addWidget(self.stats_labels["comments_total"], row, 2)
        stats_layout.addWidget(QLabel("Active:"), row, 3)
        stats_layout.addWidget(self.stats_labels["comments_active"], row, 4)
        
        row += 1
        stats_layout.addWidget(QLabel("<b>Accounts:</b>"), row, 0)
        stats_layout.addWidget(QLabel("Total:"), row, 1)
        stats_layout.addWidget(self.stats_labels["accounts_total"], row, 2)
        stats_layout.addWidget(QLabel("Active:"), row, 3)
        stats_layout.addWidget(self.stats_labels["accounts_active"], row, 4)
        
        row += 1
        stats_layout.addWidget(QLabel("<b>Groups:</b>"), row, 0)
        stats_layout.addWidget(QLabel("Total:"), row, 1)
        stats_layout.addWidget(self.stats_labels["groups_total"], row, 2)
        stats_layout.addWidget(QLabel("Active:"), row, 3)
        stats_layout.addWidget(self.stats_labels["groups_active"], row, 4)
        
        row += 1
        stats_layout.addWidget(QLabel("<b>Members:</b>"), row, 0)
        stats_layout.addWidget(QLabel("Total:"), row, 1)
        stats_layout.addWidget(self.stats_labels["members_total"], row, 2)
        stats_layout.addWidget(QLabel("Active:"), row, 3)
        stats_layout.addWidget(self.stats_labels["members_active"], row, 4)
        
        row += 1
        stats_layout.addWidget(QLabel("<b>Database Size:</b>"), row, 0)
        stats_layout.addWidget(self.stats_labels["database_size"], row, 1, 1, 4)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # Activity chart
        if CHARTS_AVAILABLE:
            chart_group = QGroupBox("Activity Chart (Last 30 Days)")
            chart_layout = QVBoxLayout()
            
            # Date range selection
            date_layout = QHBoxLayout()
            date_layout.addWidget(QLabel("From:"))
            self.date_from = QDateEdit()
            self.date_from.setDate(QDate.currentDate().addDays(-30))
            self.date_from.setCalendarPopup(True)
            date_layout.addWidget(self.date_from)
            
            date_layout.addWidget(QLabel("To:"))
            self.date_to = QDateEdit()
            self.date_to.setDate(QDate.currentDate())
            self.date_to.setCalendarPopup(True)
            date_layout.addWidget(self.date_to)
            
            self.btn_update_chart = QPushButton("Update Chart")
            self.btn_update_chart.clicked.connect(self.update_activity_chart)
            date_layout.addWidget(self.btn_update_chart)
            
            date_layout.addStretch()
            chart_layout.addLayout(date_layout)
            
            # Create chart view
            self.chart_view = QChartView()
            self.chart_view.setRenderHint(QPainter.Antialiasing)
            chart_layout.addWidget(self.chart_view)
            
            chart_group.setLayout(chart_layout)
            layout.addWidget(chart_group)
        else:
            # Show message that charts are not available
            chart_msg = QLabel("Charts are not available. Install PyQt5.QtChart to enable charts.")
            chart_msg.setStyleSheet("color: red;")
            layout.addWidget(chart_msg)
        
        # Refresh button
        refresh_layout = QHBoxLayout()
        self.btn_refresh = QPushButton("Refresh Statistics")
        self.btn_refresh.clicked.connect(self.load_statistics)
        refresh_layout.addStretch()
        refresh_layout.addWidget(self.btn_refresh)
        layout.addLayout(refresh_layout)
        
        return tab
    
    def create_search_tab(self):
        """Create the search tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Search controls
        search_group = QGroupBox("Search")
        search_layout = QHBoxLayout()
        
        # Search type
        self.search_type = QComboBox()
        self.search_type.addItems(["Profiles", "Posts", "Comments", "Accounts", "Groups", "Members"])
        search_layout.addWidget(self.search_type)
        
        # Search query
        self.search_query = QLineEdit()
        self.search_query.setPlaceholderText("Enter search query...")
        self.search_query.returnPressed.connect(self.search)
        search_layout.addWidget(self.search_query)
        
        # Status filter
        self.status_filter = QComboBox()
        self.status_filter.addItems(["All", "Active", "Banned", "Not Logged In"])
        search_layout.addWidget(self.status_filter)
        
        # Search button
        self.btn_search = QPushButton("Search")
        self.btn_search.clicked.connect(self.search)
        search_layout.addWidget(self.btn_search)
        
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels(["ID", "Name/URL", "Status", "Created", "Updated"])
        self.results_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        layout.addWidget(self.results_table)
        
        # Pagination controls
        pagination_layout = QHBoxLayout()
        
        self.page_info = QLabel("Page 1 of 1")
        pagination_layout.addWidget(self.page_info)
        
        pagination_layout.addStretch()
        
        self.btn_prev_page = QPushButton("Previous")
        self.btn_prev_page.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.btn_prev_page)
        
        self.btn_next_page = QPushButton("Next")
        self.btn_next_page.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.btn_next_page)
        
        layout.addLayout(pagination_layout)
        
        # Initialize pagination variables
        self.current_page = 1
        self.total_pages = 1
        self.page_size = 100
        self.current_search = {"type": "", "query": "", "status": ""}
        
        # Update pagination buttons
        self.update_pagination()
        
        return tab
    
    def create_backup_tab(self):
        """Create the backup and restore tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Backup group
        backup_group = QGroupBox("Create Backup")
        backup_layout = QHBoxLayout()
        
        self.backup_path = QLineEdit()
        self.backup_path.setPlaceholderText("Backup file path (leave empty for automatic)")
        self.backup_path.setReadOnly(True)
        backup_layout.addWidget(self.backup_path)
        
        self.btn_browse_backup = QPushButton("Browse...")
        self.btn_browse_backup.clicked.connect(self.browse_backup_path)
        backup_layout.addWidget(self.btn_browse_backup)
        
        self.btn_create_backup = QPushButton("Create Backup")
        self.btn_create_backup.clicked.connect(self.create_backup)
        backup_layout.addWidget(self.btn_create_backup)
        
        backup_group.setLayout(backup_layout)
        layout.addWidget(backup_group)
        
        # Restore group
        restore_group = QGroupBox("Restore from Backup")
        restore_layout = QVBoxLayout()
        
        # Backups list
        self.backups_list = QTableWidget()
        self.backups_list.setColumnCount(5)
        self.backups_list.setHorizontalHeaderLabels(["Filename", "Date", "Size", "Records", ""])
        self.backups_list.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        restore_layout.addWidget(self.backups_list)
        
        # Restore controls
        restore_buttons = QHBoxLayout()
        
        self.btn_refresh_backups = QPushButton("Refresh List")
        self.btn_refresh_backups.clicked.connect(self.load_backups)
        restore_buttons.addWidget(self.btn_refresh_backups)
        
        restore_buttons.addStretch()
        
        self.btn_restore_backup = QPushButton("Restore Selected")
        self.btn_restore_backup.clicked.connect(self.restore_backup)
        restore_buttons.addWidget(self.btn_restore_backup)
        
        restore_layout.addLayout(restore_buttons)
        restore_group.setLayout(restore_layout)
        layout.addWidget(restore_group)
        
        # Load backups
        self.load_backups()
        
        return tab
    
    def load_statistics(self):
        """Load and display database statistics."""
        # Get statistics from database
        stats = self.db.get_statistics()
        
        # Update labels
        if "profiles" in stats:
            self.stats_labels["profiles_total"].setText(str(stats["profiles"]["total"]))
            self.stats_labels["profiles_active"].setText(str(stats["profiles"]["active"]))
            self.stats_labels["profiles_banned"].setText(str(stats["profiles"]["banned"]))
            self.stats_labels["profiles_not_logged_in"].setText(str(stats["profiles"]["not_logged_in"]))
        
        if "posts" in stats:
            self.stats_labels["posts_total"].setText(str(stats["posts"]["total"]))
            self.stats_labels["posts_active"].setText(str(stats["posts"]["active"]))
        
        if "comments" in stats:
            self.stats_labels["comments_total"].setText(str(stats["comments"]["total"]))
            self.stats_labels["comments_active"].setText(str(stats["comments"]["active"]))
        
        if "accounts" in stats:
            self.stats_labels["accounts_total"].setText(str(stats["accounts"]["total"]))
            self.stats_labels["accounts_active"].setText(str(stats["accounts"]["active"]))
        
        if "groups" in stats:
            self.stats_labels["groups_total"].setText(str(stats["groups"]["total"]))
            self.stats_labels["groups_active"].setText(str(stats["groups"]["active"]))
        
        if "members" in stats:
            self.stats_labels["members_total"].setText(str(stats["members"]["total"]))
            self.stats_labels["members_active"].setText(str(stats["members"]["active"]))
        
        if "database_size" in stats:
            size_formatted = self.db.format_size(stats["database_size"])
            self.stats_labels["database_size"].setText(size_formatted)
        
        # Update activity chart if available
        if CHARTS_AVAILABLE:
            self.update_activity_chart()
    
    def update_activity_chart(self):
        """Update the activity chart with data from the selected date range."""
        if not CHARTS_AVAILABLE:
            return
        
        # Get date range
        date_from = self.date_from.date().toString("yyyy-MM-dd")
        date_to = self.date_to.date().toString("yyyy-MM-dd")
        
        # Calculate days difference
        days = self.date_from.date().daysTo(self.date_to.date()) + 1
        
        # Get activity stats
        activity_stats = self.db.get_activity_stats(days)
        
        # Create chart
        chart = QChart()
        chart.setTitle("Activity by Day")
        chart.setAnimationOptions(QChart.SeriesAnimations)
        
        # Create series for each data type
        series_dict = {}
        for data_type in ["profiles", "posts", "comments", "accounts", "groups", "members"]:
            series = QLineSeries()
            series.setName(data_type.capitalize())
            series_dict[data_type] = series
        
        # Create a set of all dates in the range
        all_dates = set()
        for data_type, data in activity_stats.items():
            for item in data:
                all_dates.add(item["date"])
        
        # Sort dates
        all_dates = sorted(all_dates)
        
        # Create a dictionary to store counts by date for each data type
        counts_by_date = {data_type: {date: 0 for date in all_dates} for data_type in activity_stats.keys()}
        
        # Fill in the actual counts
        for data_type, data in activity_stats.items():
            for item in data:
                counts_by_date[data_type][item["date"]] = item["count"]
        
        # Add data points to series
        for date in all_dates:
            # Convert date string to QDateTime
            dt = QDate.fromString(date, "yyyy-MM-dd").startOfDay()
            msecs = dt.toMSecsSinceEpoch()
            
            # Add point to each series
            for data_type, counts in counts_by_date.items():
                series_dict[data_type].append(msecs, counts[date])
        
        # Add series to chart
        for series in series_dict.values():
            chart.addSeries(series)
        
        # Create axes
        axis_x = QDateTimeAxis()
        axis_x.setFormat("yyyy-MM-dd")
        axis_x.setTitleText("Date")
        chart.addAxis(axis_x, Qt.AlignBottom)
        
        axis_y = QValueAxis()
        axis_y.setLabelFormat("%i")
        axis_y.setTitleText("Count")
        chart.addAxis(axis_y, Qt.AlignLeft)
        
        # Attach axes to series
        for series in series_dict.values():
            series.attachAxis(axis_x)
            series.attachAxis(axis_y)
        
        # Set chart in view
        self.chart_view.setChart(chart)
    
    def search(self):
        """Perform search based on current criteria."""
        # Get search parameters
        search_type = self.search_type.currentText().lower()
        query = self.search_query.text().strip()
        status = self.status_filter.currentText().lower()
        
        # Reset pagination
        self.current_page = 1
        
        # Store current search
        self.current_search = {
            "type": search_type,
            "query": query,
            "status": status if status != "all" else None
        }
        
        # Perform search
        self.load_search_results()
    
    def load_search_results(self):
        """Load search results for the current page."""
        # Calculate offset
        offset = (self.current_page - 1) * self.page_size
        
        # Get results based on search type
        results = []
        if self.current_search["type"] == "profiles":
            results = self.db.search_profiles(
                self.current_search["query"],
                self.current_search["status"],
                self.page_size,
                offset
            )
        elif self.current_search["type"] == "posts":
            results = self.db.search_posts(
                self.current_search["query"],
                self.current_search["status"],
                self.page_size,
                offset
            )
        elif self.current_search["type"] == "comments":
            results = self.db.search_comments(
                self.current_search["query"],
                self.current_search["status"],
                self.page_size,
                offset
            )
        elif self.current_search["type"] == "accounts":
            results = self.db.search_accounts(
                self.current_search["query"],
                self.current_search["status"],
                self.page_size,
                offset
            )
        elif self.current_search["type"] == "groups":
            results = self.db.search_groups(
                self.current_search["query"],
                self.current_search["status"],
                self.page_size,
                offset
            )
        elif self.current_search["type"] == "members":
            results = self.db.search_members(
                self.current_search["query"],
                self.current_search["status"],
                self.page_size,
                offset
            )
        
        # Update table
        self.update_results_table(results)
        
        # Update pagination
        # For simplicity, we'll assume there are more pages if we got a full page of results
        self.total_pages = self.current_page + 1 if len(results) == self.page_size else self.current_page
        self.update_pagination()
    
    def update_results_table(self, results):
        """Update the results table with search results."""
        # Clear table
        self.results_table.setRowCount(0)
        
        # Set column headers based on search type
        if self.current_search["type"] == "profiles":
            self.results_table.setHorizontalHeaderLabels(["ID", "Profile ID", "Status", "Created", "Updated"])
        elif self.current_search["type"] == "posts":
            self.results_table.setHorizontalHeaderLabels(["ID", "Post URL", "Status", "Created", "Updated"])
        elif self.current_search["type"] == "comments":
            self.results_table.setHorizontalHeaderLabels(["ID", "Content", "Status", "Created", "Updated"])
        elif self.current_search["type"] == "accounts":
            self.results_table.setHorizontalHeaderLabels(["ID", "Username", "Status", "Created", "Updated"])
        elif self.current_search["type"] == "groups":
            self.results_table.setHorizontalHeaderLabels(["ID", "Group URL", "Status", "Created", "Updated"])
        elif self.current_search["type"] == "members":
            self.results_table.setHorizontalHeaderLabels(["ID", "Member ID", "Status", "Created", "Updated"])
        
        # Add results to table
        for row, result in enumerate(results):
            self.results_table.insertRow(row)
            
            # ID
            id_item = QTableWidgetItem(str(result["id"]))
            id_item.setFlags(id_item.flags() & ~Qt.ItemIsEditable)
            self.results_table.setItem(row, 0, id_item)
            
            # Name/URL/Content
            if self.current_search["type"] == "profiles":
                name_item = QTableWidgetItem(result["profile_id"])
            elif self.current_search["type"] == "posts":
                name_item = QTableWidgetItem(result["post_url"])
            elif self.current_search["type"] == "comments":
                name_item = QTableWidgetItem(result["content"])
            elif self.current_search["type"] == "accounts":
                name_item = QTableWidgetItem(result["username"] or result["profile_id"])
            elif self.current_search["type"] == "groups":
                name_item = QTableWidgetItem(result["group_url"])
            elif self.current_search["type"] == "members":
                name_item = QTableWidgetItem(result["member_id"])
            else:
                name_item = QTableWidgetItem("")
            
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.results_table.setItem(row, 1, name_item)
            
            # Status
            status_item = QTableWidgetItem(result.get("status", ""))
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.results_table.setItem(row, 2, status_item)
            
            # Created
            created_item = QTableWidgetItem(result.get("created_at", ""))
            created_item.setFlags(created_item.flags() & ~Qt.ItemIsEditable)
            self.results_table.setItem(row, 3, created_item)
            
            # Updated
            updated_item = QTableWidgetItem(result.get("updated_at", ""))
            updated_item.setFlags(updated_item.flags() & ~Qt.ItemIsEditable)
            self.results_table.setItem(row, 4, updated_item)
    
    def update_pagination(self):
        """Update pagination controls."""
        self.page_info.setText(f"Page {self.current_page} of {self.total_pages}")
        self.btn_prev_page.setEnabled(self.current_page > 1)
        self.btn_next_page.setEnabled(self.current_page < self.total_pages)
    
    def prev_page(self):
        """Go to previous page of results."""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_search_results()
    
    def next_page(self):
        """Go to next page of results."""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_search_results()
    
    def browse_backup_path(self):
        """Browse for backup file path."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Backup As", "", "Database Files (*.db);;All Files (*)"
        )
        if file_path:
            self.backup_path.setText(file_path)
    
    def create_backup(self):
        """Create a database backup."""
        backup_path = self.backup_path.text().strip()
        
        # Create backup
        result = self.db.backup_database(backup_path if backup_path else None)
        
        if result:
            QMessageBox.information(self, "Backup Created", f"Database backup created successfully at:\n{result}")
            # Refresh backups list
            self.load_backups()
        else:
            QMessageBox.critical(self, "Backup Failed", "Failed to create database backup. Check the logs for details.")
    
    def load_backups(self):
        """Load and display available backups."""
        # Get backups list
        backups = self.db.list_backups()
        
        # Clear table
        self.backups_list.setRowCount(0)
        
        # Add backups to table
        for row, backup_path in enumerate(backups):
            # Get backup info
            info = self.db.get_backup_info(backup_path)
            
            self.backups_list.insertRow(row)
            
            # Filename
            filename_item = QTableWidgetItem(info["filename"])
            filename_item.setData(Qt.UserRole, info["path"])
            filename_item.setFlags(filename_item.flags() & ~Qt.ItemIsEditable)
            self.backups_list.setItem(row, 0, filename_item)
            
            # Date
            date_item = QTableWidgetItem(info["modified"])
            date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
            self.backups_list.setItem(row, 1, date_item)
            
            # Size
            size_item = QTableWidgetItem(info["size_formatted"])
            size_item.setFlags(size_item.flags() & ~Qt.ItemIsEditable)
            self.backups_list.setItem(row, 2, size_item)
            
            # Records
            if "counts" in info:
                counts = info["counts"]
                total = sum(counts.values())
                records_item = QTableWidgetItem(str(total))
                records_item.setToolTip(
                    f"Profiles: {counts.get('profiles', 0)}\n"
                    f"Posts: {counts.get('posts', 0)}\n"
                    f"Comments: {counts.get('comments', 0)}\n"
                    f"Accounts: {counts.get('accounts', 0)}\n"
                    f"Groups: {counts.get('groups', 0)}\n"
                    f"Members: {counts.get('members', 0)}"
                )
            else:
                records_item = QTableWidgetItem("Unknown")
            
            records_item.setFlags(records_item.flags() & ~Qt.ItemIsEditable)
            self.backups_list.setItem(row, 3, records_item)
            
            # Restore button
            restore_btn = QPushButton("Restore")
            restore_btn.clicked.connect(lambda checked, path=backup_path: self.restore_backup(path))
            self.backups_list.setCellWidget(row, 4, restore_btn)
    
    def restore_backup(self, backup_path=None):
        """Restore database from a backup."""
        # If no path provided, get from selected row
        if not backup_path:
            selected_rows = self.backups_list.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "No Selection", "Please select a backup to restore.")
                return
            
            # Get path from first selected row
            backup_path = self.backups_list.item(selected_rows[0].row(), 0).data(Qt.UserRole)
        
        # Confirm restore
        reply = QMessageBox.question(
            self,
            "Confirm Restore",
            f"Are you sure you want to restore the database from this backup?\n\n"
            f"This will replace all current data with the data from the backup.\n\n"
            f"A backup of the current database will be created before restoring.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # Restore database
            result = self.db.restore_database(backup_path)
            
            if result:
                QMessageBox.information(self, "Restore Successful", "Database has been restored successfully.")
                # Refresh statistics
                self.load_statistics()
            else:
                QMessageBox.critical(self, "Restore Failed", "Failed to restore database. Check the logs for details.")
