function extractDates(posts) {
    for (let i = 0; i < posts.length; i++) {
        const post = posts[i];
        
        // تحويل نص التاريخ إلى تاريخ قابل للقراءة
        let dateText = post.timestampText || '';
        let date = null;
        
        // استخدام utime إذا كان متاحًا (أكثر دقة)
        if (post.utime) {
            date = new Date(parseInt(post.utime) * 1000);
            post.date = date.toISOString();
            continue;
        }
        
        // محاولة تحليل نص التاريخ
        const now = new Date();
        
        // حالة "منذ X دقيقة/ساعة/يوم/أسبوع/شهر/سنة"
        const timeAgoMatch = dateText.match(/([0-9]+)\s+(min|minute|hour|hr|day|week|month|year)s?\s+ago/i);
        if (timeAgoMatch) {
            const amount = parseInt(timeAgoMatch[1]);
            const unit = timeAgoMatch[2].toLowerCase();
            
            date = new Date(now);
            
            if (unit === 'min' || unit === 'minute') {
                date.setMinutes(date.getMinutes() - amount);
            } else if (unit === 'hour' || unit === 'hr') {
                date.setHours(date.getHours() - amount);
            } else if (unit === 'day') {
                date.setDate(date.getDate() - amount);
            } else if (unit === 'week') {
                date.setDate(date.getDate() - (amount * 7));
            } else if (unit === 'month') {
                date.setMonth(date.getMonth() - amount);
            } else if (unit === 'year') {
                date.setFullYear(date.getFullYear() - amount);
            }
            
            post.date = date.toISOString();
            continue;
        }
        
        // حالة "yesterday at XX:XX"
        if (dateText.toLowerCase().includes('yesterday')) {
            date = new Date(now);
            date.setDate(date.getDate() - 1);
            
            // استخراج الوقت إذا كان متاحًا
            const timeMatch = dateText.match(/([0-9]{1,2}):([0-9]{2})(?:\s*(am|pm))?/i);
            if (timeMatch) {
                let hours = parseInt(timeMatch[1]);
                const minutes = parseInt(timeMatch[2]);
                const ampm = timeMatch[3] ? timeMatch[3].toLowerCase() : null;
                
                if (ampm === 'pm' && hours < 12) {
                    hours += 12;
                } else if (ampm === 'am' && hours === 12) {
                    hours = 0;
                }
                
                date.setHours(hours, minutes, 0, 0);
            } else {
                // إذا لم يكن هناك وقت، استخدم منتصف اليوم
                date.setHours(12, 0, 0, 0);
            }
            
            post.date = date.toISOString();
            continue;
        }
        
        // حالة "today at XX:XX"
        if (dateText.toLowerCase().includes('today')) {
            date = new Date(now);
            
            // استخراج الوقت إذا كان متاحًا
            const timeMatch = dateText.match(/([0-9]{1,2}):([0-9]{2})(?:\s*(am|pm))?/i);
            if (timeMatch) {
                let hours = parseInt(timeMatch[1]);
                const minutes = parseInt(timeMatch[2]);
                const ampm = timeMatch[3] ? timeMatch[3].toLowerCase() : null;
                
                if (ampm === 'pm' && hours < 12) {
                    hours += 12;
                } else if (ampm === 'am' && hours === 12) {
                    hours = 0;
                }
                
                date.setHours(hours, minutes, 0, 0);
            } else {
                // إذا لم يكن هناك وقت، استخدم منتصف اليوم
                date.setHours(12, 0, 0, 0);
            }
            
            post.date = date.toISOString();
            continue;
        }
        
        // حالة "Month Day at XX:XX" (e.g., "January 1 at 12:00 PM")
        const monthDayMatch = dateText.match(/([a-z]+)\s+([0-9]{1,2})(?:\s+at\s+([0-9]{1,2}):([0-9]{2})(?:\s*(am|pm))?)?/i);
        if (monthDayMatch) {
            const monthName = monthDayMatch[1];
            const day = parseInt(monthDayMatch[2]);
            
            // تحويل اسم الشهر إلى رقم
            const months = {
                'january': 0, 'february': 1, 'march': 2, 'april': 3, 'may': 4, 'june': 5,
                'july': 6, 'august': 7, 'september': 8, 'october': 9, 'november': 10, 'december': 11,
                'jan': 0, 'feb': 1, 'mar': 2, 'apr': 3, 'jun': 5, 'jul': 6, 'aug': 7, 'sep': 8, 'oct': 9, 'nov': 10, 'dec': 11
            };
            
            const monthIndex = months[monthName.toLowerCase()];
            if (monthIndex !== undefined) {
                date = new Date(now);
                date.setMonth(monthIndex);
                date.setDate(day);
                
                // إذا كان الشهر في المستقبل، فمن المحتمل أنه من العام الماضي
                if (date > now) {
                    date.setFullYear(date.getFullYear() - 1);
                }
                
                // استخراج الوقت إذا كان متاحًا
                if (monthDayMatch[3] && monthDayMatch[4]) {
                    let hours = parseInt(monthDayMatch[3]);
                    const minutes = parseInt(monthDayMatch[4]);
                    const ampm = monthDayMatch[5] ? monthDayMatch[5].toLowerCase() : null;
                    
                    if (ampm === 'pm' && hours < 12) {
                        hours += 12;
                    } else if (ampm === 'am' && hours === 12) {
                        hours = 0;
                    }
                    
                    date.setHours(hours, minutes, 0, 0);
                } else {
                    // إذا لم يكن هناك وقت، استخدم منتصف اليوم
                    date.setHours(12, 0, 0, 0);
                }
                
                post.date = date.toISOString();
                continue;
            }
        }
        
        // حالة "Month Day, Year at XX:XX" (e.g., "January 1, 2023 at 12:00 PM")
        const fullDateMatch = dateText.match(/([a-z]+)\s+([0-9]{1,2}),\s+([0-9]{4})(?:\s+at\s+([0-9]{1,2}):([0-9]{2})(?:\s*(am|pm))?)?/i);
        if (fullDateMatch) {
            const monthName = fullDateMatch[1];
            const day = parseInt(fullDateMatch[2]);
            const year = parseInt(fullDateMatch[3]);
            
            // تحويل اسم الشهر إلى رقم
            const months = {
                'january': 0, 'february': 1, 'march': 2, 'april': 3, 'may': 4, 'june': 5,
                'july': 6, 'august': 7, 'september': 8, 'october': 9, 'november': 10, 'december': 11,
                'jan': 0, 'feb': 1, 'mar': 2, 'apr': 3, 'jun': 5, 'jul': 6, 'aug': 7, 'sep': 8, 'oct': 9, 'nov': 10, 'dec': 11
            };
            
            const monthIndex = months[monthName.toLowerCase()];
            if (monthIndex !== undefined) {
                date = new Date(year, monthIndex, day);
                
                // استخراج الوقت إذا كان متاحًا
                if (fullDateMatch[4] && fullDateMatch[5]) {
                    let hours = parseInt(fullDateMatch[4]);
                    const minutes = parseInt(fullDateMatch[5]);
                    const ampm = fullDateMatch[6] ? fullDateMatch[6].toLowerCase() : null;
                    
                    if (ampm === 'pm' && hours < 12) {
                        hours += 12;
                    } else if (ampm === 'am' && hours === 12) {
                        hours = 0;
                    }
                    
                    date.setHours(hours, minutes, 0, 0);
                } else {
                    // إذا لم يكن هناك وقت، استخدم منتصف اليوم
                    date.setHours(12, 0, 0, 0);
                }
                
                post.date = date.toISOString();
                continue;
            }
        }
        
        // إذا لم نتمكن من تحليل التاريخ، استخدم الوقت الحالي
        post.date = now.toISOString();
    }
    
    return posts;
}

return extractDates(arguments[0]);
