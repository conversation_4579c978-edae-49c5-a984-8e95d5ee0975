import os
import csv
import logging

# Setup logger
logger = logging.getLogger('zpammer.csv_reader')

class CSVReader:
    """
    Utility class for reading and writing CSV files.
    
    This class handles:
    - Reading CSV files
    - Writing CSV files
    - Validating CSV data
    """
    
    @staticmethod
    def read_csv(file_path, expected_headers=None, encoding='utf-8'):
        """
        Read a CSV file and return its contents.
        
        Args:
            file_path (str): Path to the CSV file.
            expected_headers (list, optional): List of expected headers.
            encoding (str, optional): File encoding.
            
        Returns:
            tuple: (headers, data) where headers is a list of column names and data is a list of rows.
        """
        headers = []
        data = []
        
        try:
            with open(file_path, 'r', newline='', encoding=encoding) as csvfile:
                reader = csv.reader(csvfile)
                headers = next(reader, [])
                
                # Validate headers if expected_headers is provided
                if expected_headers and headers[:len(expected_headers)] != expected_headers:
                    logger.warning(f"CSV header mismatch. Expected {expected_headers} but got {headers[:len(expected_headers)]}")
                
                # Read data rows
                for row in reader:
                    if row:  # Skip empty rows
                        data.append(row)
                
                logger.info(f"Read {len(data)} rows from {file_path}")
                return headers, data
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return headers, data
    
    @staticmethod
    def write_csv(file_path, headers, data, encoding='utf-8'):
        """
        Write data to a CSV file.
        
        Args:
            file_path (str): Path to the CSV file.
            headers (list): List of column names.
            data (list): List of rows.
            encoding (str, optional): File encoding.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            with open(file_path, 'w', newline='', encoding=encoding) as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)
                
            logger.info(f"Wrote {len(data)} rows to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Error writing CSV file {file_path}: {e}")
            return False
    
    @staticmethod
    def read_accounts_csv(file_path, encoding='utf-8'):
        """
        Read accounts from a CSV file.
        
        Args:
            file_path (str): Path to the CSV file.
            encoding (str, optional): File encoding.
            
        Returns:
            list: List of account dictionaries.
        """
        accounts = []
        expected_headers = ['FB', 'Profile ID', 'Browser ID', 'User', 'Pass', 'Statue']
        
        try:
            with open(file_path, 'r', newline='', encoding=encoding) as csvfile:
                reader = csv.DictReader(csvfile)
                field_names = reader.fieldnames
                
                # Validate headers
                if not all(header in field_names for header in expected_headers[:3]):
                    logger.warning(f"CSV header mismatch. Expected at least {expected_headers[:3]} but got {field_names}")
                
                # Read accounts
                for row in reader:
                    if 'Browser ID' in row and row['Browser ID'].strip():
                        account = {
                            'fb': row.get('FB', '').strip(),
                            'profile_id': row.get('Profile ID', '').strip(),
                            'browser_id': row.get('Browser ID', '').strip(),
                            'user': row.get('User', '').strip(),
                            'pass': row.get('Pass', '').strip(),
                            'status': row.get('Statue', '').strip()
                        }
                        accounts.append(account)
                
                logger.info(f"Read {len(accounts)} accounts from {file_path}")
                return accounts
        except Exception as e:
            logger.error(f"Error reading accounts CSV file {file_path}: {e}")
            return accounts
