#!/usr/bin/env python3
import os
import sys
import traceback
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('debug_zpammer')

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "src"))

try:
    logger.debug("Importing MainWindow from gui.main_window")
    from gui.main_window import MainWindow
    logger.debug("Successfully imported MainWindow")

    logger.debug("Creating QApplication")
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    logger.debug("Successfully created QApplication")

    logger.debug("Creating MainWindow instance")
    try:
        window = MainWindow()
        logger.debug("Successfully created MainWindow instance")
    except Exception as e:
        logger.error(f"Error creating MainWindow instance: {e}")
        traceback.print_exc()
        raise

    logger.debug("Showing MainWindow")
    window.show()
    logger.debug("Successfully showed MainWindow")

    logger.debug("Running application")
    exit_code = app.exec_()
    logger.debug(f"Application exited with code {exit_code}")
    sys.exit(exit_code)
except Exception as e:
    logger.error(f"Error: {e}")
    traceback.print_exc()
    sys.exit(1)
