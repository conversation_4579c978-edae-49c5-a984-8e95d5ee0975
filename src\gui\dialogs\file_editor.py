from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit,
                            QPushButton, QLabel, QFileDialog, QMessageBox)
from PyQt5.QtCore import Qt
import os

class FileEditorDialog(QDialog):
    """Dialog for editing text files."""
    
    def __init__(self, file_path=None, title="File Editor", parent=None):
        """
        Initialize the file editor dialog.
        
        Args:
            file_path (str, optional): Path to the file to edit.
            title (str, optional): Dialog title.
            parent (QWidget, optional): Parent widget.
        """
        super().__init__(parent)
        
        self.file_path = file_path
        self.setWindowTitle(title)
        self.resize(800, 600)
        
        self.setup_ui()
        
        if file_path:
            self.load_file(file_path)
    
    def setup_ui(self):
        """Create and configure the UI components."""
        layout = QVBoxLayout(self)
        
        # File path display
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("File:"))
        self.path_label = QLabel(self.file_path or "New File")
        path_layout.addWidget(self.path_label, 1)
        self.btn_browse = QPushButton("Browse")
        path_layout.addWidget(self.btn_browse)
        layout.addLayout(path_layout)
        
        # Text editor
        self.text_edit = QTextEdit()
        layout.addWidget(self.text_edit)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        self.btn_save = QPushButton("Save")
        self.btn_save.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        self.btn_cancel = QPushButton("Cancel")
        buttons_layout.addWidget(self.btn_save)
        buttons_layout.addWidget(self.btn_cancel)
        layout.addLayout(buttons_layout)
        
        # Connect signals
        self.btn_browse.clicked.connect(self.browse_file)
        self.btn_save.clicked.connect(self.save_file)
        self.btn_cancel.clicked.connect(self.reject)
    
    def load_file(self, file_path):
        """
        Load a file into the editor.
        
        Args:
            file_path (str): Path to the file to load.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.text_edit.setText(content)
            self.file_path = file_path
            self.path_label.setText(file_path)
            return True
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load file: {e}")
            return False
    
    def save_file(self):
        """
        Save the current content to the file.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        if not self.file_path:
            return self.save_file_as()
        
        try:
            content = self.text_edit.toPlainText()
            
            with open(self.file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            QMessageBox.information(self, "Success", "File saved successfully.")
            self.accept()
            return True
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save file: {e}")
            return False
    
    def save_file_as(self):
        """
        Save the current content to a new file.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save File As", "", "Text Files (*.txt);;All Files (*)"
        )
        
        if not file_path:
            return False
        
        self.file_path = file_path
        self.path_label.setText(file_path)
        return self.save_file()
    
    def browse_file(self):
        """
        Browse for a file to open.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open File", "", "Text Files (*.txt);;All Files (*)"
        )
        
        if not file_path:
            return False
        
        return self.load_file(file_path)
