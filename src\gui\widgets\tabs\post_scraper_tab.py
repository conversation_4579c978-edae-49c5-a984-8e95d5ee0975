"""
Post Scraper Tab - UI for extracting posts from Facebook groups.
"""

import os
import json
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit,
    QListWidget, QProgressBar, QTextEdit, QComboBox, QCheckBox, QSpinBox,
    QGroupBox, QSplitter, QMessageBox, QFileDialog, QDialog,
    QDialogButtonBox, QApplication
)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QIcon, QCursor, QClipboard

from .post_scraper_controller import PostScraperController


class PostScraperTab(QWidget):
    """Tab for extracting posts from Facebook groups."""

    def __init__(self, parent=None):
        """Initialize the Post Scraper tab."""
        super().__init__(parent)

        # Initialize controller
        self.controller = PostScraperController()

        # Connect controller signals
        self.controller.log_message.connect(self.on_log_message)
        self.controller.progress_updated.connect(self.on_progress_updated)
        self.controller.post_links_updated.connect(self.on_post_links_updated)
        self.controller.scraping_completed.connect(self.on_scraping_completed)

        # Set up UI
        self.setup_ui()

        # Load saved settings
        self.load_settings()

        # Load saved group links
        self.load_saved_group_links()

    def setup_ui(self):
        """Set up the user interface."""
        # Create main layout with horizontal splitter (like Member Scraper)
        main_layout = QHBoxLayout(self)

        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - Group links and extracted posts
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Group links section
        self.group_links_group = QGroupBox("Facebook Group Links (0)")
        group_links_layout = QVBoxLayout(self.group_links_group)

        # Group link input
        group_link_input_layout = QHBoxLayout()
        self.add_group_link_input = QLineEdit()
        self.add_group_link_input.setPlaceholderText("Enter Facebook group link...")
        self.add_group_link_button = QPushButton("Add")
        self.add_group_link_button.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_group_link_button.setCursor(QCursor(Qt.PointingHandCursor))

        group_link_input_layout.addWidget(self.add_group_link_input)
        group_link_input_layout.addWidget(self.add_group_link_button)
        group_links_layout.addLayout(group_link_input_layout)

        # Group links list
        self.group_links_list = QListWidget()
        self.group_links_list.setAlternatingRowColors(True)
        self.group_links_list.setSelectionMode(QListWidget.ExtendedSelection)
        group_links_layout.addWidget(self.group_links_list)

        # Group links buttons
        group_links_buttons = QHBoxLayout()
        self.import_group_links_button = QPushButton("Import")
        self.import_group_links_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.edit_group_links_button = QPushButton("Edit")
        self.edit_group_links_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.remove_group_link_button = QPushButton("Remove")
        self.remove_group_link_button.setStyleSheet("background-color: #FF9800; color: white;")
        self.remove_group_link_button.setCursor(QCursor(Qt.PointingHandCursor))

        group_links_buttons.addWidget(self.import_group_links_button)
        group_links_buttons.addWidget(self.edit_group_links_button)
        group_links_buttons.addWidget(self.remove_group_link_button)
        group_links_layout.addLayout(group_links_buttons)

        # Extracted posts section
        self.posts_group = QGroupBox("Extracted Posts (0)")
        posts_layout = QVBoxLayout(self.posts_group)

        self.new_post_links_list = QListWidget()
        self.new_post_links_list.setAlternatingRowColors(True)
        self.new_post_links_list.setSelectionMode(QListWidget.ExtendedSelection)
        posts_layout.addWidget(self.new_post_links_list)

        # Posts buttons
        posts_buttons = QHBoxLayout()
        self.export_json_button = QPushButton("Export JSON")
        self.export_json_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.export_csv_button = QPushButton("Export CSV")
        self.export_csv_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.export_clipboard_button = QPushButton("Copy All")
        self.export_clipboard_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_new_post_links_button = QPushButton("Clear")
        self.clear_new_post_links_button.setStyleSheet("background-color: #f44336; color: white;")
        self.clear_new_post_links_button.setCursor(QCursor(Qt.PointingHandCursor))

        posts_buttons.addWidget(self.export_json_button)
        posts_buttons.addWidget(self.export_csv_button)
        posts_buttons.addWidget(self.export_clipboard_button)
        posts_buttons.addWidget(self.clear_new_post_links_button)
        posts_layout.addLayout(posts_buttons)

        # Add groups to left layout
        left_layout.addWidget(self.group_links_group, 1)
        left_layout.addWidget(self.posts_group, 1)

        # Right panel - controls and output
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Settings section
        self.settings_group = QGroupBox("Post Scraper Settings")
        settings_layout = QVBoxLayout(self.settings_group)

        # Profile ID
        profile_layout = QHBoxLayout()
        profile_layout.addWidget(QLabel("Profile ID:"))
        self.profile_id_input = QComboBox()
        self.profile_id_input.setEditable(True)
        profile_layout.addWidget(self.profile_id_input)
        settings_layout.addLayout(profile_layout)

        # Post fetch quantity
        fetch_layout = QHBoxLayout()
        fetch_layout.addWidget(QLabel("Fetch method:"))
        self.fetch_method = QComboBox()
        self.fetch_method.addItems(["All Posts", "By Post Count", "By Days"])
        fetch_layout.addWidget(self.fetch_method)
        settings_layout.addLayout(fetch_layout)

        # Posts count
        posts_count_layout = QHBoxLayout()
        posts_count_layout.addWidget(QLabel("Posts count:"))
        self.posts_count_spinbox = QSpinBox()
        self.posts_count_spinbox.setRange(10, 1000)
        self.posts_count_spinbox.setValue(100)
        posts_count_layout.addWidget(self.posts_count_spinbox)
        settings_layout.addLayout(posts_count_layout)

        # Days count
        days_count_layout = QHBoxLayout()
        days_count_layout.addWidget(QLabel("Days count:"))
        self.days_count_spinbox = QSpinBox()
        self.days_count_spinbox.setRange(1, 365)
        self.days_count_spinbox.setValue(7)
        days_count_layout.addWidget(self.days_count_spinbox)
        settings_layout.addLayout(days_count_layout)

        # Deep scroll settings
        deep_scroll_layout = QHBoxLayout()
        deep_scroll_layout.addWidget(QLabel("Deep scroll:"))
        self.deep_scroll_spinbox = QSpinBox()
        self.deep_scroll_spinbox.setRange(5, 100)  # Increased maximum to 100 scrolls
        self.deep_scroll_spinbox.setValue(50)      # Increased default to 50 scrolls
        self.deep_scroll_spinbox.setSuffix(" scrolls")
        self.deep_scroll_spinbox.setToolTip("Perform a deep scroll every N scrolls to load more content")
        deep_scroll_layout.addWidget(self.deep_scroll_spinbox)

        settings_layout.addLayout(deep_scroll_layout)

        # Extraction method settings
        extraction_method_layout = QHBoxLayout()
        extraction_method_layout.addWidget(QLabel("Extraction method:"))
        self.extraction_method_combo = QComboBox()
        self.extraction_method_combo.addItems(["XHR Method", "Date Click Method", "Direct Method"])
        self.extraction_method_combo.setCurrentIndex(2)  # Set Direct Method as default
        self.extraction_method_combo.setToolTip("XHR Method: Standard extraction method\nDate Click Method: Extracts posts by clicking on date elements\nDirect Method: Extracts posts directly from the page without clicking (recommended)")
        extraction_method_layout.addWidget(self.extraction_method_combo)

        settings_layout.addLayout(extraction_method_layout)

        # Checkboxes for options
        self.auto_add_checkbox = QCheckBox("Auto-add to extracted posts")
        self.detect_selectors_checkbox = QCheckBox("Auto-detect post selectors")
        self.headless_mode = QCheckBox("Headless mode")

        self.auto_add_checkbox.setChecked(True)
        self.detect_selectors_checkbox.setChecked(True)

        settings_layout.addWidget(self.auto_add_checkbox)
        settings_layout.addWidget(self.detect_selectors_checkbox)
        settings_layout.addWidget(self.headless_mode)

        # Control section
        self.control_group = QGroupBox("Control")
        control_layout = QVBoxLayout(self.control_group)

        # Control buttons
        control_buttons = QHBoxLayout()
        self.start_button = QPushButton("Start Post Scraper")
        self.start_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.start_button.setCursor(QCursor(Qt.PointingHandCursor))

        self.stop_button = QPushButton("Stop Post Scraper")
        self.stop_button.setStyleSheet("background-color: #f44336; color: white; font-weight: bold;")
        self.stop_button.setEnabled(False)
        self.stop_button.setCursor(QCursor(Qt.PointingHandCursor))

        self.save_settings_button = QPushButton("Save Settings")
        self.save_settings_button.setStyleSheet("background-color: #FF9800; color: white;")
        self.save_settings_button.setCursor(QCursor(Qt.PointingHandCursor))

        control_buttons.addWidget(self.start_button)
        control_buttons.addWidget(self.stop_button)
        control_buttons.addWidget(self.save_settings_button)
        control_layout.addLayout(control_buttons)

        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        control_layout.addLayout(progress_layout)

        # Status label
        self.status_label = QLabel("Status: Idle")
        control_layout.addWidget(self.status_label)

        # Actions section
        self.actions_group = QGroupBox("Actions")
        actions_layout = QVBoxLayout(self.actions_group)

        # Send to Spammer buttons
        spammer_buttons = QHBoxLayout()
        self.send_to_spammer_button = QPushButton("Send All to Spammer")
        self.send_to_spammer_button.setStyleSheet("background-color: #9C27B0; color: white;")
        self.send_to_spammer_button.setCursor(QCursor(Qt.PointingHandCursor))

        self.send_selected_to_spammer_button = QPushButton("Send Selected")
        self.send_selected_to_spammer_button.setStyleSheet("background-color: #673AB7; color: white;")
        self.send_selected_to_spammer_button.setCursor(QCursor(Qt.PointingHandCursor))

        self.detect_selectors_button = QPushButton("Detect Selectors")
        self.detect_selectors_button.setStyleSheet("background-color: #2196F3; color: white;")
        self.detect_selectors_button.setCursor(QCursor(Qt.PointingHandCursor))

        spammer_buttons.addWidget(self.send_to_spammer_button)
        spammer_buttons.addWidget(self.send_selected_to_spammer_button)
        spammer_buttons.addWidget(self.detect_selectors_button)

        actions_layout.addLayout(spammer_buttons)

        # Log section
        self.log_group = QGroupBox("Operation Log")
        log_layout = QVBoxLayout(self.log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)

        log_buttons = QHBoxLayout()
        self.clear_log_button = QPushButton("Clear Log")
        self.clear_log_button.setCursor(QCursor(Qt.PointingHandCursor))
        log_buttons.addWidget(self.clear_log_button)
        log_buttons.addStretch()

        log_layout.addWidget(self.log_text)
        log_layout.addLayout(log_buttons)

        # Add groups to right layout
        right_layout.addWidget(self.settings_group)
        right_layout.addWidget(self.control_group)
        right_layout.addWidget(self.actions_group)
        right_layout.addWidget(self.log_group)

        # Add panels to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # Set initial splitter sizes (40% left, 60% right)
        splitter.setSizes([400, 600])

        # Connect signals
        self.connect_signals()

    def connect_signals(self):
        """Connect signals to slots."""
        # Button signals for group links
        self.add_group_link_button.clicked.connect(self.on_add_group_link)
        self.import_group_links_button.clicked.connect(self.on_import_group_links)
        self.edit_group_links_button.clicked.connect(self.on_edit_group_links)
        self.remove_group_link_button.clicked.connect(self.on_remove_group_link)

        # Export buttons
        self.export_json_button.clicked.connect(self.on_export_json)
        self.export_csv_button.clicked.connect(self.on_export_csv)
        self.export_clipboard_button.clicked.connect(self.on_copy_new_post_links)
        self.clear_new_post_links_button.clicked.connect(self.on_clear_new_post_links)

        # Control buttons
        self.start_button.clicked.connect(self.on_start_scraping)
        self.stop_button.clicked.connect(self.on_stop_scraping)
        self.save_settings_button.clicked.connect(self.on_save_settings)

        # Action buttons
        self.send_to_spammer_button.clicked.connect(self.on_send_to_post_spammer)
        self.send_selected_to_spammer_button.clicked.connect(self.on_send_selected_to_post_spammer)
        self.detect_selectors_button.clicked.connect(self.on_detect_selectors)

        # Log buttons
        self.clear_log_button.clicked.connect(self.on_clear_log)

        # Fetch method change
        self.fetch_method.currentIndexChanged.connect(self.on_fetch_method_changed)

    def on_fetch_method_changed(self, index):
        """Handle fetch method change."""
        # Enable/disable spinboxes based on selection
        self.posts_count_spinbox.setEnabled(index == 1)  # By Post Count
        self.days_count_spinbox.setEnabled(index == 2)   # By Days

    def on_add_group_link(self):
        """Handle add group link button click."""
        # Get group link from input field
        group_link = self.add_group_link_input.text().strip()

        if not group_link:
            return

        # Add chronological sorting parameter if not already present
        if "/?sorting_setting=CHRONOLOGICAL" not in group_link and "?sorting_setting=CHRONOLOGICAL" not in group_link:
            # Check if the URL already has parameters
            if "?" in group_link:
                group_link += "&sorting_setting=CHRONOLOGICAL"
            else:
                group_link += "/?sorting_setting=CHRONOLOGICAL"

        # Add to list if not already present
        for i in range(self.group_links_list.count()):
            if self.group_links_list.item(i).text() == group_link:
                return

        # Add to list
        self.group_links_list.addItem(group_link)
        self.add_group_link_input.clear()

        # Update group count in title
        self.update_group_count()

    def on_import_group_links(self):
        """Handle import group links button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Group Links", "", "Text Files (*.txt);;All Files (*)"
        )

        if not file_path:
            return

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                links = [line.strip() for line in f.readlines() if line.strip()]

            # Add links to list
            for link in links:
                # Add chronological sorting parameter if not already present
                if "/?sorting_setting=CHRONOLOGICAL" not in link and "?sorting_setting=CHRONOLOGICAL" not in link:
                    # Check if the URL already has parameters
                    if "?" in link:
                        link += "&sorting_setting=CHRONOLOGICAL"
                    else:
                        link += "/?sorting_setting=CHRONOLOGICAL"

                # Check if already in list
                exists = False
                for i in range(self.group_links_list.count()):
                    if self.group_links_list.item(i).text() == link:
                        exists = True
                        break

                if not exists:
                    self.group_links_list.addItem(link)

            # Update group count in title
            self.update_group_count()

            self.log_message(f"[INFO] Imported {len(links)} group links from {file_path}")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to import group links: {str(e)}")

    def on_edit_group_links(self):
        """Handle edit group links button click."""
        # Get current links
        links = [self.group_links_list.item(i).text() for i in range(self.group_links_list.count())]

        # Show dialog to edit links
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QDialogButtonBox

        dialog = QDialog(self)
        dialog.setWindowTitle("Edit Group Links")
        dialog.setMinimumSize(500, 400)

        layout = QVBoxLayout(dialog)

        text_edit = QTextEdit()
        text_edit.setPlainText("\n".join(links))
        layout.addWidget(text_edit)

        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            # Get edited links
            edited_links = [line.strip() for line in text_edit.toPlainText().split("\n") if line.strip()]

            # Clear list and add edited links
            self.group_links_list.clear()
            for link in edited_links:
                # Add chronological sorting parameter if not already present
                if "/?sorting_setting=CHRONOLOGICAL" not in link and "?sorting_setting=CHRONOLOGICAL" not in link:
                    # Check if the URL already has parameters
                    if "?" in link:
                        link += "&sorting_setting=CHRONOLOGICAL"
                    else:
                        link += "/?sorting_setting=CHRONOLOGICAL"

                self.group_links_list.addItem(link)

            # Update group count in title
            self.update_group_count()

            self.log_message(f"[INFO] Updated group links. Now have {len(edited_links)} links.")

    def on_clear_new_post_links(self):
        """Clear all post links."""
        self.new_post_links_list.clear()
        self.update_posts_count()
        self.log_message("[INFO] Cleared all post links")

    def on_export_json(self):
        """Export posts to JSON file."""
        self.controller.export_to_json()

    def on_export_csv(self):
        """Export posts to CSV file."""
        self.controller.export_to_csv()

    def on_start_scraping(self):
        """Start the post scraping process."""
        # Get group links
        group_links = [self.group_links_list.item(i).text() for i in range(self.group_links_list.count())]

        if not group_links:
            QMessageBox.warning(self, "Warning", "No group links loaded.")
            return

        # Get profile ID
        profile_id = self.profile_id_input.currentText().strip()
        if not profile_id:
            QMessageBox.warning(self, "Warning", "Please enter a profile ID.")
            return

        # Get settings
        settings = {
            "profile_id": profile_id,
            "fetch_method": self.fetch_method.currentIndex(),
            "posts_count": self.posts_count_spinbox.value(),
            "days_count": self.days_count_spinbox.value(),
            "scroll_delay": 2,  # Default value, will be randomized in controller
            "deep_scroll_frequency": self.deep_scroll_spinbox.value(),
            "auto_add": self.auto_add_checkbox.isChecked(),
            "detect_selectors": self.detect_selectors_checkbox.isChecked(),
            "headless_mode": self.headless_mode.isChecked(),
            "random_delay": True,  # Flag to use random delay between 1-3 seconds
            "extraction_method": "date_click" if self.extraction_method_combo.currentIndex() == 1 else
                              "direct" if self.extraction_method_combo.currentIndex() == 2 else "xhr"
        }

        # Update UI
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("Status: Running")
        self.progress_bar.setValue(0)

        # Start scraping
        self.controller.start_scraping(group_links, settings)

    def on_stop_scraping(self):
        """Stop the post scraping process."""
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Are you sure you want to stop the post scraper?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop scraping
            self.controller.stop_scraping()

            # Update UI
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("Status: Stopped")

            # Log stop
            self.log_message("[WARNING] Post Scraper stopped by user")

    def on_save_settings(self):
        """Save current settings."""
        settings = {
            "profile_id": self.profile_id_input.currentText().strip(),
            "fetch_method": self.fetch_method.currentIndex(),
            "posts_count": self.posts_count_spinbox.value(),
            "days_count": self.days_count_spinbox.value(),
            "scroll_delay": 2,  # Default value, will be randomized in controller
            "deep_scroll_frequency": self.deep_scroll_spinbox.value(),
            "auto_add": self.auto_add_checkbox.isChecked(),
            "detect_selectors": self.detect_selectors_checkbox.isChecked(),
            "headless_mode": self.headless_mode.isChecked(),
            "random_delay": True,  # Flag to use random delay between 1-3 seconds
            "extraction_method": "date_click" if self.extraction_method_combo.currentIndex() == 1 else
                              "direct" if self.extraction_method_combo.currentIndex() == 2 else "xhr"
        }

        # Save settings using controller
        self.controller.save_settings(settings)
        self.log_message("[INFO] Settings saved successfully")

    def on_send_to_post_spammer(self):
        """Send all post links to Post Spammer."""
        # Get all post links
        post_links = [self.new_post_links_list.item(i).text() for i in range(self.new_post_links_list.count())]

        if not post_links:
            QMessageBox.warning(self, "Warning", "No post links to send.")
            return

        # Send to Post Spammer
        try:
            self.controller.send_to_spammer(post_links)
            self.log_message(f"[INFO] Sent {len(post_links)} post links to Post Spammer")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to send post links to Post Spammer: {str(e)}")

    def on_send_selected_to_post_spammer(self):
        """Send selected post links to Post Spammer."""
        # Get selected post links
        selected_items = self.new_post_links_list.selectedItems()
        post_links = [item.text() for item in selected_items]

        if not post_links:
            QMessageBox.warning(self, "Warning", "No post links selected.")
            return

        # Send to Post Spammer
        try:
            self.controller.send_to_spammer(post_links)
            self.log_message(f"[INFO] Sent {len(post_links)} selected post links to Post Spammer")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to send post links to Post Spammer: {str(e)}")

    def on_copy_new_post_links(self):
        """Copy all post links to clipboard."""
        # Get all post links
        post_links = [self.new_post_links_list.item(i).text() for i in range(self.new_post_links_list.count())]

        if not post_links:
            QMessageBox.warning(self, "Warning", "No post links to copy.")
            return

        # Copy to clipboard
        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(post_links))

        self.log_message(f"[INFO] Copied {len(post_links)} post links to clipboard")

    def on_detect_selectors(self):
        """Detect post selectors."""
        # Get profile ID
        profile_id = self.profile_id_input.currentText().strip()
        if not profile_id:
            QMessageBox.warning(self, "Warning", "Please enter a profile ID.")
            return

        # Get first group link
        if self.group_links_list.count() > 0:
            group_url = self.group_links_list.item(0).text()
        else:
            QMessageBox.warning(self, "Warning", "Please add at least one group link.")
            return

        # Detect selectors
        self.log_message("[INFO] Starting selector detection...")
        self.controller.detect_post_selectors(profile_id, group_url)

    def on_clear_log(self):
        """Clear the log."""
        self.log_text.clear()

    def update_group_count(self):
        """Update the group count in the group box title."""
        count = self.group_links_list.count()
        self.group_links_group.setTitle(f"Facebook Group Links ({count})")

    def update_posts_count(self):
        """Update the posts count in the group box title."""
        count = self.new_post_links_list.count()
        self.posts_group.setTitle(f"Extracted Posts ({count})")

    def log_message(self, message):
        """Add a message to the log."""
        self.log_text.append(message)

    def on_log_message(self, message):
        """Handle log message from controller."""
        self.log_message(message)

    def on_progress_updated(self, current, total, message):
        """Handle progress update from controller."""
        progress = int(current / total * 100) if total > 0 else 0
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"Status: {message}")

    def on_post_links_updated(self, post_links):
        """Handle post links update from controller."""
        # Add new post links to list
        for link in post_links:
            # Check if already in list
            exists = False
            for i in range(self.new_post_links_list.count()):
                if self.new_post_links_list.item(i).text() == link:
                    exists = True
                    break

            if not exists:
                self.new_post_links_list.addItem(link)

        # Update posts count in title
        self.update_posts_count()

    def on_scraping_completed(self):
        """Handle scraping completed signal from controller."""
        # Update UI
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Status: Completed")
        self.progress_bar.setValue(100)

        # Log completion
        self.log_message("[INFO] Post scraping completed")

    def load_saved_group_links(self):
        """Load saved group links."""
        try:
            # Get group links from controller
            group_links = self.controller.load_group_links()

            # Add to list
            for link in group_links:
                # Add chronological sorting parameter if not already present
                if "/?sorting_setting=CHRONOLOGICAL" not in link and "?sorting_setting=CHRONOLOGICAL" not in link:
                    # Check if the URL already has parameters
                    if "?" in link:
                        link += "&sorting_setting=CHRONOLOGICAL"
                    else:
                        link += "/?sorting_setting=CHRONOLOGICAL"

                self.group_links_list.addItem(link)

            # Update group count in title
            self.update_group_count()

            self.log_message(f"[INFO] Loaded {len(group_links)} saved group links")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to load saved group links: {str(e)}")

    def on_fetch_method_changed(self, index):
        """Handle fetch method change."""
        # Enable/disable spinboxes based on selection
        self.posts_count_spinbox.setEnabled(index == 1)  # By Post Count
        self.days_count_spinbox.setEnabled(index == 2)   # By Days

    def on_remove_group_link(self):
        """Remove selected group links."""
        # Get selected items
        selected_items = self.group_links_list.selectedItems()

        if not selected_items:
            return

        # Remove selected items
        for item in selected_items:
            row = self.group_links_list.row(item)
            self.group_links_list.takeItem(row)

        # Update group count in title
        self.update_group_count()

        # Group links input
        group_links_header = QHBoxLayout()
        group_links_header.setSpacing(10)

        self.add_group_link_input = QLineEdit()
        self.add_group_link_input.setPlaceholderText("Enter Facebook group link...")
        self.add_group_link_input.setToolTip("Enter a Facebook group URL to scrape posts from")

        self.add_group_link_button = QPushButton("Add New")
        self.add_group_link_button.setIcon(QIcon.fromTheme("list-add", QIcon()))
        self.add_group_link_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.add_group_link_button.setToolTip("Add this group link to the list")
        self.add_group_link_button.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_group_link_button.clicked.connect(self.on_add_group_link)

        group_links_header.addWidget(self.add_group_link_input)
        group_links_header.addWidget(self.add_group_link_button)
        group_links_layout.addLayout(group_links_header)

        # Group links list
        self.group_links_list = QListWidget()
        self.group_links_list.setMinimumHeight(120)
        self.group_links_list.setAlternatingRowColors(True)
        self.group_links_list.setToolTip("List of Facebook group links to scrape")
        group_links_layout.addWidget(self.group_links_list)

        # Group links buttons
        group_links_buttons = QHBoxLayout()
        group_links_buttons.setSpacing(10)

        self.remove_group_link_button = QPushButton("Delete Selected")
        self.remove_group_link_button.setIcon(QIcon.fromTheme("list-remove", QIcon()))
        self.remove_group_link_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.remove_group_link_button.setToolTip("Remove selected group links from the list")
        self.remove_group_link_button.setStyleSheet("background-color: #FF9800; color: white;")
        self.remove_group_link_button.clicked.connect(self.on_remove_group_link)

        # Import file button (similar to Spammer)
        self.import_group_links_button = QPushButton("Import File")
        self.import_group_links_button.setIcon(QIcon.fromTheme("document-open", QIcon()))
        self.import_group_links_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.import_group_links_button.setToolTip("Import group links from a file")
        self.import_group_links_button.setStyleSheet("background-color: #0078d7; color: white;")
        self.import_group_links_button.clicked.connect(self.on_import_group_links)

        # Edit button (similar to Spammer)
        self.edit_group_links_button = QPushButton("Edit Groups")
        self.edit_group_links_button.setIcon(QIcon.fromTheme("document-edit", QIcon()))
        self.edit_group_links_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.edit_group_links_button.setToolTip("Edit group links in bulk")
        self.edit_group_links_button.setStyleSheet("background-color: #0078d7; color: white;")
        self.edit_group_links_button.clicked.connect(self.on_edit_group_links)
        # Clear All button
        self.clear_group_links_button = QPushButton("Clear All")
        self.clear_group_links_button.setIcon(QIcon.fromTheme("edit-clear", QIcon()))
        self.clear_group_links_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_group_links_button.setToolTip("Clear all group links from the list")
        self.clear_group_links_button.setStyleSheet("background-color: #f44336; color: white;")
        self.clear_group_links_button.clicked.connect(self.on_clear_group_links)

        group_links_buttons.addWidget(self.import_group_links_button)
        group_links_buttons.addWidget(self.add_group_link_button)
        group_links_buttons.addWidget(self.edit_group_links_button)
        group_links_buttons.addWidget(self.remove_group_link_button)
        group_links_buttons.addStretch()
        group_links_layout.addLayout(group_links_buttons)

        # Add the group links group to the layout
        top_layout.addWidget(group_links_group)

        # Third row - Action buttons and progress bar
        third_row_layout = QHBoxLayout()
        third_row_layout.setSpacing(10)

        # Action buttons in a group box
        action_group = QGroupBox("Actions")
        action_layout = QHBoxLayout(action_group)
        action_layout.setContentsMargins(10, 15, 10, 10)
        action_layout.setSpacing(10)

        # Start button with icon
        self.start_button = QPushButton("Start Post Scraper")
        self.start_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.start_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.start_button.setFixedHeight(30)
        self.start_button.setToolTip("Start extracting posts from the selected groups")
        self.start_button.setStyleSheet("background-color: #4CAF50; color: white;")
        self.start_button.clicked.connect(self.on_start_scraping)

        # Stop button with icon
        self.stop_button = QPushButton("Stop Post Scraper")
        self.stop_button.setIcon(self.style().standardIcon(QStyle.SP_MediaStop))
        self.stop_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.stop_button.setFixedHeight(30)
        self.stop_button.setEnabled(False)
        self.stop_button.setToolTip("Stop the current extraction process")
        self.stop_button.setStyleSheet("background-color: #f44336; color: white;")
        self.stop_button.clicked.connect(self.on_stop_scraping)

        # Save settings button with icon
        self.save_settings_button = QPushButton("Save Settings")
        self.save_settings_button.setIcon(self.style().standardIcon(QStyle.SP_DialogSaveButton))
        self.save_settings_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.save_settings_button.setFixedHeight(30)
        self.save_settings_button.setToolTip("Save current settings for future use")
        self.save_settings_button.setStyleSheet("background-color: #FF9800; color: white;")
        self.save_settings_button.clicked.connect(self.on_save_settings)

        # Add buttons to layout
        action_layout.addWidget(self.start_button, 1)
        action_layout.addWidget(self.stop_button, 1)
        action_layout.addWidget(self.save_settings_button, 1)

        # Progress bar in a group box
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setContentsMargins(10, 15, 10, 10)
        progress_layout.setSpacing(5)

        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFormat("%p% (%v/%m)")
        self.progress_bar.setToolTip("Current progress of the extraction process")
        self.progress_bar.setMinimumHeight(25)
        progress_layout.addWidget(self.progress_bar)

        # Add the third row groups to the layout
        third_row_layout.addWidget(action_group, 1)
        third_row_layout.addWidget(progress_group, 2)

        top_layout.addLayout(third_row_layout)

        # Add top widget to splitter
        splitter.addWidget(top_widget)

        # Bottom section - Results
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        bottom_layout.setContentsMargins(5, 5, 5, 5)
        bottom_layout.setSpacing(10)

        # Results section in a horizontal layout
        results_layout = QHBoxLayout()
        results_layout.setSpacing(10)

        # New Post Links section in a group box
        new_post_links_group = QGroupBox("Extracted Post Links")
        new_post_links_layout = QVBoxLayout(new_post_links_group)
        new_post_links_layout.setContentsMargins(10, 15, 10, 10)
        new_post_links_layout.setSpacing(10)

        # Post links list with styling
        self.new_post_links_list = QListWidget()
        self.new_post_links_list.setMinimumHeight(200)
        self.new_post_links_list.setAlternatingRowColors(True)
        self.new_post_links_list.setSelectionMode(QListWidget.ExtendedSelection)  # Allow multiple selection
        self.new_post_links_list.setToolTip("List of extracted Facebook post links (select multiple with Ctrl/Shift)")
        self.new_post_links_list.setStyleSheet("""
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        new_post_links_layout.addWidget(self.new_post_links_list)

        # Create a grid layout for buttons to organize them better
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(8)
        buttons_grid.setContentsMargins(0, 5, 0, 5)

        # Create all buttons for post links management

        # Row 1: Action buttons
        # Copy button with icon
        self.copy_new_post_links_button = QPushButton("Copy All")
        self.copy_new_post_links_button.setIcon(QIcon.fromTheme("edit-copy", QIcon()))
        self.copy_new_post_links_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.copy_new_post_links_button.setToolTip("Copy all post links to clipboard")
        self.copy_new_post_links_button.setStyleSheet("background-color: #0078d7; color: white;")
        self.copy_new_post_links_button.clicked.connect(self.on_copy_new_post_links)

        # Send to Post Spammer button with icon
        self.send_to_spammer_button = QPushButton("Send All to Spammer")
        self.send_to_spammer_button.setIcon(QIcon.fromTheme("go-next", QIcon()))
        self.send_to_spammer_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.send_to_spammer_button.setToolTip("Send all post links to Post Spammer")
        self.send_to_spammer_button.setStyleSheet("background-color: #9C27B0; color: white;")
        self.send_to_spammer_button.clicked.connect(self.on_send_to_post_spammer)

        # Send selected to Post Spammer button with icon
        self.send_selected_to_spammer_button = QPushButton("Send Selected")
        self.send_selected_to_spammer_button.setIcon(QIcon.fromTheme("go-next", QIcon()))
        self.send_selected_to_spammer_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.send_selected_to_spammer_button.setToolTip("Send selected post links to Post Spammer")
        self.send_selected_to_spammer_button.setStyleSheet("background-color: #673AB7; color: white;")
        self.send_selected_to_spammer_button.clicked.connect(self.on_send_selected_to_post_spammer)

        # Clear button with icon
        self.clear_new_post_links_button = QPushButton("Clear All")
        self.clear_new_post_links_button.setIcon(QIcon.fromTheme("edit-clear", QIcon()))
        self.clear_new_post_links_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_new_post_links_button.setToolTip("Clear all post links from the list")
        self.clear_new_post_links_button.setStyleSheet("background-color: #f44336; color: white;")
        self.clear_new_post_links_button.clicked.connect(self.on_clear_new_post_links)

        # Row 2: Export buttons
        # Export to JSON button
        self.export_json_button = QPushButton("Export to JSON")
        self.export_json_button.setIcon(QIcon.fromTheme("document-save", QIcon()))
        self.export_json_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.export_json_button.setToolTip("Export all post data to a JSON file")
        self.export_json_button.setStyleSheet("background-color: #2196F3; color: white;")
        self.export_json_button.clicked.connect(self.on_export_json)

        # Export to CSV button
        self.export_csv_button = QPushButton("Export to CSV")
        self.export_csv_button.setIcon(QIcon.fromTheme("document-save", QIcon()))
        self.export_csv_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.export_csv_button.setToolTip("Export all post data to a CSV file")
        self.export_csv_button.setStyleSheet("background-color: #00BCD4; color: white;")
        self.export_csv_button.clicked.connect(self.on_export_csv)

        # Export to Clipboard button
        self.export_clipboard_button = QPushButton("Export to Clipboard")
        self.export_clipboard_button.setIcon(QIcon.fromTheme("edit-copy", QIcon()))
        self.export_clipboard_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.export_clipboard_button.setToolTip("Export post data to clipboard in tabular format")
        self.export_clipboard_button.setStyleSheet("background-color: #009688; color: white;")
        self.export_clipboard_button.clicked.connect(self.on_export_clipboard)

        # Add buttons to grid layout - 3 buttons per row
        # Row 1: Action buttons
        buttons_grid.addWidget(self.copy_new_post_links_button, 0, 0)
        buttons_grid.addWidget(self.send_to_spammer_button, 0, 1)
        buttons_grid.addWidget(self.send_selected_to_spammer_button, 0, 2)

        # Row 2: More action buttons and export buttons
        buttons_grid.addWidget(self.clear_new_post_links_button, 1, 0)
        buttons_grid.addWidget(self.export_json_button, 1, 1)
        buttons_grid.addWidget(self.export_csv_button, 1, 2)

        # Row 3: Export to clipboard button
        buttons_grid.addWidget(self.export_clipboard_button, 2, 0, 1, 3)  # Span across 3 columns

        # Add the grid layout to the main layout
        new_post_links_layout.addLayout(buttons_grid)

        # Log section in a group box
        log_group = QGroupBox("Operation Log")
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(10, 15, 10, 10)
        log_layout.setSpacing(10)

        # Log text area with styling
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(200)
        self.log_text.setToolTip("Log of operations and events")
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: Consolas, Monaco, monospace;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        log_layout.addWidget(self.log_text)

        # Log buttons in a horizontal layout
        log_buttons = QHBoxLayout()
        log_buttons.setSpacing(10)

        # Clear log button with icon
        self.clear_log_button = QPushButton("Clear Log")
        self.clear_log_button.setIcon(QIcon.fromTheme("edit-clear", QIcon()))
        self.clear_log_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.clear_log_button.setToolTip("Clear the log")
        self.clear_log_button.setStyleSheet("background-color: #FF9800; color: white;")
        self.clear_log_button.clicked.connect(self.on_clear_log)

        log_buttons.addWidget(self.clear_log_button)
        log_buttons.addStretch()

        log_layout.addLayout(log_buttons)

        # Add to results layout
        results_layout.addWidget(new_post_links_group, 1)
        results_layout.addWidget(log_group, 1)

        bottom_layout.addLayout(results_layout)

        # Add bottom widget to splitter
        splitter.addWidget(bottom_widget)

        # Set initial splitter sizes
        splitter.setSizes([450, 550])

    def load_settings(self):
        """Load saved settings."""
        try:
            # Get settings from controller
            settings = self.controller.load_settings()

            if settings:
                # Load profile ID
                if "profile_id" in settings:
                    self.profile_id_input.setCurrentText(settings["profile_id"])

                # Load fetch method
                if "fetch_method" in settings:
                    self.fetch_method.setCurrentIndex(settings["fetch_method"])

                # Load posts count
                if "posts_count" in settings:
                    self.posts_count_spinbox.setValue(settings["posts_count"])

                # Load days count
                if "days_count" in settings:
                    self.days_count_spinbox.setValue(settings["days_count"])

                # Scroll delay is now automatic (1-3 seconds with random variation)
                # No need to load it from settings

                # Load deep scroll frequency
                if "deep_scroll_frequency" in settings:
                    self.deep_scroll_spinbox.setValue(settings["deep_scroll_frequency"])

                # Load checkboxes
                if "auto_add" in settings:
                    self.auto_add_checkbox.setChecked(settings["auto_add"])

                if "detect_selectors" in settings:
                    self.detect_selectors_checkbox.setChecked(settings["detect_selectors"])

                # Removed use_adaptive_selectors checkbox

                if "headless_mode" in settings:
                    self.headless_mode.setChecked(settings["headless_mode"])

                # Load extraction method
                if "extraction_method" in settings:
                    if settings["extraction_method"] == "date_click":
                        self.extraction_method_combo.setCurrentIndex(1)  # Date Click Method
                    elif settings["extraction_method"] == "direct":
                        self.extraction_method_combo.setCurrentIndex(2)  # Direct Method
                    else:
                        self.extraction_method_combo.setCurrentIndex(0)  # XHR Method

                self.log_message("[INFO] Loaded saved settings")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to load settings: {str(e)}")

    def save_settings(self):
        """Save current settings."""
        try:
            settings_dir = os.path.join(os.path.expanduser("~"), "Zpammer", "settings")
            os.makedirs(settings_dir, exist_ok=True)

            settings_path = os.path.join(settings_dir, "post_scraper_settings.json")

            # Get scrolling settings
            scrolling_settings = self.get_scrolling_settings()

            settings = {
                "profile_id": self.profile_id_input.currentText(),
                "fetch_all": self.fetch_all_radio.isChecked(),
                "fetch_by_posts": self.fetch_by_posts_radio.isChecked(),
                "posts_count": self.posts_count_spinbox.value(),
                "fetch_by_days": self.fetch_by_days_radio.isChecked(),
                "days_count": self.days_count_spinbox.value(),
                "auto_add_to_new_post_links": self.auto_add_checkbox.isChecked(),
                "detect_selectors": self.detect_selectors_checkbox.isChecked(),
                "scroll_delay": 2,  # Default value, will be randomized in controller
                "random_delay": True,  # Flag to use random delay between 1-3 seconds
                "deep_scroll_frequency": scrolling_settings["deep_scroll_frequency"]
            }

            with open(settings_path, "w", encoding="utf-8") as f:
                json.dump(settings, f, indent=4)

            self.log_message("[INFO] Settings saved successfully")
            return settings
        except Exception as e:
            self.log_message(f"[ERROR] Failed to save settings: {e}")
            return None

    def load_saved_group_links(self):
        """Load saved group links."""
        try:
            # Get group links from controller
            group_links = self.controller.load_group_links()

            # Add to list
            for link in group_links:
                self.group_links_list.addItem(link)

            # Update group count in title
            self.update_group_count()

            self.log_message(f"[INFO] Loaded {len(group_links)} saved group links")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to load saved group links: {str(e)}")

    def save_group_links(self):
        """Save current group links."""
        try:
            # Get all group links
            group_links = [self.group_links_list.item(i).text() for i in range(self.group_links_list.count())]

            # Save using controller
            self.controller.save_group_links(group_links)
            return True
        except Exception as e:
            self.log_message(f"[ERROR] Failed to save group links: {str(e)}")
            return False

    def update_group_count(self):
        """Update the group count in the group box title."""
        count = self.group_links_list.count()
        self.group_links_group.setTitle(f"Facebook Group Links ({count})")

    def update_posts_count(self):
        """Update the posts count in the group box title."""
        count = self.new_post_links_list.count()
        self.posts_group.setTitle(f"Extracted Posts ({count})")

    def log_message(self, message):
        """Add a message to the log."""
        # Filter out messages about downloading photos
        if "downloading a photo" in message.lower():
            return

        self.log_text.append(message)
        # Scroll to the bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def on_add_group_link(self):
        """Add a group link to the list."""
        link = self.add_group_link_input.text().strip()
        if not link:
            return

        # Check if the link is already in the list
        for i in range(self.group_links_list.count()):
            if self.group_links_list.item(i).text() == link:
                self.log_message(f"[WARNING] Group link already exists: {link}")
                return

        # Add the link to the list
        self.group_links_list.addItem(link)
        self.add_group_link_input.clear()

        # Update group count in title
        self.update_group_count()

        # Save the updated list
        self.save_group_links()

        self.log_message(f"[INFO] Added group link: {link}")

    def on_remove_group_link(self):
        """Remove the selected group link from the list."""
        selected_items = self.group_links_list.selectedItems()
        if not selected_items:
            return

        for item in selected_items:
            self.log_message(f"[INFO] Removed group link: {item.text()}")
            self.group_links_list.takeItem(self.group_links_list.row(item))

        # Update group count in title
        self.update_group_count()

        # Save the updated list
        self.save_group_links()

    def on_import_group_links(self):
        """Import group links from a file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Group Links",
            "",
            "Text Files (*.txt);;All Files (*)"
        )

        if not file_path:
            return

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                links = [line.strip() for line in f.readlines() if line.strip()]

            # Add links to the list
            added_count = 0
            for link in links:
                # Check if the link is already in the list
                exists = False
                for i in range(self.group_links_list.count()):
                    if self.group_links_list.item(i).text() == link:
                        exists = True
                        break

                if not exists:
                    self.group_links_list.addItem(link)
                    added_count += 1

            # Update group count in title
            self.update_group_count()

            # Save the updated list
            self.save_group_links()

            self.log_message(f"[INFO] Imported {added_count} group links from {file_path}")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to import group links: {str(e)}")

    def on_edit_group_links(self):
        """Edit group links in bulk."""
        # Get current links
        links = [self.group_links_list.item(i).text() for i in range(self.group_links_list.count())]

        # Show dialog to edit links
        dialog = QDialog(self)
        dialog.setWindowTitle("Edit Group Links")
        dialog.setMinimumSize(500, 400)

        layout = QVBoxLayout(dialog)

        text_edit = QTextEdit()
        text_edit.setPlainText("\n".join(links))
        layout.addWidget(text_edit)

        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            # Get edited links
            edited_links = [line.strip() for line in text_edit.toPlainText().split("\n") if line.strip()]

            # Clear list and add edited links
            self.group_links_list.clear()
            for link in edited_links:
                self.group_links_list.addItem(link)

            # Update group count in title
            self.update_group_count()

            # Save the updated list
            self.save_group_links()

            self.log_message(f"[INFO] Updated group links. Now have {len(edited_links)} links.")

    @pyqtSlot()
    def on_clear_group_links(self):
        """Clear all group links from the list."""
        if self.group_links_list.count() == 0:
            return

        # Ask for confirmation
        reply = QMessageBox.question(
            self,
            "Clear Group Links",
            "Are you sure you want to clear all group links?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.group_links_list.clear()
            self.save_group_links()
            self.log_message("[INFO] Cleared all group links")

    @pyqtSlot(bool)
    def on_fetch_option_changed(self, _):
        """Handle fetch option radio button changes.

        Args:
            _: Ignored parameter from signal
        """
        self.posts_count_spinbox.setEnabled(self.fetch_by_posts_radio.isChecked())
        self.days_count_spinbox.setEnabled(self.fetch_by_days_radio.isChecked())

    def get_scrolling_settings(self):
        """Get scrolling settings from the UI.

        Returns:
            dict: Dictionary with scrolling settings
        """
        return {
            "scroll_delay": 2,  # Default value, will be randomized in controller
            "deep_scroll_frequency": self.deep_scroll_spinbox.value(),
            "random_delay": True  # Flag to use random delay between 1-3 seconds
        }

    @pyqtSlot()
    def on_save_settings(self):
        """Save current settings."""
        settings = self.save_settings()
        if settings:
            self.log_message("[INFO] Settings saved successfully")
            QMessageBox.information(self, "Settings Saved", "Your settings have been saved successfully.")
        else:
            self.log_message("[ERROR] Failed to save settings")
            QMessageBox.critical(self, "Error", "Failed to save settings. Please check the log for details.")

    def on_start_scraping(self):
        """Start the scraping process."""
        # Check if there are any group links
        if self.group_links_list.count() == 0:
            QMessageBox.warning(self, "No Group Links", "Please add at least one group link to scrape.")
            return

        # Check if a profile ID is provided
        profile_id = self.profile_id_input.currentText().strip()
        if not profile_id:
            QMessageBox.warning(self, "No Profile ID", "Please enter a profile ID.")
            return

        # Get group links
        group_links = [self.group_links_list.item(i).text() for i in range(self.group_links_list.count())]

        # Get settings
        extraction_method = "direct"
        if self.extraction_method_combo.currentIndex() == 0:
            extraction_method = "xhr"
        elif self.extraction_method_combo.currentIndex() == 1:
            extraction_method = "date_click"

        # Get headless mode setting
        headless_mode = self.headless_mode.isChecked()

        settings = {
            "profile_id": profile_id,
            "fetch_method": self.fetch_method.currentIndex(),
            "posts_count": self.posts_count_spinbox.value(),
            "days_count": self.days_count_spinbox.value(),
            "scroll_delay": 2,  # Default value, will be randomized in controller
            "deep_scroll_frequency": self.deep_scroll_spinbox.value(),
            "auto_add": self.auto_add_checkbox.isChecked(),
            "detect_selectors": self.detect_selectors_checkbox.isChecked(),
            "headless": headless_mode,  # Use 'headless' key for consistency
            "headless_mode": headless_mode,  # Keep for backward compatibility
            "random_delay": True,  # Flag to use random delay between 1-3 seconds
            "extraction_method": extraction_method,
            "min_scroll_delay": 1500,  # 1.5 seconds minimum delay
            "max_scroll_delay": 3000   # 3 seconds maximum delay
        }

        # Update UI
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("Status: Running")
        self.progress_bar.setValue(0)

        # Log settings
        self.log_message("[INFO] Starting post scraper")
        self.log_message(f"[INFO] Using profile: {profile_id}")
        self.log_message(f"[INFO] Extraction method: {settings['extraction_method'].upper()}")
        self.log_message(f"[INFO] Deep scroll frequency: {settings['deep_scroll_frequency']} scrolls")
        self.log_message(f"[INFO] Headless mode: {settings['headless']}")

        # Show additional info for Direct Method
        if settings['extraction_method'] == 'direct':
            self.log_message("[INFO] Using enhanced direct extraction with improved date/time detection")

        # Show additional info for Date Click Method
        if settings['extraction_method'] == 'date_click':
            self.log_message("[INFO] Using enhanced date click extraction with improved date/time detection")
            if settings['headless']:
                self.log_message("[INFO] Running in headless mode with aggressive extraction enabled")

        if settings['fetch_method'] == 0:  # All Posts
            self.log_message("[INFO] Will extract ALL available posts")
        elif settings['fetch_method'] == 1:  # By Post Count
            self.log_message(f"[INFO] Will extract up to {settings['posts_count']} posts per group")
        elif settings['fetch_method'] == 2:  # By Days
            self.log_message(f"[INFO] Will extract posts from the last {settings['days_count']} days")

        # Start the controller
        self.controller.start_scraping(group_links, settings)

    def on_stop_scraping(self):
        """Stop the scraping process."""
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Are you sure you want to stop the post scraper?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop scraping
            self.controller.stop_scraping()

            # Update UI
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("Status: Stopped")

            # Log stop
            self.log_message("[WARNING] Post Scraper stopped by user")


    def on_detect_selectors(self):
        """Detect post selectors."""
        # Get profile ID
        profile_id = self.profile_id_input.currentText().strip()
        if not profile_id:
            QMessageBox.warning(self, "Warning", "Please enter a profile ID.")
            return

        # Get first group link
        if self.group_links_list.count() > 0:
            group_url = self.group_links_list.item(0).text()
        else:
            QMessageBox.warning(self, "Warning", "Please add at least one group link.")
            return

        # Confirm with the user
        reply = QMessageBox.question(
            self,
            "Detect Selectors",
            f"This will open a browser using profile {profile_id} to detect selectors from {group_url}. Continue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.No:
            return

        # Disable UI elements
        self.detect_selectors_button.setEnabled(False)
        self.start_button.setEnabled(False)

        # Log the action
        self.log_message("[INFO] Starting selector detection...")
        self.log_message(f"[INFO] Using profile: {profile_id}")
        self.log_message(f"[INFO] Using group: {group_url}")

        # Get headless mode setting
        headless_mode = self.headless_mode.isChecked()
        self.log_message(f"[INFO] Headless mode: {headless_mode}")

        # Update controller settings
        self.controller.settings = {
            "headless_mode": headless_mode,
            "detect_selectors": True
        }

        try:
            # Detect selectors
            updated = self.controller.detect_post_selectors(profile_id, group_url)

            if updated:
                self.log_message("[SUCCESS] New post selectors detected and saved")
                QMessageBox.information(
                    self,
                    "Success",
                    "New post selectors were detected and saved successfully."
                )
            else:
                self.log_message("[INFO] No new post selectors detected")
                QMessageBox.information(
                    self,
                    "Information",
                    "No new post selectors were detected. Current selectors are up to date."
                )
        except Exception as e:
            self.log_message(f"[ERROR] Failed to detect selectors: {str(e)}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to detect selectors: {str(e)}"
            )
        finally:
            # Re-enable UI elements
            self.detect_selectors_button.setEnabled(True)
            self.start_button.setEnabled(True)

    @pyqtSlot()
    def on_save_settings(self):
        """Save current settings."""
        settings = self.save_settings()
        if settings:
            self.log_message("[INFO] Settings saved for future use")

    @pyqtSlot()
    def on_copy_new_post_links(self):
        """Copy all new post links to clipboard."""
        if self.new_post_links_list.count() == 0:
            self.log_message("[WARNING] No post links to copy")
            return

        links = []
        for i in range(self.new_post_links_list.count()):
            links.append(self.new_post_links_list.item(i).text())

        try:
            import pyperclip
            pyperclip.copy("\n".join(links))
            self.log_message(f"[INFO] Copied {len(links)} post links to clipboard")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to copy to clipboard: {e}")

    def on_clear_new_post_links(self):
        """Clear all post links."""
        if self.new_post_links_list.count() == 0:
            return

        # Ask for confirmation
        reply = QMessageBox.question(
            self,
            "Clear Post Links",
            "Are you sure you want to clear all extracted post links?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.new_post_links_list.clear()
            self.update_posts_count()
            self.log_message("[INFO] Cleared all post links")

    @pyqtSlot()
    def on_send_to_post_spammer(self):
        """Send all post links to Post Spammer tab."""
        if self.new_post_links_list.count() == 0:
            self.log_message("[WARNING] No post links to send to Post Spammer")
            return

        links = []
        for i in range(self.new_post_links_list.count()):
            links.append(self.new_post_links_list.item(i).text())

        self._send_links_to_spammer(links, "all")

    @pyqtSlot()
    def on_send_selected_to_post_spammer(self):
        """Send selected post links to Post Spammer tab."""
        selected_items = self.new_post_links_list.selectedItems()
        if not selected_items:
            self.log_message("[WARNING] No post links selected to send to Post Spammer")
            return

        links = [item.text() for item in selected_items]
        self._send_links_to_spammer(links, "selected")

    def _send_links_to_spammer(self, links, mode="all"):
        """Send links to Post Spammer tab.

        Args:
            links (list): List of links to send
            mode (str): Mode of operation ('all' or 'selected')
        """
        if not links:
            self.log_message("[WARNING] No post links to send to Post Spammer")
            return

        try:
            # Get the main window
            main_window = self.window()

            # Find the Post Spammer tab
            post_spammer_tab = None

            # Check if main_window has tabs attribute
            if hasattr(main_window, 'tabs'):
                # Find the Spammer tab
                for i in range(main_window.tabs.count()):
                    tab = main_window.tabs.widget(i)
                    tab_text = main_window.tabs.tabText(i)
                    if tab_text == "Spammer":
                        post_spammer_tab = tab
                        break

                if post_spammer_tab:
                    # Switch to the Post Spammer tab
                    main_window.tabs.setCurrentWidget(post_spammer_tab)

                # Add links to the Post Spammer tab
                if hasattr(post_spammer_tab, 'add_links'):
                    post_spammer_tab.add_links(links)
                    if mode == "all":
                        self.log_message(f"[INFO] Sent all {len(links)} post links to Post Spammer tab")
                    else:
                        self.log_message(f"[INFO] Sent {len(links)} selected post links to Post Spammer tab")
                else:
                    self.log_message("[ERROR] Post Spammer tab does not have an add_links method")
            else:
                self.log_message("[ERROR] Could not find Post Spammer tab")
        except Exception as e:
            self.log_message(f"[ERROR] Failed to send links to Post Spammer: {e}")

    def on_clear_log(self):
        """Clear the log."""
        self.log_text.clear()

    def on_export_json(self):
        """Export posts to JSON file."""
        self.controller.export_to_json()

    def on_export_csv(self):
        """Export posts to CSV file."""
        self.controller.export_to_csv()

    def on_export_clipboard(self):
        """Export posts to clipboard."""
        self.controller.export_to_clipboard()

    def on_log_message(self, message):
        """Handle log message from controller."""
        # Filter out messages about downloading photos
        if "downloading a photo" in message.lower():
            return

        self.log_message(message)

    def on_progress_updated(self, current, total, message):
        """Handle progress update from controller."""
        progress = int(current / total * 100) if total > 0 else 0
        self.progress_bar.setValue(progress)
        self.status_label.setText(f"Status: {message}")

    def on_post_links_updated(self, post_links):
        """Handle post links update from controller."""
        # Add new post links to list
        for link in post_links:
            # Check if already in list
            exists = False
            for i in range(self.new_post_links_list.count()):
                if self.new_post_links_list.item(i).text() == link:
                    exists = True
                    break

            if not exists:
                self.new_post_links_list.addItem(link)

        # Update posts count in title
        self.update_posts_count()

        self.log_message(f"[INFO] Added {len(post_links)} new post links to the list")

    def on_scraping_completed(self):
        """Handle scraping completed signal from controller."""
        # Update UI
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("Status: Completed")
        self.progress_bar.setValue(100)

        # Log completion
        self.log_message("[INFO] Post scraping completed")
