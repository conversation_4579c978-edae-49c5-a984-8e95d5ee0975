from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QTabWidget, QWidget, QLabel,
                            QVBoxLayout, QStatusBar, QToolBar, QAction, QMessageBox, QSizePolicy)
from PyQt5.QtCore import Qt, QPoint
from PyQt5.QtGui import QIcon, QColor, QPixmap, QPainter

# Import tabs
from .widgets.tabs.dashboard import DashboardTab
from .widgets.tabs.spammer import SpammerTab
from .widgets.tabs.checker import CheckerTab
from .widgets.tabs.logginer import LogginerTab
from .widgets.tabs.member_scraper import MemberScraperTab
from .widgets.tabs.settings import SettingsTab
from .widgets.tabs.statistics import StatisticsTab
from .widgets.tabs.post_scraper_tab import PostScraperTab

class MainWindow(QMainWindow):
    """Main window of the Zpammer GUI application."""

    def __init__(self):
        super().__init__()

        # Set window properties
        self.setWindowTitle("Zpammer v2.0")
        self.setMinimumSize(1200, 800)

        # Create central widget and main layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(5, 5, 5, 5)

        # Create and setup UI components
        self.setup_toolbar()
        self.setup_tabs()
        self.setup_statusbar()

        # Connect signals and slots
        self.connect_signals()

    def create_icon(self, color, shape="circle", size=32):
        """Create an icon with the specified color and shape.

        Args:
            color (QColor): Color of the icon.
            shape (str): Shape of the icon ("circle", "square", "triangle", "play", "stop", "gear").
            size (int): Size of the icon in pixels.

        Returns:
            QIcon: Colored icon with the specified shape.
        """
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(color)
        painter.setPen(Qt.NoPen)

        # Draw the specified shape
        if shape == "circle":
            painter.drawEllipse(4, 4, size-8, size-8)
        elif shape == "square":
            painter.drawRect(4, 4, size-8, size-8)
        elif shape == "triangle":
            points = [QPoint(size//2, 4), QPoint(size-4, size-4), QPoint(4, size-4)]
            painter.drawPolygon(points)
        elif shape == "play":
            # Draw a play triangle
            points = [QPoint(8, 4), QPoint(size-4, size//2), QPoint(8, size-4)]
            painter.drawPolygon(points)
        elif shape == "stop":
            # Draw a stop square
            painter.drawRect(8, 8, size-16, size-16)
        elif shape == "gear":
            # Draw a simplified gear
            painter.drawEllipse(8, 8, size-16, size-16)
            painter.drawEllipse(size//3, size//3, size//3, size//3)

        painter.end()
        return QIcon(pixmap)

    def setup_toolbar(self):
        """Create and configure the main toolbar."""
        self.toolbar = QToolBar("Main Toolbar")
        self.toolbar.setMovable(False)
        self.toolbar.setStyleSheet("""
            QToolBar {
                spacing: 10px;
                padding: 5px;
                background-color: #2c3e50;
                color: white;
                border: none;
                min-height: 30px;
            }
            QLabel {
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 0 10px;
            }
            QToolButton {
                color: white;
                font-weight: bold;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: rgba(255, 255, 255, 0.1);
            }
            QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QToolButton:pressed {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)
        self.addToolBar(self.toolbar)

        # Add program title
        title_label = QLabel("Zpammer v2.0")
        self.toolbar.addWidget(title_label)

        # Add spacer to push the following items to the right
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.toolbar.addWidget(spacer)

        # Create icons for settings and about
        settings_icon = self.create_icon(QColor(33, 150, 243), "gear")  # Blue gear
        about_icon = self.create_icon(QColor(156, 39, 176), "circle")  # Purple circle

        # Add actions to toolbar
        self.action_settings = QAction(settings_icon, "Settings", self)
        self.action_about = QAction(about_icon, "About", self)

        # Set tooltips
        self.action_settings.setToolTip("Open settings")
        self.action_about.setToolTip("About Zpammer")

        self.toolbar.addAction(self.action_settings)
        self.toolbar.addAction(self.action_about)

    def setup_tabs(self):
        """Create and configure the tab widget with all tabs."""
        self.tabs = QTabWidget()
        self.tabs.setTabPosition(QTabWidget.North)
        self.tabs.setDocumentMode(True)

        # Create tab instances
        self.dashboard_tab = DashboardTab()
        self.spammer_tab = SpammerTab()
        self.checker_tab = CheckerTab()
        self.logginer_tab = LogginerTab()
        self.member_scraper_tab = MemberScraperTab()
        self.post_scraper_tab = PostScraperTab()
        self.statistics_tab = StatisticsTab()
        self.settings_tab = SettingsTab()

        # Add tabs to tab widget
        self.tabs.addTab(self.dashboard_tab, "Dashboard")
        self.tabs.addTab(self.spammer_tab, "Spammer")
        self.tabs.addTab(self.checker_tab, "Checker")
        self.tabs.addTab(self.logginer_tab, "Logginer")
        self.tabs.addTab(self.member_scraper_tab, "Member Scraper")
        self.tabs.addTab(self.post_scraper_tab, "Post Scraper")
        self.tabs.addTab(self.statistics_tab, "Statistics")
        self.tabs.addTab(self.settings_tab, "Settings")

        # Add tab widget to main layout
        self.main_layout.addWidget(self.tabs)

    def setup_statusbar(self):
        """Create and configure the status bar."""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        self.statusbar.showMessage("Ready")

    def connect_signals(self):
        """Connect signals to slots."""
        self.action_settings.triggered.connect(self.on_settings)
        self.action_about.triggered.connect(self.on_about)

        # Connect tab change signal
        self.tabs.currentChanged.connect(self.on_tab_changed)

    # Removed on_start and on_stop methods as they are no longer needed

    def on_settings(self):
        """Open settings tab."""
        self.tabs.setCurrentWidget(self.settings_tab)

    def on_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self,
            "About Zpammer",
            "<h1>Zpammer v2.0</h1>"
            "<p>A powerful Facebook automation tool with GUI.</p>"
            "<p>© 2025 Zpammer Team</p>"
        )

    def on_tab_changed(self, index):
        """Handle tab change event."""
        tab_name = self.tabs.tabText(index)
        self.statusbar.showMessage(f"Current tab: {tab_name}")

    def closeEvent(self, event):
        """Handle window close event."""
        reply = QMessageBox.question(
            self,
            "Exit Confirmation",
            "Are you sure you want to exit?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Perform cleanup operations
            event.accept()
        else:
            event.ignore()
