from PyQt5.QtWidgets import QCheckBox
from PyQt5.QtCore import Qt, QRect, QPoint, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush, QPaintEvent


class ToggleSwitch(QCheckBox):
    """
    A custom toggle switch widget that looks like a modern switch.
    """

    def __init__(self, parent=None, track_radius=10, thumb_radius=8):
        super().__init__(parent)

        # Set size policy
        self.setFixedSize(4 * track_radius, 2 * track_radius)

        # Set cursor to pointing hand
        self.setCursor(Qt.PointingHandCursor)

        # Save parameters
        self._track_radius = track_radius
        self._thumb_radius = thumb_radius

        # Setup colors
        self._track_color_on = QColor(53, 199, 89)  # Green
        self._track_color_off = QColor(155, 155, 155)  # Gray
        self._thumb_color_on = QColor(255, 255, 255)  # White
        self._thumb_color_off = QColor(255, 255, 255)  # White

        # Add shadow effect for better appearance
        self._shadow_color = QColor(0, 0, 0, 50)  # Semi-transparent black

        # Setup animation
        self._thumb_position = 0.0
        self._animation = QPropertyAnimation(self, b"thumbPosition")
        self._animation.setEasingCurve(QEasingCurve.OutQuad)  # Faster easing curve
        self._animation.setDuration(100)  # Shorter animation duration for faster response

        # Connect signals
        self.stateChanged.connect(self._on_state_changed)

    def _on_state_changed(self, state):
        """Handle state change event."""
        if state == Qt.Checked:
            self._animation.setEndValue(1.0)
        else:
            self._animation.setEndValue(0.0)
        self._animation.start()

    def paintEvent(self, event: QPaintEvent):
        """Paint the toggle switch."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Calculate track and thumb positions
        track_opacity = 0.6
        margin = max(0, self._track_radius - self._thumb_radius)

        # Track position
        track_rect = QRect(
            margin,
            margin,
            self.width() - 2 * margin,
            self.height() - 2 * margin
        )

        # Thumb position
        thumb_x = margin + self._thumb_position * (self.width() - 2 * margin - 2 * self._thumb_radius)
        thumb_rect = QRect(
            int(thumb_x),
            int(margin),
            int(2 * self._thumb_radius),
            int(2 * self._thumb_radius)
        )

        # Determine colors based on state
        if self.isChecked():
            track_color = self._track_color_on
            thumb_color = self._thumb_color_on
        else:
            track_color = self._track_color_off
            thumb_color = self._thumb_color_off

        # Draw track
        track_opacity_color = QColor(track_color)
        track_opacity_color.setAlphaF(track_opacity)
        painter.setBrush(track_opacity_color)
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(track_rect, self._track_radius, self._track_radius)

        # Draw shadow for thumb
        shadow_rect = QRect(thumb_rect)
        shadow_rect.moveRight(shadow_rect.right() + 1)  # Offset shadow slightly
        shadow_rect.moveBottom(shadow_rect.bottom() + 1)
        painter.setBrush(self._shadow_color)
        painter.drawEllipse(shadow_rect)

        # Draw thumb
        painter.setBrush(thumb_color)
        painter.drawEllipse(thumb_rect)

    def sizeHint(self):
        """Return the recommended size for the widget."""
        from PyQt5.QtCore import QSize
        return QSize(4 * self._track_radius, 2 * self._track_radius)

    # Property for animation
    @pyqtProperty(float)
    def thumbPosition(self):
        return self._thumb_position

    @thumbPosition.setter
    def thumbPosition(self, pos):
        self._thumb_position = pos
        self.update()
