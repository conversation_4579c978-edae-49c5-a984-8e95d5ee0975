from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
                            QPushButton, QLabel, QFileDialog, QMessageBox, QHeaderView)
from PyQt5.QtCore import Qt, pyqtSlot

from ..widgets.tabs.csv_controller import CSVController

class CSVEditorDialog(QDialog):
    """Dialog for editing CSV files."""
    
    def __init__(self, file_path=None, title="CSV Editor", parent=None):
        """
        Initialize the CSV editor dialog.
        
        Args:
            file_path (str, optional): Path to the CSV file to edit.
            title (str, optional): Dialog title.
            parent (QWidget, optional): Parent widget.
        """
        super().__init__(parent)
        
        self.file_path = file_path
        self.setWindowTitle(title)
        self.resize(800, 600)
        
        # Create controller
        self.controller = CSVController()
        
        # Connect controller signals
        self.controller.log_message.connect(self.log_message)
        self.controller.data_loaded.connect(self.on_data_loaded)
        
        # Setup UI
        self.setup_ui()
        
        # Load data if file_path is provided
        if file_path:
            self.load_csv(file_path)
    
    def setup_ui(self):
        """Create and configure the UI components."""
        layout = QVBoxLayout(self)
        
        # File path display
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("File:"))
        self.path_label = QLabel(self.file_path or "New File")
        path_layout.addWidget(self.path_label, 1)
        self.btn_browse = QPushButton("Browse")
        path_layout.addWidget(self.btn_browse)
        layout.addLayout(path_layout)
        
        # Table widget
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.table)
        
        # Control buttons
        control_layout = QHBoxLayout()
        self.btn_add_row = QPushButton("Add Row")
        self.btn_delete_row = QPushButton("Delete Row")
        self.btn_save = QPushButton("Save")
        self.btn_save.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        self.btn_cancel = QPushButton("Cancel")
        
        control_layout.addWidget(self.btn_add_row)
        control_layout.addWidget(self.btn_delete_row)
        control_layout.addStretch()
        control_layout.addWidget(self.btn_save)
        control_layout.addWidget(self.btn_cancel)
        layout.addLayout(control_layout)
        
        # Log display
        self.log_label = QLabel("Ready")
        layout.addWidget(self.log_label)
        
        # Connect signals
        self.btn_browse.clicked.connect(self.browse_file)
        self.btn_add_row.clicked.connect(self.add_row)
        self.btn_delete_row.clicked.connect(self.delete_row)
        self.btn_save.clicked.connect(self.save_csv)
        self.btn_cancel.clicked.connect(self.reject)
    
    def load_csv(self, file_path=None):
        """
        Load a CSV file into the editor.
        
        Args:
            file_path (str, optional): Path to the CSV file.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        if file_path:
            self.file_path = file_path
            self.path_label.setText(file_path)
        
        if not self.file_path:
            return False
        
        # Load CSV data
        self.controller.load_csv(self.file_path)
        return True
    
    @pyqtSlot(list, list)
    def on_data_loaded(self, headers, data):
        """
        Handle data loaded from CSV file.
        
        Args:
            headers (list): List of column names.
            data (list): List of rows.
        """
        # Clear table
        self.table.clear()
        
        # Set table dimensions
        self.table.setRowCount(len(data))
        self.table.setColumnCount(len(headers))
        
        # Set headers
        self.table.setHorizontalHeaderLabels(headers)
        
        # Populate table
        for row_idx, row in enumerate(data):
            for col_idx, cell in enumerate(row):
                if col_idx < len(headers):  # Ensure we don't exceed the number of columns
                    item = QTableWidgetItem(cell)
                    self.table.setItem(row_idx, col_idx, item)
    
    def get_table_data(self):
        """
        Get data from the table.
        
        Returns:
            tuple: (headers, data) where headers is a list of column names and data is a list of rows.
        """
        # Get headers
        headers = []
        for col_idx in range(self.table.columnCount()):
            header_item = self.table.horizontalHeaderItem(col_idx)
            headers.append(header_item.text() if header_item else f"Column {col_idx+1}")
        
        # Get data
        data = []
        for row_idx in range(self.table.rowCount()):
            row_data = []
            for col_idx in range(self.table.columnCount()):
                item = self.table.item(row_idx, col_idx)
                row_data.append(item.text() if item else "")
            data.append(row_data)
        
        return headers, data
    
    def browse_file(self):
        """
        Browse for a CSV file to open.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open CSV File", "Files", "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            return self.load_csv(file_path)
        
        return False
    
    def add_row(self):
        """Add a new row to the table."""
        row_idx = self.table.rowCount()
        self.table.insertRow(row_idx)
        
        # Initialize cells with empty values
        for col_idx in range(self.table.columnCount()):
            self.table.setItem(row_idx, col_idx, QTableWidgetItem(""))
        
        self.log_message("[INFO] Added new row")
    
    def delete_row(self):
        """Delete the selected row from the table."""
        selected_rows = set()
        for item in self.table.selectedItems():
            selected_rows.add(item.row())
        
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "No rows selected for deletion.")
            return
        
        # Remove rows in reverse order to avoid index shifting
        for row_idx in sorted(selected_rows, reverse=True):
            self.table.removeRow(row_idx)
        
        self.log_message(f"[INFO] Deleted {len(selected_rows)} row(s)")
    
    def save_csv(self):
        """
        Save the current content to the CSV file.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        if not self.file_path:
            return self.save_csv_as()
        
        # Get data from table
        headers, data = self.get_table_data()
        
        # Save to file
        success = self.controller.save_csv(self.file_path, headers, data)
        
        if success:
            QMessageBox.information(self, "Success", "CSV file saved successfully.")
            self.accept()
        
        return success
    
    def save_csv_as(self):
        """
        Save the current content to a new CSV file.
        
        Returns:
            bool: True if successful, False otherwise.
        """
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save CSV File As", "Files", "CSV Files (*.csv);;All Files (*)"
        )
        
        if file_path:
            self.file_path = file_path
            self.path_label.setText(file_path)
            return self.save_csv()
        
        return False
    
    def log_message(self, message):
        """Display a log message."""
        self.log_label.setText(message)
