from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTextEdit, QListWidget, QProgressBar,
                            QCheckBox, QSpinBox, QSplitter, QFileDialog, QMessageBox, QInputDialog,
                            QComboBox)
from PyQt5.QtCore import Qt, pyqtSlot

# Import dialogs
from ...dialogs.bulk_editor import BulkEditorDialog

# Import controller
from .member_scraper_controller import MemberScraperController

class MemberScraperTab(QWidget):
    """Member Scraper tab for scraping members from Facebook groups."""

    def __init__(self):
        super().__init__()

        # Create controller
        self.controller = MemberScraperController()

        # Connect controller signals
        self.controller.progress_updated.connect(self.on_progress_updated)
        self.controller.log_message.connect(self.log_message)

        self.setup_ui()

    def setup_ui(self):
        """Create and configure the UI components."""
        main_layout = QHBoxLayout(self)

        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Left panel - configuration
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Group links section
        self.group_links_group = QGroupBox("Group Links (0)")
        group_links_layout = QVBoxLayout()
        self.group_links_list = QListWidget()

        group_links_buttons = QHBoxLayout()
        self.btn_import_group_links = QPushButton("Import File")
        self.btn_add_group_link = QPushButton("Add New")
        self.btn_delete_selected_group_link = QPushButton("Delete Selected")
        self.btn_delete_selected_group_link.setStyleSheet("background-color: #f44336; color: white;")
        self.btn_bulk_group_links = QPushButton("Edit Links")
        self.btn_bulk_group_links.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")

        group_links_buttons.addWidget(self.btn_import_group_links)
        group_links_buttons.addWidget(self.btn_add_group_link)
        group_links_buttons.addWidget(self.btn_delete_selected_group_link)
        group_links_buttons.addWidget(self.btn_bulk_group_links)

        group_links_layout.addWidget(self.group_links_list)
        group_links_layout.addLayout(group_links_buttons)
        self.group_links_group.setLayout(group_links_layout)

        # Members section
        self.members_group = QGroupBox("Members (0)")
        members_layout = QVBoxLayout()
        self.members_list = QListWidget()
        # Permitir selección múltiple
        self.members_list.setSelectionMode(QListWidget.ExtendedSelection)

        members_buttons = QHBoxLayout()
        self.btn_import_members = QPushButton("Import File")
        self.btn_add_member = QPushButton("Add New")
        self.btn_delete_selected_member = QPushButton("Delete Selected")
        self.btn_delete_selected_member.setStyleSheet("background-color: #f44336; color: white;")
        self.btn_bulk_members = QPushButton("Edit Members")
        self.btn_bulk_members.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")

        members_buttons.addWidget(self.btn_import_members)
        members_buttons.addWidget(self.btn_add_member)
        members_buttons.addWidget(self.btn_delete_selected_member)
        members_buttons.addWidget(self.btn_bulk_members)

        members_layout.addWidget(self.members_list)
        members_layout.addLayout(members_buttons)
        self.members_group.setLayout(members_layout)

        # Add groups to left layout
        left_layout.addWidget(self.group_links_group)
        left_layout.addWidget(self.members_group)

        # Right panel - controls and output
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Settings section
        self.settings_group = QGroupBox("Member Scraper Settings")
        settings_layout = QVBoxLayout()

        # Profile selection
        profile_layout = QHBoxLayout()
        profile_layout.addWidget(QLabel("Profile ID:"))
        self.profile_id_input = QComboBox()
        self.profile_id_input.setEditable(True)

        # Load profiles from database
        profiles = self.controller.db.get_profiles()
        if profiles:
            for profile in profiles:
                self.profile_id_input.addItem(profile.get('profile_id', ''))

        profile_layout.addWidget(self.profile_id_input)
        settings_layout.addLayout(profile_layout)

        # Members per group
        members_per_group_layout = QHBoxLayout()
        members_per_group_layout.addWidget(QLabel("Members per group:"))
        self.members_per_group = QSpinBox()
        self.members_per_group.setValue(1000)
        self.members_per_group.setRange(10, 10000000)  # Permitir hasta 10 millones de miembros
        self.members_per_group.setSingleStep(1000)  # Incrementos de 1000
        self.members_per_group.setGroupSeparatorShown(True)  # Mostrar separadores de miles
        members_per_group_layout.addWidget(self.members_per_group)
        settings_layout.addLayout(members_per_group_layout)

        # Scroll delay
        scroll_delay_layout = QHBoxLayout()
        scroll_delay_layout.addWidget(QLabel("Scroll delay (seconds):"))
        self.scroll_delay = QSpinBox()
        self.scroll_delay.setValue(2)
        self.scroll_delay.setRange(1, 10)
        scroll_delay_layout.addWidget(self.scroll_delay)
        settings_layout.addLayout(scroll_delay_layout)

        # Checkboxes for options
        self.save_profile_ids = QCheckBox("Save profile IDs")
        self.headless_mode = QCheckBox("Headless mode")

        self.save_profile_ids.setChecked(True)

        settings_layout.addWidget(self.save_profile_ids)
        settings_layout.addWidget(self.headless_mode)

        self.settings_group.setLayout(settings_layout)

        # Control section
        self.control_group = QGroupBox("Control")
        control_layout = QVBoxLayout()

        # Botones de control en una fila
        control_buttons_layout = QHBoxLayout()

        self.btn_start = QPushButton("Start Scraper")
        self.btn_start.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 5px; font-size: 11px;")
        self.btn_start.setFixedHeight(30)

        self.btn_stop = QPushButton("Stop Scraper")
        self.btn_stop.setStyleSheet("background-color: #f44336; color: white; font-weight: bold; padding: 5px; font-size: 11px;")
        self.btn_stop.setFixedHeight(30)
        self.btn_stop.setEnabled(False)

        control_buttons_layout.addWidget(self.btn_start)
        control_buttons_layout.addWidget(self.btn_stop)

        control_layout.addLayout(control_buttons_layout)

        # Progress section
        progress_layout = QHBoxLayout()
        progress_layout.addWidget(QLabel("Progress:"))
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        control_layout.addLayout(progress_layout)

        # Status label
        self.status_label = QLabel("Status: Idle")
        control_layout.addWidget(self.status_label)

        self.control_group.setLayout(control_layout)

        # Results section
        self.results_group = QGroupBox("Results")
        results_layout = QVBoxLayout()

        # Statistics
        stats_layout = QVBoxLayout()
        self.groups_processed_label = QLabel("Groups Processed: 0/0")
        self.members_scraped_label = QLabel("Members Scraped: 0")
        self.new_members_label = QLabel("New Members: 0")
        stats_layout.addWidget(self.groups_processed_label)
        stats_layout.addWidget(self.members_scraped_label)
        stats_layout.addWidget(self.new_members_label)
        results_layout.addLayout(stats_layout)

        self.results_group.setLayout(results_layout)

        # Log section
        self.log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        self.log_group.setLayout(log_layout)

        # Add groups to right layout
        right_layout.addWidget(self.settings_group)
        right_layout.addWidget(self.control_group)
        right_layout.addWidget(self.results_group)
        right_layout.addWidget(self.log_group)

        # Add panels to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # Set initial splitter sizes (40% left, 60% right)
        splitter.setSizes([400, 600])

        # Connect signals
        self.connect_signals()

        # Load sample data
        self.load_sample_data()

    def update_item_counts(self):
        """Update the item counts in group titles."""
        # Get counts from database
        group_links_count = self.controller.db.get_groups_count()
        members_count = self.controller.db.get_members_count()

        # Update UI
        self.group_links_group.setTitle(f"Group Links ({group_links_count})")
        self.members_group.setTitle(f"Members ({members_count})")

        # Update list widgets if they don't match the database
        if self.group_links_list.count() != group_links_count:
            self.refresh_group_links_list()

        if self.members_list.count() != members_count:
            self.refresh_members_list()

    def connect_signals(self):
        """Connect signals to slots."""
        # Button signals for group links
        self.btn_import_group_links.clicked.connect(self.on_import_group_links)
        self.btn_add_group_link.clicked.connect(self.on_add_group_link)
        self.btn_delete_selected_group_link.clicked.connect(self.on_delete_selected_group_link)
        self.btn_bulk_group_links.clicked.connect(self.on_bulk_group_links)

        # Button signals for members
        self.btn_import_members.clicked.connect(self.on_import_members)
        self.btn_add_member.clicked.connect(self.on_add_member)
        self.btn_delete_selected_member.clicked.connect(self.on_delete_selected_member)
        self.btn_bulk_members.clicked.connect(self.on_bulk_members)

        # Control buttons
        self.btn_start.clicked.connect(self.start)
        self.btn_stop.clicked.connect(self.stop)

        # Connect controller finished signal
        self.controller.finished.connect(self.on_scraper_finished)

    def refresh_group_links_list(self):
        """Refresh the group links list from the database."""
        group_links = self.controller.load_group_links()
        self.group_links_list.clear()
        self.group_links_list.addItems(group_links)

    def refresh_members_list(self):
        """Refresh the members list from the database."""
        members = self.controller.load_members()
        self.members_list.clear()
        self.members_list.addItems(members)

    def load_sample_data(self):
        """Load sample data for demonstration."""
        # Load data from database
        self.refresh_group_links_list()
        self.refresh_members_list()

        # Update item counts
        self.update_item_counts()

        # Log initial message
        self.log_message("[INFO] Sample data loaded")

    def log_message(self, message):
        """Add a message to the log."""
        self.log_text.append(message)
        # Auto-scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def on_import_group_links(self):
        """Handle import group links button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Group Links File", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing group links from {file_path}")

            try:
                # Import group links directly to database
                group_links = self.controller.load_group_links(file_path)

                # Refresh the group links list
                self.refresh_group_links_list()

                # Update item counts
                self.update_item_counts()

                # Show success message
                QMessageBox.information(self, "Import Successful", f"Successfully imported {len(group_links)} group links.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import group links: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import group links: {e}")

    def on_add_group_link(self):
        """Handle add group link button click."""
        group_link, ok = QInputDialog.getText(
            self, "Add Group Link", "Enter group link:"
        )

        if ok and group_link.strip():
            group_url = group_link.strip()

            # Check if group link exists in database
            existing_group = self.controller.db.get_group(group_url)

            if not existing_group:
                # Save group link to database
                if self.controller.db.save_group(group_url):
                    self.log_message(f"[INFO] Added new group link: {group_url}")

                    # Refresh the group links list
                    self.refresh_group_links_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add group link: {group_url}")
                    QMessageBox.critical(self, "Error", f"Failed to add group link '{group_url}'.")
            else:
                self.log_message(f"[WARNING] Group link already exists: {group_url}")
                QMessageBox.warning(self, "Duplicate Group Link", f"Group link '{group_url}' already exists.")

    def on_bulk_group_links(self):
        """Handle bulk edit group links button click."""
        self.log_message("[INFO] Opening bulk group links editor")

        # Get group links from database
        groups_data = self.controller.db.get_groups()
        current_links = [g['group_url'] for g in groups_data]

        # Create and show bulk editor dialog
        dialog = BulkEditorDialog("Bulk Edit Group Links", current_links, "group links", self)

        # Connect the items_updated signal
        dialog.items_updated.connect(self.on_group_links_updated)

        # Show dialog
        dialog.exec_()

    @pyqtSlot(list)
    def on_group_links_updated(self, links):
        """Handle group links updated from bulk editor."""
        self.log_message(f"[INFO] Updating group links list with {len(links)} links")

        # Save group links to database
        self.controller.save_group_links(links)

        # Refresh the group links list
        self.refresh_group_links_list()

        # Update item counts
        self.update_item_counts()

    def on_import_members(self):
        """Handle import members button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Members File", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing members from {file_path}")

            try:
                # Import members directly to database
                members = self.controller.load_members(file_path)

                # Refresh the members list
                self.refresh_members_list()

                # Update item counts
                self.update_item_counts()

                # Show success message
                QMessageBox.information(self, "Import Successful", f"Successfully imported {len(members)} members.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import members: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import members: {e}")

    def on_add_member(self):
        """Handle add member button click."""
        member, ok = QInputDialog.getText(
            self, "Add Member", "Enter member ID:"
        )

        if ok and member.strip():
            member_id = member.strip()

            # Check if member exists in database
            existing_member = self.controller.db.get_member(member_id)

            if not existing_member:
                # Save member to database
                if self.controller.db.save_member(member_id):
                    self.log_message(f"[INFO] Added new member: {member_id}")

                    # Refresh the members list
                    self.refresh_members_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add member: {member_id}")
                    QMessageBox.critical(self, "Error", f"Failed to add member '{member_id}'.")
            else:
                self.log_message(f"[WARNING] Member already exists: {member_id}")
                QMessageBox.warning(self, "Duplicate Member", f"Member '{member_id}' already exists.")

    def on_bulk_members(self):
        """Handle bulk edit members button click."""
        self.log_message("[INFO] Opening bulk members editor")

        # Get members from database
        members_data = self.controller.db.get_members()
        current_members = [m['member_id'] for m in members_data]

        # Create and show bulk editor dialog
        dialog = BulkEditorDialog("Bulk Edit Members", current_members, "members", self)

        # Connect the items_updated signal
        dialog.items_updated.connect(self.on_members_updated)

        # Show dialog
        dialog.exec_()

    @pyqtSlot(list)
    def on_members_updated(self, members):
        """Handle members updated from bulk editor."""
        self.log_message(f"[INFO] Updating members list with {len(members)} members")

        # Save members to database
        self.controller.save_members(members)

        # Refresh the members list
        self.refresh_members_list()

        # Update item counts
        self.update_item_counts()

    def start(self):
        """Start the member scraper process."""
        # Get group links from list
        group_links = [self.group_links_list.item(i).text() for i in range(self.group_links_list.count())]

        # Get settings
        profile_id = self.profile_id_input.currentText().strip()

        settings = {
            "profile_id": profile_id,
            "members_per_group": self.members_per_group.value(),
            "scroll_delay": self.scroll_delay.value(),
            "save_profile_ids": self.save_profile_ids.isChecked(),
            "headless_mode": self.headless_mode.isChecked()
        }

        # Validate profile ID
        if not profile_id:
            QMessageBox.warning(self, "Warning", "Please select or enter a profile ID.")
            return

        # Validate inputs
        if not group_links:
            QMessageBox.warning(self, "Warning", "No group links loaded.")
            return

        # Update UI
        self.btn_start.setEnabled(False)
        self.btn_stop.setEnabled(True)
        self.status_label.setText("Status: Running")
        self.progress_bar.setValue(0)

        # Start member scraper process
        self.controller.start_scraper(group_links, settings)

    def stop(self):
        """Stop the member scraper process."""
        reply = QMessageBox.question(
            self,
            "Confirmation",
            "Are you sure you want to stop the member scraper?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop member scraper process
            self.controller.stop_scraper()

            # Update UI
            self.btn_start.setEnabled(True)
            self.btn_stop.setEnabled(False)
            self.status_label.setText("Status: Stopped")

            # Log stop
            self.log_message("[WARNING] Member Scraper stopped by user")

    def on_delete_selected_group_link(self):
        """Handle delete selected group link button click."""
        # Get selected items
        selected_items = self.group_links_list.selectedItems()

        if not selected_items:
            QMessageBox.warning(self, "Warning", "No group links selected.")
            return

        # Confirm deletion
        count = len(selected_items)
        reply = QMessageBox.question(
            self,
            "Confirmation",
            f"Are you sure you want to delete {count} selected group link(s)?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete selected items
            deleted_count = 0
            for item in selected_items:
                group_url = item.text()

                # Get group from database
                group = self.controller.db.get_group(group_url)
                if group:
                    # Delete group from database
                    if self.controller.db.delete_group(group_url):
                        deleted_count += 1
                        self.log_message(f"[INFO] Deleted group link: {group_url}")
                    else:
                        self.log_message(f"[ERROR] Failed to delete group link: {group_url}")

            # Refresh the group links list
            self.refresh_group_links_list()

            # Update item counts
            self.update_item_counts()

            # Show success message
            if deleted_count > 0:
                QMessageBox.information(self, "Delete Successful", f"Successfully deleted {deleted_count} group link(s).")

    def on_delete_selected_member(self):
        """Handle delete selected member button click."""
        # Get selected items
        selected_items = self.members_list.selectedItems()

        if not selected_items:
            QMessageBox.warning(self, "Warning", "No members selected.")
            return

        # Confirm deletion
        count = len(selected_items)
        reply = QMessageBox.question(
            self,
            "Confirmation",
            f"Are you sure you want to delete {count} selected member(s)?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete selected items
            deleted_count = 0
            member_ids_to_delete = []

            # Primero recopilamos todos los IDs de miembros a eliminar
            for item in selected_items:
                member_name = item.text()

                # Buscar todos los miembros en la base de datos
                members_data = self.controller.db.get_members()

                # Buscar el miembro por nombre o ID
                for member in members_data:
                    # Comprobar si coincide con el nombre o el ID
                    if (member.get('name') and member['name'] == member_name) or member['member_id'] == member_name:
                        member_ids_to_delete.append(member['member_id'])
                        break

            # Ahora eliminamos todos los miembros en una sola operación
            if member_ids_to_delete:
                self.log_message(f"[INFO] Attempting to delete {len(member_ids_to_delete)} members...")

                # Eliminar miembros uno por uno para mantener el registro
                for member_id in member_ids_to_delete:
                    # Obtener el nombre del miembro para el registro
                    member_data = self.controller.db.get_member(member_id)
                    member_name = member_data.get('name', member_id) if member_data else member_id

                    # Eliminar el miembro
                    if self.controller.db.delete_member(member_id):
                        deleted_count += 1
                        self.log_message(f"[INFO] Deleted member: {member_name} (ID: {member_id})")
                    else:
                        self.log_message(f"[ERROR] Failed to delete member: {member_name} (ID: {member_id})")

                # Refresh the members list
                self.refresh_members_list()

                # Update item counts
                self.update_item_counts()

                # Show success message
                if deleted_count > 0:
                    QMessageBox.information(self, "Delete Successful", f"Successfully deleted {deleted_count} member(s).")
            else:
                self.log_message(f"[WARNING] No matching members found in database for the selected items")

    @pyqtSlot(dict)
    def on_scraper_finished(self, result):
        """Handle scraper finished signal.

        Args:
            result (dict): Result dictionary from the worker thread.
        """
        # Update UI
        self.btn_start.setEnabled(True)
        self.btn_stop.setEnabled(False)
        self.progress_bar.setValue(100)
        self.status_label.setText("Status: Idle")

        # Process results
        if result.get("success"):
            total_members = result.get("total_members", 0)
            new_members = result.get("new_members", 0)
            member_ids = result.get("member_ids", [])

            # Update statistics labels
            self.members_scraped_label.setText(f"Members Scraped: {total_members}")
            self.new_members_label.setText(f"New Members: {new_members}")

            # Añadir los miembros extraídos a la lista de miembros
            self.log_message(f"[INFO] Updating members list with extracted members")

            # Guardar el número actual de miembros para comparar después
            current_count = self.members_list.count()

            # Forzar una recarga de la base de datos para asegurarnos de que tenemos los datos más recientes
            self.controller.db.connect()

            # Limpiar la lista actual para asegurarnos de que se actualiza correctamente
            self.members_list.clear()

            # Cargar todos los miembros de la base de datos
            members = self.controller.load_members()

            # Verificar si hay miembros en la base de datos
            db_count = self.controller.db.get_members_count()
            self.log_message(f"[INFO] Found {db_count} members in database")

            # Si no hay miembros en la base de datos pero hay miembros extraídos, intentar guardarlos de nuevo
            if db_count == 0 and result.get("members") and len(result.get("members", [])) > 0:
                self.log_message(f"[WARNING] No members in database but {len(result.get('members', []))} were extracted. Trying to save them again...")

                # Intentar guardar los miembros extraídos de nuevo
                saved_count = 0
                error_count = 0
                error_details = []

                for member in result.get("members", []):
                    member_id = member.get("id")
                    member_name = member.get("name")

                    if member_id and member_name:
                        save_result = self.controller.db.save_member(member_id, name=member_name)

                        if isinstance(save_result, dict):
                            if save_result.get('status') in ['new', 'updated']:
                                saved_count += 1
                            elif save_result.get('status') == 'error':
                                error_count += 1
                                error_msg = save_result.get('error', 'Unknown error')
                                error_details.append(f"{member_name} (ID: {member_id}): {error_msg}")
                                if len(error_details) <= 5:  # Limit the number of detailed error messages
                                    self.log_message(f"[ERROR] Failed to save member: {member_name} (ID: {member_id}). Error: {error_msg}")
                        else:
                            # Compatibilidad con versiones anteriores
                            if save_result:
                                saved_count += 1
                            else:
                                error_count += 1

                self.log_message(f"[INFO] Saved {saved_count} members to database, {error_count} errors")

                # Si hay errores, mostrar detalles adicionales
                if error_count > 0:
                    if error_count > 5:
                        self.log_message(f"[WARNING] Too many errors ({error_count}). Showing only the first 5.")

                    # Intentar diagnosticar problemas comunes
                    self.log_message("[INFO] Diagnosing database issues...")
                    try:
                        # Verificar si la tabla members existe
                        cursor = self.controller.db.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='members'")
                        if cursor and not cursor.fetchone():
                            self.log_message("[ERROR] The 'members' table does not exist in the database!")
                            self.log_message("[INFO] Creating 'members' table...")
                            self.controller.verify_database_tables()

                            # Intentar guardar de nuevo después de crear la tabla
                            self.log_message("[INFO] Retrying member save operation...")
                            saved_count = 0
                            for member in result.get("members", [])[:10]:  # Intentar con los primeros 10 miembros
                                member_id = member.get("id")
                                member_name = member.get("name")
                                if member_id and member_name:
                                    if self.controller.db.save_member(member_id, name=member_name):
                                        saved_count += 1
                            self.log_message(f"[INFO] Saved {saved_count} members after table creation")
                    except Exception as e:
                        self.log_message(f"[ERROR] Database diagnosis failed: {str(e)}")

                # Recargar los miembros
                members = self.controller.load_members()

            # Añadir todos los miembros a la lista
            if members:
                self.log_message(f"[INFO] Adding {len(members)} members to the list")
                self.members_list.addItems(members)
            else:
                self.log_message("[WARNING] No members found in database")

            # Calcular cuántos miembros nuevos se añadieron
            new_count = self.members_list.count()
            added_count = new_count - current_count

            self.log_message(f"[INFO] Added {added_count} new members to the members list (total: {new_count})")

            # Ya hemos actualizado la lista de miembros, no es necesario refrescarla de nuevo
            # self.refresh_members_list()

            # Update item counts
            self.update_item_counts()

            # Show message
            QMessageBox.information(
                self,
                "Scraper Finished",
                f"Member scraper has finished.\n\nExtracted {total_members} members, {new_members} new members added to database."
            )

            # Cerrar completamente el navegador
            self.controller.close_all_browsers()
            self.log_message("[INFO] All browsers have been closed")
        else:
            error = result.get("error", "Unknown error")
            QMessageBox.warning(self, "Scraper Failed", f"Member scraper failed: {error}")

            # Cerrar completamente el navegador incluso en caso de error
            self.controller.close_all_browsers()
            self.log_message("[INFO] All browsers have been closed")

    @pyqtSlot(int, int, str)
    def on_progress_updated(self, current, total, message):
        """Handle progress update from controller."""
        # Update progress bar
        progress = int(current / total * 100) if total > 0 else 0
        self.progress_bar.setValue(progress)

        # Update status label
        self.status_label.setText(f"Status: {message}")

        # Update statistics labels
        self.groups_processed_label.setText(f"Groups Processed: {current}/{total}")
