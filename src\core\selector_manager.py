"""
Selector Manager

Este módulo gestiona la detección y actualización de selectores para elementos de Facebook.
Permite detectar automáticamente selectores cuando Facebook cambia su estructura HTML.
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class SelectorManager:
    """Gestiona la detección y actualización de selectores para elementos de Facebook."""

    def __init__(self, selectors_file_path=None):
        """
        Inicializa el gestor de selectores.

        Args:
            selectors_file_path (str, optional): Ruta al archivo de selectores.
                Si no se proporciona, se usará la ruta predeterminada.
        """
        # Determinar la ruta del archivo de selectores
        if selectors_file_path is None:
            # Usar la ruta predeterminada
            base_dir = Path(__file__).resolve().parent.parent.parent
            self.selectors_file_path = os.path.join(base_dir, "Files", "facebook_selectors.json")
        else:
            self.selectors_file_path = selectors_file_path

        # Cargar selectores existentes o crear nuevos
        self.selectors = self._load_selectors()

        # Ruta al script JavaScript para detección de selectores
        self.js_detector_path = os.path.join(
            base_dir, "src", "gui", "widgets", "tabs", "js", "facebook_selector_detector.js"
        )

        logger.info(f"Selector Manager initialized with file: {self.selectors_file_path}")

    def _load_selectors(self):
        """
        Carga los selectores desde el archivo JSON.

        Returns:
            dict: Diccionario con los selectores cargados.
        """
        try:
            if os.path.exists(self.selectors_file_path):
                with open(self.selectors_file_path, 'r', encoding='utf-8') as f:
                    selectors = json.load(f)
                logger.info(f"Loaded selectors from {self.selectors_file_path}")
                return selectors
            else:
                logger.warning(f"Selectors file not found at {self.selectors_file_path}, creating default")
                return self._create_default_selectors()
        except Exception as e:
            logger.error(f"Error loading selectors: {e}")
            return self._create_default_selectors()

    def _create_default_selectors(self):
        """
        Crea un diccionario de selectores predeterminados.

        Returns:
            dict: Diccionario con selectores predeterminados.
        """
        default_selectors = {
            "timestamp": datetime.now().isoformat(),
            "version": "1.0",
            "selectors": {
                "xpathSelectors": {
                    "reply_button_xpaths": [
                        "//div[13]//div[4]//div[3]/div[{}]//ul/li[3]/div/div",
                        "//div[13]//div[4]//div[3]/div[{}]//ul/li[3]",
                        "//div[13]//div[4]//div[3]/div[{}]//div[2]//div[2]//ul/li[3]",
                        "//div[2]//div[4]//div[3]/div[{}]//div[1]//div[2]//ul/li[3]"
                    ],
                    "comment_full_xpaths": [
                        "//div[13]//div[4]//div[3]/div[{}]",
                        "//div[2]//div[4]//div[3]/div[{}]",
                        "//div[2]//div[4]//div[3]/div[{}]",
                        "//div[3]//div[4]//div[3]/div[{}]",
                        "//div[2]//div[4]//div[2]/div[3]/div[{}]"
                    ]
                },
                "commentButtons": [
                    "div[aria-label*=\"comment\"]",
                    "div[role=\"button\"]:has(span:contains(\"Comment\"))",
                    "span[role=\"button\"]:not([aria-label=\"\"]):nth-of-type(1)"
                ],
                "commentInputFields": [
                    "div[contenteditable=\"true\"][role=\"textbox\"]",
                    "div[contenteditable=\"true\"][aria-label*=\"comment\"]",
                    "div[role=\"textbox\"]"
                ],
                "commentSubmitButtons": [
                    "div[aria-label=\"Comment\"]",
                    "div[aria-label=\"Post\"]",
                    "div[role=\"button\"]:has(span:contains(\"Post\"))"
                ],
                "commentContainers": [
                    ".html-div.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x1gslohp",
                    "div[role=\"article\"] div.x1n2onr6"
                ],
                "replyButtons": [
                    "div[role=\"button\"]:has(span:contains(\"Reply\"))",
                    "span[role=\"button\"]:contains(\"Reply\")"
                ]
            }
        }

        # Guardar los selectores predeterminados
        self._save_selectors(default_selectors)

        return default_selectors

    def _save_selectors(self, selectors):
        """
        Guarda los selectores en el archivo JSON.

        Args:
            selectors (dict): Diccionario con los selectores a guardar.
        """
        try:
            # Asegurarse de que el directorio existe
            os.makedirs(os.path.dirname(self.selectors_file_path), exist_ok=True)

            with open(self.selectors_file_path, 'w', encoding='utf-8') as f:
                json.dump(selectors, f, indent=2)

            logger.info(f"Saved selectors to {self.selectors_file_path}")
        except Exception as e:
            logger.error(f"Error saving selectors: {e}")

    def get_selectors(self, selector_type=None):
        """
        Obtiene los selectores.

        Args:
            selector_type (str, optional): Tipo de selector a obtener.
                Si no se proporciona, se devolverán todos los selectores.

        Returns:
            dict or list: Diccionario con todos los selectores o lista con los selectores del tipo especificado.
        """
        if selector_type is None:
            return self.selectors

        return self.selectors.get("selectors", {}).get(selector_type, [])

    def get_xpath_selectors(self, xpath_type=None):
        """
        Gets XPath selectors.

        Args:
            xpath_type (str, optional): Type of XPath selector to get.
                If not provided, all XPath selectors will be returned.

        Returns:
            dict or list: Dictionary with all XPath selectors or list with the specified XPath selectors.
        """
        xpath_selectors = self.selectors.get("selectors", {}).get("xpathSelectors", {})

        if xpath_type is None:
            return xpath_selectors

        return xpath_selectors.get(xpath_type, [])

    def update_selectors(self, new_selectors):
        """
        Actualiza los selectores con nuevos valores.

        Args:
            new_selectors (dict): Diccionario con los nuevos selectores.
        """
        try:
            # Actualizar timestamp
            new_selectors["timestamp"] = datetime.now().isoformat()

            # Guardar los nuevos selectores
            self._save_selectors(new_selectors)

            # Actualizar selectores en memoria
            self.selectors = new_selectors

            logger.info("Selectors updated successfully")
        except Exception as e:
            logger.error(f"Error updating selectors: {e}")

    def add_selector(self, selector_type, selector):
        """
        Agrega un nuevo selector a la lista.

        Args:
            selector_type (str): Tipo de selector (commentButtons, commentInputFields, etc.).
            selector (str): Selector CSS a agregar.
        """
        try:
            if selector_type not in self.selectors.get("selectors", {}):
                self.selectors["selectors"][selector_type] = []

            if selector not in self.selectors["selectors"][selector_type]:
                self.selectors["selectors"][selector_type].append(selector)
                self._save_selectors(self.selectors)
                logger.info(f"Added new selector '{selector}' to {selector_type}")
        except Exception as e:
            logger.error(f"Error adding selector: {e}")

    def get_js_detector_code(self):
        """
        Obtiene el código JavaScript para la detección de selectores.

        Returns:
            str: Código JavaScript para la detección de selectores.
        """
        try:
            with open(self.js_detector_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error loading JS detector code: {e}")
            return ""

    def process_detected_selectors(self, detected_selectors_json):
        """
        Procesa los selectores detectados por el script JavaScript.

        Args:
            detected_selectors_json (str): JSON con los selectores detectados.

        Returns:
            bool: True si se actualizaron los selectores, False en caso contrario.
        """
        try:
            detected_selectors = json.loads(detected_selectors_json)

            # Verificar si hay nuevos selectores
            updated = False

            for selector_type, selectors in detected_selectors.get("selectors", {}).items():
                for selector in selectors:
                    if selector_type not in self.selectors.get("selectors", {}):
                        self.selectors["selectors"][selector_type] = []

                    if selector not in self.selectors["selectors"][selector_type]:
                        self.selectors["selectors"][selector_type].append(selector)
                        updated = True

            if updated:
                # Actualizar timestamp
                self.selectors["timestamp"] = datetime.now().isoformat()

                # Guardar los selectores actualizados
                self._save_selectors(self.selectors)

                logger.info("Selectors updated with newly detected selectors")

            return updated
        except Exception as e:
            logger.error(f"Error processing detected selectors: {e}")
            return False
