"""
Controller for the post scraper functionality.
"""

import os
import json
import time
import random
import traceback
from PyQt5.QtCore import QObject, pyqtSignal, QThread

from core.utils.browser import BrowserManager
from core.post_selector_manager import PostSelectorManager
# Removed unused imports


# AdaptiveScraperMixin class removed - replaced with Advanced Facebook Post Extractor


class PostScraperController(QObject):
    """Controller for the post scraper functionality."""

    # Signals
    log_message = pyqtSignal(str)
    progress_updated = pyqtSignal(int, int, str)
    post_links_updated = pyqtSignal(list)
    scraping_completed = pyqtSignal()

    def __init__(self, parent=None):
        """Initialize the controller."""
        super().__init__(parent)
        self.browser_manager = BrowserManager()
        self.selector_manager = PostSelectorManager()
        self.is_running = False
        self.worker_thread = None
        self.worker = None

        # Track all extracted posts across groups
        self.all_extracted_posts = []

        # Set up file paths
        self.output_dir = os.path.join(os.path.expanduser("~"), "Zpammer", "output")
        os.makedirs(self.output_dir, exist_ok=True)

        # File paths for output
        self.database_file_path = os.path.join(self.output_dir, "posts_data.json")
        self.post_links_file_path = os.path.join(self.output_dir, "post_links.txt")

        self.log_message.emit("[INFO] Post Scraper initialized")

    def start_scraping(self, group_links, settings):
        """Start the post scraping process.

        Args:
            group_links (list): List of group links to scrape
            settings (dict): Scraper settings
        """
        if self.is_running:
            self.log_message.emit("[WARNING] Post scraper is already running")
            return

        # Save settings
        self.save_settings(settings)

        # Get profile ID from settings
        profile_id = settings.get("profile_id", "")
        if not profile_id:
            self.log_message.emit("[ERROR] No profile ID provided")
            return

        self.is_running = True
        self.log_message.emit("[INFO] Starting post scraper")

        # Reset the list of extracted posts
        self.all_extracted_posts = []

        # Create a new worker and thread
        self.log_message.emit("[INFO] Starting post scraper worker")
        self.worker = NewXHRScraperWorker(self, group_links, profile_id, settings)

        self.worker_thread = QThread()

        # Move worker to thread
        self.worker.moveToThread(self.worker_thread)

        # Connect signals
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.worker_thread.quit)
        self.worker.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)

        # Connect worker signals to controller signals
        self.worker.log_message.connect(self.log_message)
        self.worker.progress_updated.connect(self.progress_updated)
        self.worker.post_links_updated.connect(self.post_links_updated)
        self.worker.finished.connect(self.on_scraping_completed)

        # Start the thread
        self.worker_thread.start()

    def stop_scraping(self):
        """Stop the post scraping process and close the browser."""
        if not self.is_running:
            self.log_message.emit("[WARNING] Post scraper is not running")
            return

        self.log_message.emit("[INFO] Stopping post scraper and closing browser")
        self.is_running = False

        # Signal the worker to stop and close browser
        if self.worker:
            self.worker.stop(close_browser=True)

        # Force kill any remaining browser processes
        try:
            self.log_message.emit("[INFO] Ensuring all browser processes are terminated")
            self.browser_manager.kill_nstchrome_processes()
            self.log_message.emit("[INFO] All browser processes terminated successfully")
        except Exception as e:
            self.log_message.emit(f"[WARNING] Error terminating browser processes: {e}")

    def on_scraping_completed(self, result=None):
        """Handle scraping completion."""
        self.is_running = False

        # Log completion message
        self.log_message.emit("[INFO] Post scraping completed")

        # Log the number of posts in all_extracted_posts
        self.log_message.emit(f"[INFO] Total posts in controller: {len(self.all_extracted_posts)}")

        # If we have posts, update the UI
        if self.all_extracted_posts:
            self.log_message.emit(f"[INFO] Posts are available for export")
        else:
            self.log_message.emit(f"[WARNING] No posts available for export")

        # Emit scraping completed signal
        self.scraping_completed.emit()

    def clear_file(self, file_path):
        """Clear the content of a file.

        Args:
            file_path (str): Path to the file to clear
        """
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write("")
            self.log_message.emit(f"[INFO] Cleared file: {file_path}")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to clear file {file_path}: {e}")

    def save_post_links(self, post_links):
        """Save post links to a file.

        Args:
            post_links (list): List of post links to save
        """
        try:
            with open(self.post_links_file_path, "w", encoding="utf-8") as f:
                for link in post_links:
                    f.write(f"{link}\n")
            self.log_message.emit(f"[INFO] Saved {len(post_links)} post links to {self.post_links_file_path}")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save post links: {e}")

    def save_group_links(self, group_links):
        """Save group links to a file.

        Args:
            group_links (list): List of group links to save
        """
        try:
            # Create settings directory if it doesn't exist
            settings_dir = os.path.join(os.path.expanduser("~"), "Zpammer", "settings")
            os.makedirs(settings_dir, exist_ok=True)

            # Save group links to file
            group_links_path = os.path.join(settings_dir, "post_scraper_group_links.json")
            with open(group_links_path, "w", encoding="utf-8") as f:
                json.dump(group_links, f, indent=4)

            self.log_message.emit(f"[INFO] Saved {len(group_links)} group links to {group_links_path}")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save group links: {e}")

    def load_group_links(self):
        """Load group links from a file.

        Returns:
            list: List of group links
        """
        try:
            # Get path to group links file
            group_links_path = os.path.join(os.path.expanduser("~"), "Zpammer", "settings", "post_scraper_group_links.json")

            # Check if file exists
            if not os.path.exists(group_links_path):
                self.log_message.emit("[INFO] No saved group links found")
                return []

            # Load group links from file
            with open(group_links_path, "r", encoding="utf-8") as f:
                group_links = json.load(f)

            self.log_message.emit(f"[INFO] Loaded {len(group_links)} group links from {group_links_path}")
            return group_links
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to load group links: {e}")
            return []

    def save_settings(self, settings):
        """Save settings to a file.

        Args:
            settings (dict): Settings to save
        """
        try:
            # Create settings directory if it doesn't exist
            settings_dir = os.path.join(os.path.expanduser("~"), "Zpammer", "settings")
            os.makedirs(settings_dir, exist_ok=True)

            # Save settings to file
            settings_path = os.path.join(settings_dir, "post_scraper_settings.json")
            with open(settings_path, "w", encoding="utf-8") as f:
                json.dump(settings, f, indent=4)

            self.log_message.emit(f"[INFO] Saved settings to {settings_path}")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save settings: {e}")

    def load_settings(self):
        """Load settings from a file.

        Returns:
            dict: Settings dictionary
        """
        try:
            # Get path to settings file
            settings_path = os.path.join(os.path.expanduser("~"), "Zpammer", "settings", "post_scraper_settings.json")

            # Check if file exists
            if not os.path.exists(settings_path):
                self.log_message.emit("[INFO] No saved settings found")
                return {}

            # Load settings from file
            with open(settings_path, "r", encoding="utf-8") as f:
                settings = json.load(f)

            self.log_message.emit(f"[INFO] Loaded settings from {settings_path}")
            return settings
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to load settings: {e}")
            return {}

    def extract_posts_xhr(self, driver, max_scrolls=10, settings=None):
        """Extract posts from the current page using JavaScript.

        Args:
            driver: WebDriver instance
            max_scrolls: Maximum number of scrolls to perform
            settings: Optional settings dictionary

        Returns:
            list: List of extracted posts
        """
        try:
            self.log_message.emit("[INFO] Starting post extraction using JavaScript...")

            # Wait for the page to load completely
            time.sleep(5)

            # Try using the New Facebook Post Link Extractor
            try:
                from .post_scraper_js import (
                    NEW_FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT,
                    INIT_NEW_FACEBOOK_POST_LINK_EXTRACTOR,
                    EXTRACT_NEXT_BATCH_NEW_EXTRACTOR,
                    GET_ALL_POSTS_NEW_EXTRACTOR
                )

                # Inject the New Facebook Post Link Extractor script
                self.log_message.emit("[INFO] Injecting New Facebook Post Link Extractor script...")
                driver.execute_script(NEW_FACEBOOK_POST_LINK_EXTRACTOR_SCRIPT)

                # Wait for the script to be fully loaded and initialized
                self.log_message.emit("[INFO] Waiting for script to initialize...")
                time.sleep(3)  # Wait 3 seconds for the script to initialize

                # Verify that the script was loaded correctly
                script_loaded = driver.execute_script("return typeof window.FacebookPostLinkExtractor === 'object'")
                if not script_loaded:
                    self.log_message.emit("[WARNING] New Facebook Post Link Extractor script was not loaded correctly")
                    self.log_message.emit("[INFO] Trying to debug script loading...")
                    # Try to debug what happened
                    debug_info = driver.execute_script("""
                    return {
                        'windowKeys': Object.keys(window).filter(k => k.includes('Facebook') || k.includes('fb')),
                        'fbPostLinkExtractor': typeof window.fbPostLinkExtractor,
                        'FacebookPostLinkExtractor': typeof window.FacebookPostLinkExtractor
                    }
                    """)
                    self.log_message.emit(f"[INFO] Debug info: {debug_info}")
                    # Try again with a different approach
                    self.log_message.emit("[INFO] Trying to inject script again with a different approach...")
                    driver.execute_script("""
                    // Define the extractor directly in the window object
                    window.fbPostLinkExtractor = window.FacebookPostLinkExtractor || {
                        setConfig: function(config) {
                            this.config = config;
                            console.log('Config set:', config);
                            return true;
                        },
                        extractNextBatch: async function() { return []; },
                        getCollectedPosts: function() { return []; }
                    };
                    """)
                    time.sleep(1)  # Short wait

                # Configure the extractor with enhanced settings
                config = {
                    # Timing settings - fixed to 1-3 seconds with random variation
                    "minDelay": 1000,  # 1 second minimum delay
                    "maxDelay": 3000,  # 3 seconds maximum delay

                    # Extraction limits
                    "maxAttempts": max(100, max_scrolls * 3),  # Triple the max_scrolls for more attempts
                    "maxPosts": settings.get("posts_count", 5000),  # Use posts_count setting or default to 5000

                    # Scrolling behavior
                    "scrollStep": 800,  # Reduced scroll step for more natural scrolling
                    "maxEmptyScrolls": 5,  # Maximum number of scrolls with no new posts before trying recovery

                    # Advanced features
                    "aggressiveMode": True,  # Enable aggressive extraction mode
                    "retryOnFailure": True,  # Retry extraction if it fails
                    "humanLikeScrolling": True,  # Enable human-like scrolling behavior
                    "randomDelayEnabled": True,  # Enable random delay between 1-3 seconds
                    "logCounterEnabled": True,  # Enable continuous counter in log

                    # Duplicate handling
                    "skipDuplicates": True  # Always skip duplicates for efficiency
                }

                self.log_message.emit(f"[INFO] Initializing New Facebook Post Link Extractor with config: {config}")
                init_result = driver.execute_script(INIT_NEW_FACEBOOK_POST_LINK_EXTRACTOR, config)

                if init_result:
                    self.log_message.emit("[INFO] New Facebook Post Link Extractor initialized successfully")

                    # Extract posts in batches
                    self.log_message.emit("[INFO] Starting post extraction in batches...")

                    # Set up extraction parameters
                    max_posts = 3000  # Default to 3000 posts
                    if settings and "posts_count" in settings:
                        max_posts = min(settings["posts_count"], 3000)  # Use the smaller of the two

                    # Extract posts in batches
                    total_posts = 0
                    last_batch_count = -1  # Initialize to -1 to ensure first batch is processed
                    no_new_posts_count = 0

                    # Calculate number of batches based on settings
                    # Use deep_scroll_frequency as the number of batches to extract
                    num_batches = settings.get("deep_scroll_frequency", 50)  # Increased default to 50
                    self.log_message.emit(f"[INFO] Will extract up to {num_batches} batches of posts")

                    consecutive_failures = 0

                    for batch_num in range(1, num_batches + 1):
                        self.log_message.emit(f"start: fetching posts")

                        try:
                            # Extract next batch of posts with error handling
                            new_posts = driver.execute_script(EXTRACT_NEXT_BATCH_NEW_EXTRACTOR)

                            # Check if we got a valid result
                            if not isinstance(new_posts, list):
                                consecutive_failures += 1
                                self.log_message.emit(f"[WARNING] Failed to extract batch (failure {consecutive_failures}/3)")

                                # If we've had too many consecutive failures, try to recover
                                if consecutive_failures >= 3:
                                    self.log_message.emit("[WARNING] Multiple extraction failures, trying recovery...")

                                    # Try clicking "See More" buttons via JavaScript
                                    driver.execute_script("""
                                    document.querySelectorAll('div[role="button"]:not([aria-hidden="true"]):not([aria-disabled="true"])').forEach(btn => {
                                        if (btn.textContent.toLowerCase().includes('see more') || btn.textContent.toLowerCase().includes('show more')) {
                                            try { btn.click(); } catch(e) { console.error(e); }
                                        }
                                    });
                                    """)

                                    # Always scroll forward, never backward
                                    driver.execute_script("""
                                    const currentPosition = window.scrollY;
                                    const scrollAmount = Math.floor(window.innerHeight * (0.5 + Math.random() * 0.5)); // 50-100% of viewport height
                                    const newPosition = currentPosition + scrollAmount;
                                    console.log(`Scrolling forward by ${scrollAmount}px to position: ${newPosition}`);
                                    window.scrollTo({
                                        top: newPosition,
                                        behavior: 'smooth'
                                    });
                                    """)

                                    # Generate a random recovery delay between 1-3 seconds
                                    recovery_delay = random.uniform(1.0, 3.0)

                                    # Log only the wait time in a simple format
                                    self.log_message.emit(f"To avoid account restrictions, please wait for {recovery_delay:.1f} seconds.")

                                    # Sleep for the required time
                                    time.sleep(recovery_delay)

                                    # Reset failure counter after recovery attempt
                                    consecutive_failures = 0
                                    continue

                                # Generate a random retry delay between 1-3 seconds
                                retry_delay = random.uniform(1.0, 3.0)

                                # Log only the wait time in a simple format
                                self.log_message.emit(f"To avoid account restrictions, please wait for {retry_delay:.1f} seconds.")

                                # Sleep for the required time
                                time.sleep(retry_delay)
                                continue
                            else:
                                # Reset failure counter on success
                                consecutive_failures = 0

                            batch_count = len(new_posts)
                            total_posts = driver.execute_script(GET_ALL_POSTS_NEW_EXTRACTOR)
                            total_count = len(total_posts) if total_posts else 0

                            # Log found posts with their IDs
                            if batch_count > 0:
                                post_ids = [post.get('postId', '') for post in new_posts if post.get('postId')]
                                post_ids_str = ','.join(post_ids)
                                self.log_message.emit(f"done: found {batch_count} posts. {post_ids_str}")

                            # Check if we've reached the maximum number of posts
                            if total_count >= max_posts:
                                self.log_message.emit(f"[INFO] Reached maximum post count ({max_posts}), stopping extraction")
                                break

                            # Check if we found any new posts in this batch
                            if batch_count == 0 or total_count == last_batch_count:
                                no_new_posts_count += 1
                                self.log_message.emit(f"[INFO] No new posts found in batch {batch_num} ({no_new_posts_count}/{5})")

                                # If we've had too many consecutive batches with no new posts, try recovery strategies
                                if no_new_posts_count >= 3:  # Try recovery after 3 consecutive empty batches
                                    self.log_message.emit(f"[INFO] No new posts found in {no_new_posts_count} consecutive batches, trying recovery strategies")

                                    # Try clicking "See More" buttons via JavaScript
                                    driver.execute_script("""
                                    document.querySelectorAll('div[role="button"]:not([aria-hidden="true"]):not([aria-disabled="true"])').forEach(btn => {
                                        if (btn.textContent.toLowerCase().includes('see more') || btn.textContent.toLowerCase().includes('show more')) {
                                            try { btn.click(); } catch(e) { console.error(e); }
                                        }
                                    });
                                    """)

                                    # Always scroll forward, never backward
                                    driver.execute_script("""
                                    const currentPosition = window.scrollY;
                                    const scrollAmount = Math.floor(window.innerHeight * (0.5 + Math.random() * 0.5)); // 50-100% of viewport height
                                    const newPosition = currentPosition + scrollAmount;
                                    console.log(`Scrolling forward by ${scrollAmount}px to position: ${newPosition}`);
                                    window.scrollTo({
                                        top: newPosition,
                                        behavior: 'smooth'
                                    });
                                    """)

                                    # Generate a random recovery delay between 1-3 seconds
                                    recovery_delay = random.uniform(1.0, 3.0)

                                    # Log only the wait time in a simple format
                                    self.log_message.emit(f"To avoid account restrictions, please wait for {recovery_delay:.1f} seconds.")

                                    # Sleep for the required time
                                    time.sleep(recovery_delay)

                                    # Reset counter after recovery attempt
                                    no_new_posts_count = 0
                                    continue

                                # If we've had too many consecutive batches with no new posts after recovery, stop
                                if no_new_posts_count >= 5:  # Stop after 5 consecutive empty batches
                                    self.log_message.emit(f"[INFO] No new posts found in {no_new_posts_count} consecutive batches after recovery, stopping extraction")
                                    break
                            else:
                                # Reset counter if we found new posts
                                no_new_posts_count = 0

                            last_batch_count = total_count

                            # Add a delay between batches with natural variation
                            # Generate a random delay between 1-3 seconds
                            actual_delay = random.uniform(1.0, 3.0)

                            # Occasionally add a longer pause (5% chance)
                            if random.random() < 0.05:
                                extra_delay = random.uniform(0.5, 1.0)
                                actual_delay += extra_delay

                            # Log only the wait time in a simple format
                            self.log_message.emit(f"To avoid account restrictions, please wait for {actual_delay:.1f} seconds.")

                            # Sleep for the required time
                            time.sleep(actual_delay)

                        except Exception as e:
                            self.log_message.emit(f"[ERROR] Error extracting batch: {e}")
                            consecutive_failures += 1

                            # If we've had too many consecutive failures, stop
                            if consecutive_failures >= 3:
                                self.log_message.emit("[ERROR] Multiple extraction failures, stopping extraction")
                                break

                            # Generate a random retry delay between 1-3 seconds
                            retry_delay = random.uniform(1.0, 3.0)

                            # Log only the wait time in a simple format
                            self.log_message.emit(f"To avoid account restrictions, please wait for {retry_delay:.1f} seconds.")

                            # Sleep for the required time
                            time.sleep(retry_delay)

                    # Get all collected posts
                    self.log_message.emit("[INFO] Getting all collected posts from New Facebook Post Link Extractor")
                    all_posts = driver.execute_script(GET_ALL_POSTS_NEW_EXTRACTOR)

                    if all_posts and isinstance(all_posts, list):
                        self.log_message.emit(f"[INFO] Successfully extracted {len(all_posts)} posts using New Facebook Post Link Extractor")

                        # Convert the posts to the format expected by the rest of the code
                        converted_posts = []
                        for post in all_posts:
                            # Handle different post formats based on structure
                            if isinstance(post, str):
                                # If post is a string (URL only), create a minimal post object
                                self.log_message.emit(f"[WARNING] Post is a string, creating minimal post object: {post}")
                                converted_post = {
                                    'url': post,
                                    'postId': post.split('/posts/')[-1].split('/')[0] if '/posts/' in post else '',
                                    'author': 'N/A',
                                    'authorId': 'N/A',
                                    'content': '',
                                    'timestamp': '',
                                    'images': []
                                }
                            else:
                                # Handle the new format where author is directly in the post object
                                if 'author' in post and isinstance(post['author'], dict):
                                    # Old format with author as object
                                    author_name = post.get('author', {}).get('name', '')
                                    author_id = post.get('author', {}).get('profile', '').split('/')[-1] if post.get('author', {}).get('profile') else ''
                                elif 'author' in post and isinstance(post['author'], str):
                                    # New format with author as string
                                    author_name = post.get('author', '')
                                    author_id = post.get('authorId', '') or post.get('authorProfile', '').split('/')[-1] if post.get('authorProfile') else ''
                                else:
                                    # Fallback
                                    author_name = 'Unknown'
                                    author_id = ''

                                # Create the converted post
                                converted_post = {
                                    'url': post.get('url', ''),
                                    'postId': post.get('postId', ''),
                                    'author': author_name,
                                    'authorId': author_id,
                                    'content': post.get('content', ''),
                                    'timestamp': post.get('timestamp', ''),
                                    'images': post.get('images', [])
                                }

                            # Log the conversion for debugging
                            self.log_message.emit(f"[DEBUG] Converted post: {converted_post['url']} | Author: {converted_post['author']} | AuthorID: {converted_post['authorId']}")
                            converted_posts.append(converted_post)

                        return converted_posts
                    else:
                        self.log_message.emit("[WARNING] No posts found with New Facebook Post Link Extractor")
                else:
                    self.log_message.emit("[WARNING] Failed to initialize New Facebook Post Link Extractor")
            except Exception as e:
                self.log_message.emit(f"[WARNING] New Facebook Post Link Extractor failed: {e}. No fallback methods available.")

            # If the extractor fails, return an empty list
            self.log_message.emit("[WARNING] New Facebook Post Link Extractor failed and no fallback methods are available.")
            return []

        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to extract posts: {e}")
            traceback.print_exc()
            return []

    # extract_posts_and_comments_xhr method removed - will be replaced with a new implementation
    # extract_posts_and_comments_new_xhr method removed - will be replaced with a new implementation

    def extract_posts_direct(self, driver, max_scrolls=10, settings=None):
        """Extract posts using the Direct Post Extractor method.

        This method extracts posts directly from the DOM without clicking on date elements:
        1. Finding post elements in the page
        2. Extracting post links directly from the elements
        3. Cleaning and normalizing the URLs
        4. Scrolling to load more posts
        5. Repeating until enough posts are found

        Args:
            driver: WebDriver instance
            max_scrolls: Maximum number of scrolls to perform
            settings: Optional settings dictionary

        Returns:
            list: List of extracted posts
        """
        try:
            self.log_message.emit("[INFO] Starting post extraction using Direct Post Extractor method...")

            # Wait for the page to load completely
            time.sleep(5)

            # Try using the Facebook Direct Post Extractor
            try:
                from .post_scraper_js import (
                    FACEBOOK_DIRECT_POST_EXTRACTOR_SCRIPT,
                    INIT_DIRECT_POST_EXTRACTOR,
                    EXTRACT_NEXT_BATCH_DIRECT,
                    GET_ALL_URLS_DIRECT
                )

                # Inject the Facebook Direct Post Extractor script
                self.log_message.emit("[INFO] Injecting Facebook Direct Post Extractor script...")
                driver.execute_script(FACEBOOK_DIRECT_POST_EXTRACTOR_SCRIPT)

                # Wait for the script to be fully loaded and initialized
                self.log_message.emit("[INFO] Waiting for script to initialize...")
                time.sleep(3)  # Wait 3 seconds for the script to initialize

                # Verify that the script was loaded correctly
                script_loaded = driver.execute_script("return typeof window.FacebookDirectPostExtractor === 'object'")
                if not script_loaded:
                    self.log_message.emit("[WARNING] Facebook Direct Post Extractor script was not loaded correctly")
                    self.log_message.emit("[INFO] Trying to debug script loading...")
                    # Try to debug what happened
                    debug_info = driver.execute_script("""
                    return {
                        'windowKeys': Object.keys(window).filter(k => k.includes('Facebook') || k.includes('fb')),
                        'fbDirectPostExtractor': typeof window.fbDirectPostExtractor,
                        'FacebookDirectPostExtractor': typeof window.FacebookDirectPostExtractor
                    }
                    """)
                    self.log_message.emit(f"[INFO] Debug info: {debug_info}")
                    return []

                # Configure the extractor with enhanced settings
                config = {
                    # Timing settings - fixed to 1-3 seconds with random variation
                    "minDelay": 1500,  # 1.5 seconds minimum delay
                    "maxDelay": 3000,  # 3 seconds maximum delay

                    # Extraction limits
                    "maxPosts": settings.get("posts_count", 3000),  # Use posts_count setting or default to 3000

                    # Scrolling behavior
                    "scrollStep": 800,  # Reduced scroll step for more natural scrolling
                    "maxScrolls": max_scrolls * 2,  # Double the provided max_scrolls for better coverage
                }

                self.log_message.emit(f"[INFO] Initializing Facebook Direct Post Extractor with config: {config}")
                init_result = driver.execute_script(INIT_DIRECT_POST_EXTRACTOR, config)

                if init_result:
                    self.log_message.emit("[INFO] Facebook Direct Post Extractor initialized successfully")

                    # Extract posts in batches
                    self.log_message.emit("[INFO] Starting post extraction in batches...")

                    # Calculate number of batches based on settings
                    num_batches = settings.get("deep_scroll_frequency", 50)  # Increased default to 50
                    self.log_message.emit(f"[INFO] Will extract up to {num_batches} batches of posts")

                    consecutive_failures = 0
                    no_new_posts_count = 0
                    last_batch_count = 0

                    for batch_num in range(1, num_batches + 1):
                        self.log_message.emit(f"start: fetching posts (batch {batch_num}/{num_batches})")

                        try:
                            # Extract next batch of posts with error handling
                            new_urls = driver.execute_script(EXTRACT_NEXT_BATCH_DIRECT)

                            # Check if we got a valid result
                            if not isinstance(new_urls, list):
                                consecutive_failures += 1
                                self.log_message.emit(f"[WARNING] Failed to extract batch (failure {consecutive_failures}/3)")

                                # If we've had too many consecutive failures, try to recover
                                if consecutive_failures >= 3:
                                    self.log_message.emit("[WARNING] Multiple extraction failures, stopping extraction")
                                    break

                                # Generate a random retry delay between 1-3 seconds
                                retry_delay = random.uniform(1.0, 3.0)
                                self.log_message.emit(f"To avoid account restrictions, please wait for {retry_delay:.1f} seconds.")
                                time.sleep(retry_delay)
                                continue
                            else:
                                # Reset failure counter on success
                                consecutive_failures = 0

                            batch_count = len(new_urls)
                            all_urls = driver.execute_script(GET_ALL_URLS_DIRECT)
                            total_count = len(all_urls) if all_urls else 0

                            # Log found posts with their URLs
                            if batch_count > 0:
                                self.log_message.emit(f"done: found {batch_count} posts in batch {batch_num}")
                                for url in new_urls[:5]:  # Show first 5 URLs
                                    self.log_message.emit(f"[INFO] Found URL: {url}")

                            # Check if we've reached the maximum number of posts
                            max_posts = settings.get("posts_count", 3000)
                            if total_count >= max_posts:
                                self.log_message.emit(f"[INFO] Reached maximum post count ({max_posts}), stopping extraction")
                                break

                            # Check if we found any new posts in this batch
                            if batch_count == 0 or total_count == last_batch_count:
                                no_new_posts_count += 1
                                self.log_message.emit(f"[INFO] No new posts found in batch {batch_num} ({no_new_posts_count}/{5})")

                                # If we've had too many consecutive batches with no new posts, stop
                                if no_new_posts_count >= 5:  # Stop after 5 consecutive empty batches
                                    self.log_message.emit(f"[INFO] No new posts found in {no_new_posts_count} consecutive batches, stopping extraction")
                                    break
                            else:
                                # Reset counter if we found new posts
                                no_new_posts_count = 0

                            last_batch_count = total_count

                            # Add a delay between batches with natural variation
                            actual_delay = random.uniform(1.0, 3.0)
                            self.log_message.emit(f"To avoid account restrictions, please wait for {actual_delay:.1f} seconds.")
                            time.sleep(actual_delay)

                        except Exception as e:
                            self.log_message.emit(f"[ERROR] Error extracting batch: {e}")
                            consecutive_failures += 1

                            # If we've had too many consecutive failures, stop
                            if consecutive_failures >= 3:
                                self.log_message.emit("[ERROR] Multiple extraction failures, stopping extraction")
                                break

                            # Generate a random retry delay between 1-3 seconds
                            retry_delay = random.uniform(1.0, 3.0)
                            self.log_message.emit(f"To avoid account restrictions, please wait for {retry_delay:.1f} seconds.")
                            time.sleep(retry_delay)

                    # Get all collected URLs
                    self.log_message.emit("[INFO] Getting all collected URLs from Facebook Direct Post Extractor")
                    all_urls = driver.execute_script(GET_ALL_URLS_DIRECT)

                    if all_urls and isinstance(all_urls, list):
                        self.log_message.emit(f"[INFO] Successfully extracted {len(all_urls)} post URLs using Facebook Direct Post Extractor")

                        # Convert the URLs to the format expected by the rest of the code
                        converted_posts = []
                        for url in all_urls:
                            # Create a minimal post object with just the URL
                            post_id = ''
                            if '/posts/' in url:
                                post_id = url.split('/posts/')[-1].split('/')[0]
                            elif '/permalink/' in url:
                                post_id = url.split('/permalink/')[-1].split('/')[0]

                            converted_post = {
                                'url': url,
                                'postId': post_id,
                                'author': 'N/A',  # Will be filled in later if needed
                                'authorId': 'N/A',
                                'content': '',
                                'timestamp': '',
                                'images': []
                            }
                            converted_posts.append(converted_post)

                        return converted_posts
                    else:
                        self.log_message.emit("[WARNING] No posts found with Facebook Direct Post Extractor")
                else:
                    self.log_message.emit("[WARNING] Failed to initialize Facebook Direct Post Extractor")
            except Exception as e:
                self.log_message.emit(f"[WARNING] Facebook Direct Post Extractor failed: {e}")
                traceback.print_exc()

            # If the extractor fails, return an empty list
            return []

        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to extract posts using Direct Post Extractor method: {e}")
            traceback.print_exc()
            return []

    def extract_posts_date_click(self, driver, max_scrolls=10, settings=None):
        """Extract posts using the Date Click method.

        This method extracts posts by:
        1. Finding date/time elements in posts
        2. Clicking on them to navigate to the post
        3. Copying the URL from the browser
        4. Navigating back to the group
        5. Repeating for all visible posts

        Args:
            driver: WebDriver instance
            max_scrolls: Maximum number of scrolls to perform
            settings: Optional settings dictionary

        Returns:
            list: List of extracted posts
        """
        try:
            self.log_message.emit("[INFO] Starting post extraction using Date Click method...")

            # Wait for the page to load completely
            time.sleep(5)

            # Try using the Facebook Date Click Extractor
            try:
                from .post_scraper_js import (
                    FACEBOOK_DATE_CLICK_EXTRACTOR_SCRIPT,
                    INIT_DATE_CLICK_EXTRACTOR,
                    EXTRACT_NEXT_BATCH_DATE_CLICK,
                    GET_ALL_URLS_DATE_CLICK
                )

                # Inject the Facebook Date Click Extractor script
                self.log_message.emit("[INFO] Injecting Facebook Date Click Extractor script...")
                driver.execute_script(FACEBOOK_DATE_CLICK_EXTRACTOR_SCRIPT)

                # Wait for the script to be fully loaded and initialized
                self.log_message.emit("[INFO] Waiting for script to initialize...")
                time.sleep(3)  # Wait 3 seconds for the script to initialize

                # Verify that the script was loaded correctly
                script_loaded = driver.execute_script("return typeof window.FacebookDateClickExtractor === 'object'")
                if not script_loaded:
                    self.log_message.emit("[WARNING] Facebook Date Click Extractor script was not loaded correctly")
                    self.log_message.emit("[INFO] Trying to debug script loading...")
                    # Try to debug what happened
                    debug_info = driver.execute_script("""
                    return {
                        'windowKeys': Object.keys(window).filter(k => k.includes('Facebook') || k.includes('fb')),
                        'fbDateClickExtractor': typeof window.fbDateClickExtractor,
                        'FacebookDateClickExtractor': typeof window.FacebookDateClickExtractor
                    }
                    """)
                    self.log_message.emit(f"[INFO] Debug info: {debug_info}")
                    return []

                # Configure the extractor with enhanced settings
                config = {
                    # Timing settings - fixed to 1-3 seconds with random variation
                    "minDelay": 1500,  # 1.5 seconds minimum delay
                    "maxDelay": 3000,  # 3 seconds maximum delay

                    # Extraction limits
                    "maxPosts": settings.get("posts_count", 3000),  # Use posts_count setting or default to 3000

                    # Scrolling behavior
                    "scrollStep": 800,  # Reduced scroll step for more natural scrolling
                    "maxScrolls": max_scrolls * 2,  # Double the max_scrolls for better coverage

                    # Batch size for processing
                    "batchSize": 5,  # Process 5 posts at a time

                    # Headless mode setting
                    "headlessMode": settings.get("headless", False),  # Pass headless mode setting

                    # Enable aggressive mode for better extraction
                    "aggressiveMode": True
                }

                self.log_message.emit(f"[INFO] Initializing Facebook Date Click Extractor with config: {config}")
                init_result = driver.execute_script(INIT_DATE_CLICK_EXTRACTOR, config)

                if init_result:
                    self.log_message.emit("[INFO] Facebook Date Click Extractor initialized successfully")

                    # Extract posts in batches
                    self.log_message.emit("[INFO] Starting post extraction in batches...")

                    # Calculate number of batches based on settings
                    num_batches = settings.get("deep_scroll_frequency", 50)  # Increased default to 50
                    self.log_message.emit(f"[INFO] Will extract up to {num_batches} batches of posts")

                    consecutive_failures = 0
                    no_new_posts_count = 0
                    last_batch_count = 0

                    for batch_num in range(1, num_batches + 1):
                        self.log_message.emit(f"start: fetching posts (batch {batch_num}/{num_batches})")

                        try:
                            # Extract next batch of posts with error handling
                            new_urls = driver.execute_script(EXTRACT_NEXT_BATCH_DATE_CLICK)

                            # Check if we got a valid result
                            if not isinstance(new_urls, list):
                                consecutive_failures += 1
                                self.log_message.emit(f"[WARNING] Failed to extract batch (failure {consecutive_failures}/3)")

                                # If we've had too many consecutive failures, try to recover
                                if consecutive_failures >= 3:
                                    self.log_message.emit("[WARNING] Multiple extraction failures, stopping extraction")
                                    break

                                # Generate a random retry delay between 1-3 seconds
                                retry_delay = random.uniform(1.0, 3.0)
                                self.log_message.emit(f"To avoid account restrictions, please wait for {retry_delay:.1f} seconds.")
                                time.sleep(retry_delay)
                                continue
                            else:
                                # Reset failure counter on success
                                consecutive_failures = 0

                            batch_count = len(new_urls)
                            all_urls = driver.execute_script(GET_ALL_URLS_DATE_CLICK)
                            total_count = len(all_urls) if all_urls else 0

                            # Log found posts with their URLs
                            if batch_count > 0:
                                self.log_message.emit(f"done: found {batch_count} posts in batch {batch_num}")
                                for url in new_urls[:5]:  # Show first 5 URLs
                                    self.log_message.emit(f"[INFO] Found URL: {url}")

                            # Check if we've reached the maximum number of posts
                            max_posts = settings.get("posts_count", 3000)
                            if total_count >= max_posts:
                                self.log_message.emit(f"[INFO] Reached maximum post count ({max_posts}), stopping extraction")
                                break

                            # Check if we found any new posts in this batch
                            if batch_count == 0 or total_count == last_batch_count:
                                no_new_posts_count += 1
                                self.log_message.emit(f"[INFO] No new posts found in batch {batch_num} ({no_new_posts_count}/{5})")

                                # If we've had too many consecutive batches with no new posts, stop
                                if no_new_posts_count >= 5:  # Stop after 5 consecutive empty batches
                                    self.log_message.emit(f"[INFO] No new posts found in {no_new_posts_count} consecutive batches, stopping extraction")
                                    break
                            else:
                                # Reset counter if we found new posts
                                no_new_posts_count = 0

                            last_batch_count = total_count

                            # Add a delay between batches with natural variation
                            actual_delay = random.uniform(1.0, 3.0)
                            self.log_message.emit(f"To avoid account restrictions, please wait for {actual_delay:.1f} seconds.")
                            time.sleep(actual_delay)

                        except Exception as e:
                            self.log_message.emit(f"[ERROR] Error extracting batch: {e}")
                            consecutive_failures += 1

                            # If we've had too many consecutive failures, stop
                            if consecutive_failures >= 3:
                                self.log_message.emit("[ERROR] Multiple extraction failures, stopping extraction")
                                break

                            # Generate a random retry delay between 1-3 seconds
                            retry_delay = random.uniform(1.0, 3.0)
                            self.log_message.emit(f"To avoid account restrictions, please wait for {retry_delay:.1f} seconds.")
                            time.sleep(retry_delay)

                    # Get all collected URLs
                    self.log_message.emit("[INFO] Getting all collected URLs from Facebook Date Click Extractor")
                    all_urls = driver.execute_script(GET_ALL_URLS_DATE_CLICK)

                    if all_urls and isinstance(all_urls, list):
                        self.log_message.emit(f"[INFO] Successfully extracted {len(all_urls)} post URLs using Facebook Date Click Extractor")

                        # Convert the URLs to the format expected by the rest of the code
                        converted_posts = []
                        for url in all_urls:
                            # Create a minimal post object with just the URL
                            post_id = ''
                            if '/posts/' in url:
                                post_id = url.split('/posts/')[-1].split('/')[0]
                            elif '/permalink/' in url:
                                post_id = url.split('/permalink/')[-1].split('/')[0]

                            converted_post = {
                                'url': url,
                                'postId': post_id,
                                'author': 'N/A',  # Will be filled in later if needed
                                'authorId': 'N/A',
                                'content': '',
                                'timestamp': '',
                                'images': []
                            }
                            converted_posts.append(converted_post)

                        return converted_posts
                    else:
                        self.log_message.emit("[WARNING] No posts found with Facebook Date Click Extractor")
                else:
                    self.log_message.emit("[WARNING] Failed to initialize Facebook Date Click Extractor")
            except Exception as e:
                self.log_message.emit(f"[WARNING] Facebook Date Click Extractor failed: {e}")
                traceback.print_exc()

            # If the extractor fails, return an empty list
            return []

        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to extract posts using Date Click method: {e}")
            traceback.print_exc()
            return []


    def _construct_post_url(self, post_id, group_id):
        """Construct a Facebook post URL from post ID and group ID.

        Args:
            post_id: Post ID
            group_id: Group ID

        Returns:
            str: Facebook post URL
        """
        if not post_id or not group_id:
            return None

        return f"https://web.facebook.com/groups/{group_id}/posts/{post_id}"

    def export_to_json(self):
        """Export the extracted posts to a JSON file with all details"""
        if not self.all_extracted_posts:
            self.log_message.emit("[WARNING] No posts to export")
            # Show a message to the user
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(None, "No Posts", "There are no posts to export. Please extract posts first.")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                None, "Save JSON File", "", "JSON Files (*.json)"
            )
            if not file_path:
                return

            # Ensure the file has .json extension
            if not file_path.endswith(".json"):
                file_path += ".json"

            # Create a list of post data with all extracted information
            post_data = []
            for post in self.all_extracted_posts:
                post_entry = {
                    "postId": post.get("postId", ""),
                    "authorId": post.get("authorId", ""),
                    "author": post.get("author", ""),
                    "authorAvatar": post.get("authorAvatar", ""),
                    "content": post.get("content", ""),
                    "timestamp": post.get("timestamp", ""),
                    "url": post.get("url", ""),
                    "commentCount": post.get("commentCount", 0),
                    "reactionCount": post.get("reactionCount", 0),
                    "sharedCount": post.get("sharedCount", 0)
                }
                post_data.append(post_entry)

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(post_data, f, ensure_ascii=False, indent=4)

            self.log_message.emit(f"[INFO] Exported {len(post_data)} posts with full details to {file_path}")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to export posts: {str(e)}")

    def export_to_csv(self):
        """Export the extracted posts to a CSV file with all details"""
        if not self.all_extracted_posts:
            self.log_message.emit("[WARNING] No posts to export")
            # Show a message to the user
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(None, "No Posts", "There are no posts to export. Please extract posts first.")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                None, "Save CSV File", "", "CSV Files (*.csv)"
            )
            if not file_path:
                return

            # Ensure the file has .csv extension
            if not file_path.endswith(".csv"):
                file_path += ".csv"

            # Write to CSV file with all details
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                # Write header with all fields
                f.write("PostID,AuthorID,Author,AuthorAvatar,Content,Timestamp,URL,CommentCount,ReactionCount,SharedCount\n")

                # Write data with all fields
                for post in self.all_extracted_posts:
                    post_id = post.get('postId', '')
                    author_id = post.get('authorId', '')
                    author = post.get('author', '').replace('"', '""')
                    author_avatar = post.get('authorAvatar', '')
                    content = post.get('content', '').replace('"', '""')
                    timestamp = post.get('timestamp', '')
                    url = post.get('url', '')
                    comment_count = str(post.get('commentCount', 0))
                    reaction_count = str(post.get('reactionCount', 0))
                    shared_count = str(post.get('sharedCount', 0))

                    # Write the CSV line with all fields
                    f.write(f"\"{post_id}\",\"{author_id}\",\"{author}\",\"{author_avatar}\",\"{content}\",\"{timestamp}\",\"{url}\",{comment_count},{reaction_count},{shared_count}\n")

            self.log_message.emit(f"[INFO] Exported {len(self.all_extracted_posts)} posts with full details to {file_path}")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to export posts: {str(e)}")

    def export_to_clipboard(self):
        """Export the extracted posts to clipboard in a structured format"""
        if not self.all_extracted_posts:
            self.log_message.emit("[WARNING] No posts to export")
            # Show a message to the user
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(None, "No Posts", "There are no posts to export. Please extract posts first.")
            return

        try:
            import pyperclip

            # Create a formatted text with post details
            formatted_text = "PostID\tAuthorID\tAuthor\tURL\tPosted At\n"

            for post in self.all_extracted_posts:
                post_id = post.get('postId', 'N/A')
                author_id = post.get('authorId', 'N/A')
                author = post.get('author', 'N/A')
                url = post.get('url', 'N/A')
                timestamp = post.get('timestamp', 'N/A')

                # Add line to formatted text
                formatted_text += f"{post_id}\t{author_id}\t{author}\t{url}\t{timestamp}\n"

            # Copy to clipboard
            pyperclip.copy(formatted_text)

            self.log_message.emit(f"[INFO] Exported {len(self.all_extracted_posts)} posts to clipboard in tabular format")
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to export posts to clipboard: {str(e)}")

    def detect_post_selectors(self, profile_id, group_url):
        """Detect post selectors using the provided profile and group URL.

        Args:
            profile_id (str): Profile ID for NstBrowser
            group_url (str): URL of the Facebook group to analyze

        Returns:
            bool: True if new selectors were detected, False otherwise
        """
        try:
            self.log_message.emit(f"[INFO] Starting post selector detection using profile {profile_id}")

            # Get settings from the controller
            settings = self.load_settings()

            # Check if headless mode is enabled
            headless_mode = settings.get("headless_mode", False)
            self.log_message.emit(f"[INFO] Headless mode: {headless_mode}")

            # Update browser_manager config with the current headless setting
            if "nstbrowser" in self.browser_manager.config:
                self.browser_manager.config["nstbrowser"]["headless_mode"] = headless_mode
                self.log_message.emit("[INFO] Updated NSTBrowser config with headless mode setting")

            # Get browser instance
            driver = self.browser_manager.get_browser_for_profile(profile_id)

            if not driver:
                self.log_message.emit("[ERROR] Failed to get browser instance for selector detection")
                return False

            try:
                # Add chronological sorting parameter if not already present
                if "/?sorting_setting=CHRONOLOGICAL" not in group_url and "?sorting_setting=CHRONOLOGICAL" not in group_url:
                    # Check if the URL already has parameters
                    if "?" in group_url:
                        group_url = group_url + "&sorting_setting=CHRONOLOGICAL"
                    else:
                        group_url = group_url + "/?sorting_setting=CHRONOLOGICAL"

                # Navigate to the group URL
                self.log_message.emit(f"[INFO] Navigating to {group_url} to detect selectors...")
                driver.get(group_url)

                # Wait for the page to load
                self.log_message.emit("[INFO] Waiting for page to load...")
                time.sleep(10)  # Wait 10 seconds for the page to load

                # Scroll down a bit to load more content
                self.log_message.emit("[INFO] Scrolling to load more content...")
                for _ in range(3):  # Scroll 3 times
                    driver.execute_script("window.scrollBy(0, 1000)")
                    time.sleep(2)  # Wait 2 seconds between scrolls

                # Detect selectors
                js_detector_code = self.selector_manager.get_js_detector_code()

                # Check that the JavaScript code loaded correctly
                if not js_detector_code:
                    self.log_message.emit("[ERROR] Could not load JavaScript code for post selector detection")
                    return False

                # Execute the script and check that the facebookPostSelectorDetector object was created
                self.log_message.emit("[INFO] Executing post selector detection script...")
                driver.execute_script(js_detector_code)

                # Check that the facebookPostSelectorDetector object exists
                detector_exists = driver.execute_script("return typeof window.facebookPostSelectorDetector !== 'undefined'")

                if not detector_exists:
                    self.log_message.emit("[ERROR] The facebookPostSelectorDetector object was not created correctly")
                    return False

                # Execute selector detection
                self.log_message.emit("[INFO] Analyzing page to detect post selectors...")
                driver.execute_script("window.facebookPostSelectorDetector.generateDynamicSelectors()")
                driver.execute_script("window.facebookPostSelectorDetector.detectAllSelectors()")

                # Test selectors on visible posts
                self.log_message.emit("[INFO] Testing post selectors on visible posts...")
                driver.execute_script("window.facebookPostSelectorDetector.testSelectorsOnAllPosts()")

                # Get the final results
                detected_selectors_json = driver.execute_script("return window.facebookPostSelectorDetector.saveDetectedSelectors()")

                # Process the detected selectors
                updated = self.selector_manager.process_detected_selectors(detected_selectors_json)

                if updated:
                    self.log_message.emit("[SUCCESS] New post selectors detected and saved")
                else:
                    self.log_message.emit("[INFO] No new post selectors detected")

                return updated

            finally:
                # Close the browser
                self.log_message.emit("[INFO] Closing selector detection browser...")
                self.browser_manager.close_browser(driver)

        except Exception as e:
            self.log_message.emit(f"[ERROR] Error detecting post selectors: {e}")
            return False

    def extract_post_links(self, driver, max_scrolls=10):
        """Extract post links from the current page using a simplified approach.

        Args:
            driver: WebDriver instance
            max_scrolls: Maximum number of scrolls to perform

        Returns:
            list: List of post links
        """
        try:
            self.log_message.emit("[INFO] Starting post link extraction with simplified approach...")

            # Wait for the page to load completely
            time.sleep(5)

            # Initialize result list
            all_links = []
            seen_links = set()

            # Define a simple script to extract post links that are currently visible
            extract_visible_links = """
            function extractPostLinks() {
                const links = [];
                const linkElements = document.querySelectorAll('a[href*="/posts/"], a[href*="/permalink/"]');

                for (const link of linkElements) {
                    try {
                        const url = link.href;
                        if (!url) continue;

                        // Clean URL
                        let cleanUrl = url;
                        try {
                            const urlObj = new URL(url);
                            if (urlObj.pathname.includes('/posts/') || urlObj.pathname.includes('/permalink/')) {
                                cleanUrl = urlObj.origin + urlObj.pathname;
                            }
                        } catch (e) {
                            console.error('Error cleaning URL:', e);
                        }

                        links.push(cleanUrl);
                    } catch (e) {
                        console.error('Error extracting link:', e);
                    }
                }

                return links;
            }

            return extractPostLinks();
            """

            # Perform scrolling and extraction in small steps
            for i in range(max_scrolls):
                # Extract currently visible links
                self.log_message.emit(f"[INFO] Extracting links (scroll {i+1}/{max_scrolls})")
                current_links = driver.execute_script(extract_visible_links)

                # Add new links to the result list
                if current_links and isinstance(current_links, list):
                    new_links = 0
                    for link in current_links:
                        if link and link not in seen_links:
                            seen_links.add(link)
                            all_links.append(link)
                            new_links += 1

                    self.log_message.emit(f"[INFO] Found {len(current_links)} links, {new_links} new (total: {len(all_links)})")

                # Stop if we've collected enough links
                if len(all_links) >= max_scrolls * 3:  # Assuming ~3 links per scroll
                    self.log_message.emit(f"[INFO] Collected enough links ({len(all_links)}), stopping scrolling")
                    break

                # Scroll down
                self.log_message.emit(f"[INFO] Scrolling down (scroll {i+1}/{max_scrolls})")
                driver.execute_script("window.scrollBy(0, 1000);")

                # Wait for new content to load
                time.sleep(2)

                # Click any "See More" buttons
                driver.execute_script("""
                const seeMoreButtons = document.querySelectorAll('div[role="button"][tabindex="0"], a[role="button"], span[role="button"]');
                for (const button of seeMoreButtons) {
                    const text = button.textContent.toLowerCase();
                    if (text.includes('see more') || text.includes('show more') || text.includes('view more') || text.includes('see all')) {
                        try {
                            const rect = button.getBoundingClientRect();
                            if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
                                button.click();
                                console.log('Clicked a "See More" button');
                                break;
                            }
                        } catch (e) {
                            console.error('Error clicking button:', e);
                        }
                    }
                }
                """)

            # Final extraction to catch any links that might have loaded after the last scroll
            final_links = driver.execute_script(extract_visible_links)
            if final_links and isinstance(final_links, list):
                new_links = 0
                for link in final_links:
                    if link and link not in seen_links:
                        seen_links.add(link)
                        all_links.append(link)
                        new_links += 1

                self.log_message.emit(f"[INFO] Final extraction found {len(final_links)} links, {new_links} new (total: {len(all_links)})")

            result = all_links
            self.log_message.emit(f"[INFO] Total unique links found: {len(result)}")

            if result and isinstance(result, list):
                # Filter out empty or None values
                valid_links = [link for link in result if link]

                # Log extraction statistics
                self.log_message.emit(f"[INFO] Extraction stats: Found {len(valid_links)} valid post links out of {len(result)} total links")

                # Log the first few links for debugging
                for i, link in enumerate(valid_links[:5]):
                    self.log_message.emit(f"[INFO] Post link {i+1}: {link}")

                if len(valid_links) > 0:
                    self.log_message.emit(f"[INFO] Successfully extracted {len(valid_links)} post links")
                    return valid_links
                else:
                    self.log_message.emit("[WARNING] No valid post links were extracted")
                    return []
            else:
                self.log_message.emit("[WARNING] Invalid result from extraction")
                return []
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to extract post links: {e}")
            return []

    # Old scraper worker classes removed


class NewXHRScraperWorker(QObject):
    """Worker class for the new XHR-based post scraper."""

    # Signals
    log_message = pyqtSignal(str)
    progress_updated = pyqtSignal(int, int, str)
    post_links_updated = pyqtSignal(list)
    scraping_completed = pyqtSignal(dict)
    finished = pyqtSignal()

    def __init__(self, controller, group_links, profile_id, settings):
        """Initialize the worker.

        Args:
            controller (PostScraperController): The controller
            group_links (list): List of group links to scrape
            profile_id (str): Profile ID for NstBrowser
            settings (dict): Scraper settings
        """
        super().__init__()
        self.controller = controller
        self.group_links = group_links
        self.profile_id = profile_id
        self.settings = settings
        self.is_running = True
        self.browser_manager = BrowserManager()

        # Track all extracted posts across groups
        self.all_extracted_posts = []



    def stop(self):
        """Stop the worker."""
        self.is_running = False

    def _inject_js_file(self, driver, file_path):
        """Inject a JavaScript file into the page."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                js_code = f.read()

            # Wrap the script in a try-catch block for better error handling
            wrapped_js = f"""
            try {{
                {js_code}
                console.log('Successfully injected JavaScript file');
                return true;
            }} catch (e) {{
                console.error('Error injecting JavaScript file:', e);
                return false;
            }}
            """

            result = driver.execute_script(wrapped_js)
            if result:
                self.log_message.emit("[INFO] Successfully injected JavaScript file")
            else:
                self.log_message.emit("[WARNING] JavaScript file injection returned false")
            return result
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to inject JavaScript file: {e}")
            return False

    def run(self):
        """Run the worker."""
        try:
            self.log_message.emit("[INFO] Starting XHR post scraper worker")

            # Clear output files
            self.controller.clear_file(self.controller.database_file_path)
            self.controller.clear_file(self.controller.post_links_file_path)

            # Check if we should detect selectors first
            detect_selectors = self.settings.get("detect_selectors", True)
            if detect_selectors and len(self.group_links) > 0:
                self.log_message.emit("[INFO] Will detect post selectors from all group links before starting extraction")

                # Detect selectors from all group links
                for i, group_url in enumerate(self.group_links):
                    self.log_message.emit(f"[INFO] Detecting selectors from group {i+1}/{len(self.group_links)}: {group_url}")
                    success = self.controller.detect_post_selectors(self.profile_id, group_url)

                    if success:
                        self.log_message.emit(f"[SUCCESS] Successfully detected selectors from group: {group_url}")
                    else:
                        self.log_message.emit(f"[INFO] No new selectors detected from group: {group_url}")

            # Get browser instance
            self.log_message.emit(f"[INFO] Getting browser instance for profile {self.profile_id}")

            # Check if headless mode is enabled
            headless_mode = self.settings.get("headless_mode", False)
            self.log_message.emit(f"[INFO] Headless mode: {headless_mode}")

            # Update browser_manager config with the current headless setting
            if "nstbrowser" in self.browser_manager.config:
                self.browser_manager.config["nstbrowser"]["headless_mode"] = headless_mode
                self.log_message.emit("[INFO] Updated NSTBrowser config with headless mode setting")

            driver = self.browser_manager.get_browser_for_profile(self.profile_id)

            if not driver:
                self.log_message.emit("[ERROR] Failed to get browser instance")
                self.scraping_completed.emit({"success": False, "error": "Failed to get browser instance"})
                self.finished.emit()
                return

            # Process each group link
            total_links = len(self.group_links)
            for i, link in enumerate(self.group_links):
                if not self.is_running:
                    self.log_message.emit("[INFO] Scraper stopped by user")
                    break

                self.log_message.emit(f"[INFO] Processing URL {i+1}/{total_links}: {link}")
                self.progress_updated.emit(i+1, total_links, f"Processing URL {i+1}/{total_links}")

                # Add chronological sorting parameter if not already present
                if "/?sorting_setting=CHRONOLOGICAL" not in link and "?sorting_setting=CHRONOLOGICAL" not in link:
                    # Check if the URL already has parameters
                    if "?" in link:
                        link = link + "&sorting_setting=CHRONOLOGICAL"
                    else:
                        link = link + "/?sorting_setting=CHRONOLOGICAL"

                # Navigate to the group link
                driver.get(link)
                self.log_message.emit(f"[INFO] Navigating to {link}")

                # Wait longer for the page to load completely
                time.sleep(10)

                # Log the current URL to verify we're on the right page
                current_url = driver.current_url
                self.log_message.emit(f"[INFO] Current page URL: {current_url}")

                # Determine scroll settings based on fetch settings
                max_scrolls = 10  # Default value

                # Check fetch settings
                if self.settings.get("fetch_all", False):
                    max_scrolls = 50  # More scrolls for "fetch all"
                    self.log_message.emit("[INFO] Fetching ALL available posts (up to 50 scrolls)")
                elif self.settings.get("fetch_by_posts", False):
                    posts_count = self.settings.get("posts_count", 20)
                    # Estimate scrolls based on posts count (assuming ~2 posts per scroll)
                    max_scrolls = max(15, posts_count // 2 + 5)  # At least 15 scrolls
                    self.log_message.emit(f"[INFO] Fetching up to {posts_count} posts (estimated {max_scrolls} scrolls)")
                elif self.settings.get("fetch_by_days", False):
                    days_count = self.settings.get("days_count", 7)
                    # More days = more scrolls
                    max_scrolls = max(20, days_count * 3)  # At least 20 scrolls
                    self.log_message.emit(f"[INFO] Fetching posts from the last {days_count} days (estimated {max_scrolls} scrolls)")

                # Extract posts
                self.log_message.emit("[INFO] Using advanced post link extractor to extract posts")

                # Initialize post_links as an empty list to avoid UnboundLocalError
                post_links = []

                try:
                    # Determine which extraction method to use
                    extraction_method = self.settings.get("extraction_method", "xhr")

                    if extraction_method == "date_click":
                        self.log_message.emit("[INFO] Using Date Click method to extract posts")
                        posts = self.controller.extract_posts_date_click(driver, max_scrolls, self.settings)
                    elif extraction_method == "direct":
                        self.log_message.emit("[INFO] Using Direct Post Extractor method to extract posts")
                        posts = self.controller.extract_posts_direct(driver, max_scrolls, self.settings)
                    else:
                        self.log_message.emit("[INFO] Using XHR method to extract posts")
                        posts = self.controller.extract_posts_xhr(driver, max_scrolls, self.settings)

                    # Filter posts by date if needed
                    if self.settings.get("fetch_by_days", False):
                        days_count = self.settings.get("days_count", 7)
                        self.log_message.emit(f"[INFO] Filtering posts from the last {days_count} days")

                        # Try JavaScript filtering first
                        js_filtered = False
                        try:
                            # Load the filter_posts_by_date.js script
                            filter_script_path = os.path.join(
                                os.path.dirname(os.path.abspath(__file__)),
                                "js", "filter_posts_by_date.js"
                            )
                            self.log_message.emit(f"[INFO] Looking for filter script at: {filter_script_path}")

                            if os.path.exists(filter_script_path):
                                with open(filter_script_path, 'r', encoding='utf-8') as f:
                                    filter_script = f.read()

                                # Execute the filter script
                                original_count = len(posts)
                                filtered_posts = driver.execute_script(
                                    f"{filter_script}; return filterPostsByDate(arguments[0], arguments[1]);",
                                    posts, days_count
                                )

                                self.log_message.emit(f"[INFO] JavaScript filter: from {original_count} to {len(filtered_posts)} posts")
                                posts = filtered_posts
                                js_filtered = True
                            else:
                                self.log_message.emit(f"[WARNING] Filter script not found at: {filter_script_path}")
                        except Exception as e:
                            self.log_message.emit(f"[WARNING] Error with JavaScript filtering: {e}")

                        # If JavaScript filtering failed, use Python fallback
                        if not js_filtered:
                            self.log_message.emit("[INFO] Using Python fallback for date filtering")
                            try:
                                # Calculate cutoff date
                                import datetime
                                import re

                                now = datetime.datetime.now()
                                cutoff_date = now - datetime.timedelta(days=days_count)
                                self.log_message.emit(f"[INFO] Cutoff date: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")

                                # Function to parse Facebook date formats
                                def parse_date(date_string):
                                    if not date_string:
                                        return None

                                    try:
                                        # Try direct parsing first
                                        try:
                                            # If it's a timestamp (seconds or milliseconds)
                                            if isinstance(date_string, (int, float)) or (isinstance(date_string, str) and date_string.isdigit()):
                                                timestamp = int(date_string)
                                                # Convert from seconds to milliseconds if needed
                                                if timestamp < 10000000000:  # If in seconds
                                                    timestamp *= 1000
                                                return datetime.datetime.fromtimestamp(timestamp/1000)

                                            # Try direct parsing
                                            return datetime.datetime.fromisoformat(date_string)
                                        except (ValueError, TypeError):
                                            pass

                                        # Handle "Yesterday at X:XX PM"
                                        if "yesterday" in date_string.lower():
                                            yesterday = now - datetime.timedelta(days=1)
                                            return yesterday

                                        # Handle "X hours ago"
                                        hours_match = re.search(r'(\d+)\s*h(rs|ours)?', date_string, re.IGNORECASE)
                                        if hours_match:
                                            hours_ago = int(hours_match.group(1))
                                            return now - datetime.timedelta(hours=hours_ago)

                                        # Handle "X minutes ago"
                                        mins_match = re.search(r'(\d+)\s*m(in|ins|inutes)?', date_string, re.IGNORECASE)
                                        if mins_match:
                                            mins_ago = int(mins_match.group(1))
                                            return now - datetime.timedelta(minutes=mins_ago)

                                        # Handle "Just now"
                                        if "just now" in date_string.lower():
                                            return now

                                        # Default: assume it's recent
                                        return now
                                    except Exception as e:
                                        self.log_message.emit(f"[WARNING] Error parsing date '{date_string}': {e}")
                                        return now  # Default to current time

                                # Filter posts by date
                                original_count = len(posts)
                                filtered_posts = []
                                for post in posts:
                                    # Keep posts without timestamp
                                    if not post.get('timestamp'):
                                        filtered_posts.append(post)
                                        continue

                                    # Parse the post date
                                    post_date = parse_date(post.get('timestamp'))
                                    if not post_date:
                                        filtered_posts.append(post)  # Keep posts with unparseable dates
                                        continue

                                    # Keep posts newer than cutoff date
                                    if post_date >= cutoff_date:
                                        filtered_posts.append(post)

                                self.log_message.emit(f"[INFO] Python filter: from {original_count} to {len(filtered_posts)} posts")
                                posts = filtered_posts
                            except Exception as e:
                                self.log_message.emit(f"[WARNING] Error with Python date filtering: {e}")
                                # Continue with unfiltered posts

                    if posts:
                        self.log_message.emit(f"[INFO] XHR extraction found {len(posts)} posts")
                    else:
                        # If no posts found, try a more aggressive approach
                        self.log_message.emit(f"[INFO] No posts found with standard extraction, trying aggressive approach...")

                        # Inject and execute aggressive extraction script
                        aggressive_script = r"""
                        function findAllPostLinks() {
                            console.log('Starting aggressive post link extraction...');
                            const allLinks = [];
                            const seenUrls = new Set();

                            // Función para limpiar URLs
                            function cleanUrl(url) {
                                if (!url) return '';
                                try {
                                    // Eliminar parámetros de consulta
                                    if (url.includes('?')) {
                                        url = url.split('?')[0];
                                    }

                                    // Eliminar barra final
                                    if (url.endsWith('/')) {
                                        url = url.slice(0, -1);
                                    }

                                    return url;
                                } catch (e) {
                                    console.error('Error cleaning URL:', e);
                                    return url;
                                }
                            }

                            // Función para verificar si una URL es un enlace de publicación válido
                            function isValidPostLink(url) {
                                if (!url) return false;

                                // Verificar si es un enlace de publicación
                                const isPostLink = (
                                    url.includes('/posts/') ||
                                    url.includes('/permalink/') ||
                                    url.includes('story_fbid=') ||
                                    (url.includes('/groups/') && url.includes('/permalink/'))
                                );

                                if (!isPostLink) return false;

                                // Excluir enlaces de medios
                                const isMediaLink = (
                                    url.includes('/photo.php') ||
                                    url.includes('/photo/?fbid=') ||
                                    url.includes('/video.php') ||
                                    url.includes('/watch/') ||
                                    url.includes('/reel/') ||
                                    url.includes('set=')
                                );

                                return isPostLink && !isMediaLink;
                            }

                            // Estrategia 1: Buscar todos los enlaces en la página
                            function findLinksInPage() {
                                const links = document.querySelectorAll('a[href]');
                                console.log(`Found ${links.length} total links on page`);

                                let count = 0;
                                for (const link of links) {
                                    const href = link.href;
                                    if (!href) continue;

                                    if (isValidPostLink(href)) {
                                        const clean = cleanUrl(href);
                                        if (!seenUrls.has(clean)) {
                                            seenUrls.add(clean);
                                            allLinks.push({
                                                url: clean,
                                                postId: clean.split('/').pop(),
                                                timestamp: new Date().toISOString()
                                            });
                                            count++;
                                        }
                                    }
                                }
                                console.log(`Found ${count} post links with standard approach`);
                            }

                            // Estrategia 2: Buscar elementos de artículo y extraer enlaces
                            function findPostsInArticles() {
                                const articles = document.querySelectorAll('[role="article"]');
                                console.log(`Found ${articles.length} article elements`);

                                let count = 0;
                                for (const article of articles) {
                                    // Buscar enlaces dentro del artículo
                                    const articleLinks = article.querySelectorAll('a[href]');

                                    // Buscar enlaces de tiempo/fecha que suelen apuntar a la publicación
                                    const timeLinks = article.querySelectorAll('a[href] > abbr[data-utime], abbr[data-utime]');
                                    if (timeLinks.length > 0) {
                                        for (const timeLink of timeLinks) {
                                            let parent = timeLink.parentElement;
                                            while (parent && parent !== article) {
                                                if (parent.tagName === 'A' && parent.href) {
                                                    const href = parent.href;
                                                    if (isValidPostLink(href)) {
                                                        const clean = cleanUrl(href);
                                                        if (!seenUrls.has(clean)) {
                                                            seenUrls.add(clean);
                                                            allLinks.push({
                                                                url: clean,
                                                                postId: clean.split('/').pop(),
                                                                timestamp: timeLink.getAttribute('data-utime') || new Date().toISOString()
                                                            });
                                                            count++;
                                                        }
                                                    }
                                                    break;
                                                }
                                                parent = parent.parentElement;
                                            }
                                        }
                                    }

                                    // Buscar todos los enlaces en el artículo
                                    for (const link of articleLinks) {
                                        const href = link.href;
                                        if (isValidPostLink(href)) {
                                            const clean = cleanUrl(href);
                                            if (!seenUrls.has(clean)) {
                                                seenUrls.add(clean);
                                                allLinks.push({
                                                    url: clean,
                                                    postId: clean.split('/').pop(),
                                                    timestamp: new Date().toISOString()
                                                });
                                                count++;
                                            }
                                        }
                                    }
                                }
                                console.log(`Found ${count} additional post links from articles`);
                            }

                            // Estrategia 3: Buscar elementos con clases específicas de Facebook
                            function findPostsInSpecificElements() {
                                // Clases comunes de Facebook para contenedores de publicaciones
                                const selectors = [
                                    '.x1yztbdb', '.x78zum5', '.x1n2onr6', '.xdj266r',
                                    '.x1i10hfl', '.x1qjc9v5', '.x9f619', '.x1ja2u2z',
                                    '.x1y1aw1k', '.x1sxyh0', '.xurb0ha'
                                ];

                                let count = 0;
                                for (const selector of selectors) {
                                    const elements = document.querySelectorAll(selector);
                                    for (const element of elements) {
                                        const links = element.querySelectorAll('a[href]');
                                        for (const link of links) {
                                            const href = link.href;
                                            if (isValidPostLink(href)) {
                                                const clean = cleanUrl(href);
                                                if (!seenUrls.has(clean)) {
                                                    seenUrls.add(clean);
                                                    allLinks.push({
                                                        url: clean,
                                                        postId: clean.split('/').pop(),
                                                        timestamp: new Date().toISOString()
                                                    });
                                                    count++;
                                                }
                                            }
                                        }
                                    }
                                }
                                console.log(`Found ${count} additional post links from specific elements`);
                            }

                            // Ejecutar todas las estrategias
                            findLinksInPage();
                            findPostsInArticles();
                            findPostsInSpecificElements();

                            console.log(`Found ${allLinks.length} total unique post links with aggressive approach`);
                            return allLinks;
                        }
                        return findAllPostLinks();
                        """

                        aggressive_posts = driver.execute_script(aggressive_script)
                        if aggressive_posts and len(aggressive_posts) > 0:
                            self.log_message.emit(f"[INFO] Aggressive extraction found {len(aggressive_posts)} posts")
                            posts = aggressive_posts
                        else:
                            self.log_message.emit(f"[WARNING] No posts found with aggressive extraction either")
                            posts = []

                        # Filter posts by date is now handled earlier in the code

                        # Extract post links with improved filtering
                        post_links = []
                        for post in posts:
                            url = post.get('url')
                            if not url:
                                continue

                            # Filter out photo and video links
                            if ('/photo.php' in url or '/photo/?fbid=' in url or
                                '/video.php' in url or '/watch/' in url or
                                '/reel/' in url or 'set=' in url):
                                self.log_message.emit(f"[INFO] Filtering out media URL: {url}")
                                continue

                            # Ensure it's a post URL
                            if not ('/posts/' in url or '/permalink/' in url or 'story_fbid=' in url):
                                self.log_message.emit(f"[INFO] Filtering out non-post URL: {url}")
                                continue

                            # Clean URL by removing tracking parameters
                            if '?' in url and '__cft__' in url:
                                clean_url = url.split('?')[0]
                                self.log_message.emit(f"[INFO] Cleaned URL: {url} -> {clean_url}")
                                url = clean_url

                            # Add to post links
                            post_links.append(url)

                        # If using posts count limit, trim the results
                        if self.settings.get("fetch_by_posts", False) and post_links:
                            posts_count = self.settings.get("posts_count", 20)
                            if len(post_links) > posts_count:
                                post_links = post_links[:posts_count]
                                posts = posts[:posts_count]
                                self.log_message.emit(f"[INFO] Trimmed results to {posts_count} posts as requested")

                        # Log extracted data in a structured format
                        self.log_message.emit("[INFO] Extracted post data in structured format:")
                        self.log_message.emit("PostID\tAuthorId\tAuthor\tUrl\tPosted At")
                        for post in posts[:5]:  # Show first 5 posts as example
                            post_id = post.get('postId', 'N/A')
                            author_id = post.get('authorId', 'N/A')
                            author = post.get('author', 'N/A')
                            url = post.get('url', 'N/A')
                            timestamp = post.get('timestamp', 'N/A')
                            self.log_message.emit(f"{post_id}\t{author_id}\t{author}\t{url}\t{timestamp}")
                except Exception as e:
                    self.log_message.emit(f"[ERROR] Error during XHR post extraction: {e}")
                    posts = []

                # Create post_links from posts
                post_links = [post.get('url', '') for post in posts if post.get('url')]
                self.log_message.emit(f"[INFO] Final post count: {len(post_links)} unique posts")

                # Extract the group name from the URL for logging
                group_name = link
                try:
                    # Try to extract group name from URL
                    import re
                    match = re.search(r'/groups/([^/?]+)', link)
                    if match:
                        group_name = match.group(1)
                except:
                    pass

                # Log the results
                self.log_message.emit(f"[INFO] Successfully extracted {len(post_links)} post links from {group_name}")

                # Process posts to remove duplicates
                unique_posts = []
                seen_urls = set()

                for post in posts:
                    url = post.get('url', '')

                    # Normalize URL
                    if url:
                        # Remove query parameters
                        if '?' in url:
                            url = url.split('?')[0]

                        # Remove trailing slash
                        if url.endswith('/'):
                            url = url[:-1]

                        # Update the URL in the post object
                        post['url'] = url

                    # Add to unique posts if not already seen
                    if url and url not in seen_urls:
                        seen_urls.add(url)
                        unique_posts.append(post)

                # Log the deduplication results
                if len(posts) != len(unique_posts):
                    self.log_message.emit(f"[INFO] Removed {len(posts) - len(unique_posts)} duplicate posts")

                # Store the extracted posts for this group
                self.all_extracted_posts.extend(unique_posts)

                # Update post_links to match unique_posts (only if we have unique posts)
                if unique_posts:
                    post_links = [post.get('url', '') for post in unique_posts if post.get('url')]

                # Add to post links
                if self.settings.get("auto_add_to_new_post_links", True):
                    self.log_message.emit(f"[INFO] Adding {len(post_links)} post links to New Post Links")
                    # Make sure to emit the signal from the main thread
                    self.post_links_updated.emit(post_links.copy())

                # Save post links based on export format
                if self.settings.get("export_json", False):
                    try:
                        # Save to JSON file with all details
                        json_file_path = os.path.join(self.controller.output_dir, f"{group_name}_posts.json")

                        # Create a list of post data with all extracted information
                        detailed_posts = []
                        for post in unique_posts:
                            post_entry = {
                                "postId": post.get("postId", ""),
                                "authorId": post.get("authorId", ""),
                                "author": post.get("author", ""),
                                "authorAvatar": post.get("authorAvatar", ""),
                                "content": post.get("content", ""),
                                "timestamp": post.get("timestamp", ""),
                                "url": post.get("url", ""),
                                "commentCount": post.get("commentCount", 0),
                                "reactionCount": post.get("reactionCount", 0),
                                "sharedCount": post.get("sharedCount", 0)
                            }
                            detailed_posts.append(post_entry)

                        with open(json_file_path, 'w', encoding='utf-8') as f:
                            json.dump(detailed_posts, f, ensure_ascii=False, indent=4)

                        self.log_message.emit(f"[INFO] Saved {len(detailed_posts)} posts to JSON file: {json_file_path}")
                    except Exception as e:
                        self.log_message.emit(f"[ERROR] Failed to save as JSON: {e}")

                elif self.settings.get("export_csv", False):
                    try:
                        # Save to CSV file with all details
                        csv_file_path = os.path.join(self.controller.output_dir, f"{group_name}_posts.csv")
                        with open(csv_file_path, 'w', encoding='utf-8', newline='') as f:
                            # Write header with all fields
                            f.write("PostID,AuthorID,Author,AuthorAvatar,Content,Timestamp,URL,CommentCount,ReactionCount,SharedCount\n")

                            # Write data with all fields
                            for post in unique_posts:
                                post_id = post.get('postId', '')
                                author_id = post.get('authorId', '')
                                author = post.get('author', '').replace('"', '""')
                                author_avatar = post.get('authorAvatar', '')
                                content = post.get('content', '').replace('"', '""')
                                timestamp = post.get('timestamp', '')
                                url = post.get('url', '')
                                comment_count = str(post.get('commentCount', 0))
                                reaction_count = str(post.get('reactionCount', 0))
                                shared_count = str(post.get('sharedCount', 0))

                                # Write the CSV line with all fields
                                f.write(f"\"{post_id}\",\"{author_id}\",\"{author}\",\"{author_avatar}\",\"{content}\",\"{timestamp}\",\"{url}\",{comment_count},{reaction_count},{shared_count}\n")

                        self.log_message.emit(f"[INFO] Saved {len(unique_posts)} posts to CSV file: {csv_file_path}")
                    except Exception as e:
                        self.log_message.emit(f"[ERROR] Failed to save as CSV: {e}")

                elif self.settings.get("export_clipboard", False):
                    try:
                        # Copy to clipboard
                        import pyperclip
                        clipboard_text = "\n".join(post_links)
                        pyperclip.copy(clipboard_text)
                        self.log_message.emit(f"[INFO] Copied {len(post_links)} post links to clipboard")
                    except Exception as e:
                        self.log_message.emit(f"[ERROR] Failed to copy to clipboard: {e}")

                # Always save to the default file as well
                with open(self.controller.post_links_file_path, "a", encoding="utf-8") as f:
                    for link in post_links:
                        f.write(f"{link}\n")

            # Complete the scraping process
            # Calculate total unique posts across all groups
            all_posts = self.all_extracted_posts
            total_posts = len(all_posts)

            # Close the browser
            try:
                self.log_message.emit("[INFO] Closing browser...")
                driver.quit()
                # Also kill any remaining NstChrome processes to ensure complete cleanup
                self.browser_manager.kill_nstchrome_processes()
                self.log_message.emit("[INFO] Browser closed successfully")
            except Exception as e:
                self.log_message.emit(f"[WARNING] Error closing browser: {e}")
                # Try to force kill browser processes
                try:
                    self.browser_manager.kill_nstchrome_processes()
                    self.log_message.emit("[INFO] Killed NstChrome processes as fallback")
                except Exception as e2:
                    self.log_message.emit(f"[WARNING] Failed to kill browser processes: {e2}")

            # Update the UI with the results
            if total_posts == 0:
                self.log_message.emit("[WARNING] No posts were extracted")
                self.scraping_completed.emit({"success": False, "post_count": 0, "error": "No posts were extracted"})
            else:
                self.log_message.emit(f"[INFO] Extraction completed. Total posts extracted: {total_posts}")
                self.log_message.emit(f"[INFO] Scraping completed successfully with {total_posts} posts extracted")
                # Update controller's all_extracted_posts with our posts
                self.controller.all_extracted_posts = self.all_extracted_posts.copy()
                self.log_message.emit(f"[INFO] Updated controller with {len(self.all_extracted_posts)} posts")
                self.scraping_completed.emit({"success": True, "post_count": total_posts, "posts": all_posts})

        except Exception as e:
            error_details = traceback.format_exc()
            self.log_message.emit(f"[ERROR] Error in XHR scraper worker: {e}")
            self.log_message.emit(f"[DEBUG] Error details: {error_details}")
            self.scraping_completed.emit({"success": False, "error": str(e)})

        finally:
            self.is_running = False
            self.finished.emit()

    def stop(self, close_browser=False):
        """Stop the worker and optionally close the browser.

        Args:
            close_browser (bool): Whether to close the browser immediately
        """
        self.is_running = False

        # Close the browser if requested
        if close_browser:
            try:
                self.log_message.emit("[INFO] Closing browser immediately...")
                # Get the current driver instance if available
                driver = self.browser_manager.get_current_driver()
                if driver:
                    driver.quit()
                    self.log_message.emit("[INFO] Browser closed successfully")

                # Also kill any remaining NstChrome processes to ensure complete cleanup
                self.browser_manager.kill_nstchrome_processes()
                self.log_message.emit("[INFO] All browser processes terminated")
            except Exception as e:
                self.log_message.emit(f"[ERROR] Failed to close browser: {e}")
                # Try to force kill browser processes
                try:
                    self.browser_manager.kill_nstchrome_processes()
                    self.log_message.emit("[INFO] Killed NstChrome processes as fallback")
                except Exception as e2:
                    self.log_message.emit(f"[WARNING] Failed to kill browser processes: {e2}")