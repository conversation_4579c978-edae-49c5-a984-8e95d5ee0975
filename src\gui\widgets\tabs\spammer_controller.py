import os
import time
import logging
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QThread

from core.spammer import Spammer
from core.utils.database import Database
from core.selector_manager import SelectorManager

# Setup logger
logger = logging.getLogger('zpammer.gui.spammer_controller')

class SpammerWorker(QThread):
    """Worker thread for running the spammer in the background."""

    # Define signals
    progress_updated = pyqtSignal(int, int, int, int, str)
    log_message = pyqtSignal(str)
    finished_successfully = pyqtSignal()

    def __init__(self, spammer, profiles, posts, comments, settings):
        super().__init__()
        self.spammer = spammer
        self.profiles = profiles
        self.posts = posts
        self.comments = comments
        self.settings = settings
        self.selector_manager = SelectorManager()

    def run(self):
        """Run the spammer process."""
        self.log_message.emit("[INFO] Starting spammer process")

        # Verificar si debemos detectar selectores antes de comenzar
        detect_selectors = self.settings.get("detect_selectors", True)

        # Define callback function for progress updates
        def progress_callback(profile_index, total_profiles, comments_posted, total_comments_posted, status):
            progress = int((profile_index / total_profiles) * 100)
            self.progress_updated.emit(
                profile_index,
                total_profiles,
                comments_posted,
                total_comments_posted,
                status
            )

        # Apply settings to spammer
        # Configure spammer with settings
        self.spammer.settings = self.settings

        # Detectar selectores antes de comenzar si está habilitado
        if detect_selectors:
            # Obtener el primer perfil y post para detectar selectores
            if self.profiles and self.posts:
                first_profile = self.profiles[0]
                first_post = self.posts[0]

                self.log_message.emit(f"[INFO] Detecting selectors using profile {first_profile}...")

                # Abrir el navegador con el primer perfil
                driver = self.spammer.browser_manager.get_browser(first_profile)

                if driver:
                    try:
                        # Navegar al primer post
                        self.log_message.emit(f"[INFO] Navigating to {first_post} to detect selectors...")
                        driver.get(first_post)

                        # Esperar a que la página cargue
                        self.log_message.emit("[INFO] Waiting for page to load...")
                        time.sleep(5)  # Esperar 5 segundos para que la página cargue

                        # Detectar selectores
                        js_detector_code = self.selector_manager.get_js_detector_code()

                        # Verificar que el código JavaScript se cargó correctamente
                        if not js_detector_code:
                            self.log_message.emit("[ERROR] Could not load JavaScript code for selector detection")
                            raise Exception("Could not load JavaScript code for selector detection")

                        # Ejecutar el script y verificar que se creó el objeto facebookSelectorDetector
                        self.log_message.emit("[INFO] Executing selector detection script...")
                        driver.execute_script(js_detector_code)

                        # Verificar que el objeto facebookSelectorDetector existe
                        detector_exists = driver.execute_script("return typeof window.facebookSelectorDetector !== 'undefined'")

                        if not detector_exists:
                            self.log_message.emit("[ERROR] The facebookSelectorDetector object was not created correctly")
                            raise Exception("The facebookSelectorDetector object was not created correctly")

                        # Ejecutar la detección de selectores
                        self.log_message.emit("[INFO] Analyzing page to detect selectors...")
                        driver.execute_script("window.facebookSelectorDetector.generateDynamicSelectors()")
                        driver.execute_script("window.facebookSelectorDetector.detectAllSelectors()")

                        # Probar los selectores en los posts visibles
                        self.log_message.emit("[INFO] Testing selectors on visible posts...")
                        driver.execute_script("window.facebookSelectorDetector.testSelectorsOnAllPosts()")

                        # Obtener los resultados finales
                        detected_selectors_json = driver.execute_script("return window.facebookSelectorDetector.saveDetectedSelectors()")

                        # Procesar los selectores detectados
                        updated = self.selector_manager.process_detected_selectors(detected_selectors_json)

                        if updated:
                            self.log_message.emit("[SUCCESS] New selectors detected and saved")
                        else:
                            self.log_message.emit("[INFO] No new selectors detected")

                    except Exception as e:
                        self.log_message.emit(f"[ERROR] Error detecting selectors: {e}")
                    finally:
                        # Cerrar el navegador
                        self.log_message.emit("[INFO] Closing selector detection browser...")
                        self.spammer.browser_manager.close_browser(first_profile)

        # Start spammer with updated settings
        success = self.spammer.start(
            profiles=self.profiles,
            posts=self.posts,
            comments=self.comments,
            comments_per_profile=self.settings.get("spam_count", 3),
            callback=progress_callback
        )

        if success:
            self.log_message.emit("[SUCCESS] Spammer process completed successfully")
            self.finished_successfully.emit()
        else:
            self.log_message.emit("[ERROR] Spammer process failed")

    def stop(self):
        """Stop the spammer process."""
        if self.spammer:
            self.spammer.stop()
            self.log_message.emit("[WARNING] Stopping spammer process")


class SpammerController(QObject):
    """
    Controller for the Spammer tab.

    This class handles the interaction between the UI and the Spammer core.
    """

    # Define signals
    progress_updated = pyqtSignal(int, int, int, int, str)
    log_message = pyqtSignal(str)
    status_changed = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.spammer = Spammer()

        # Connect spammer signals
        self.connect_spammer_signals()

        # Create database instance
        self.db = Database()

        # Create selector manager instance
        self.selector_manager = SelectorManager()

        # Initialize worker
        self.worker = None

    def connect_spammer_signals(self):
        """Connect signals from the spammer to the controller."""
        # Make sure the spammer has signals attribute
        if hasattr(self.spammer, 'signals'):
            # Forward signals to the UI
            self.spammer.signals.comment_ready.connect(self.on_comment_ready)
            self.spammer.signals.comment_posted.connect(self.on_comment_posted)
            self.spammer.signals.comment_failed.connect(self.on_comment_failed)

    def on_comment_ready(self, comment_text, post_url):
        """Forward comment ready signal."""
        # This will be connected to the UI
        pass

    def on_comment_posted(self, comment_text, post_url):
        """Handle comment posted signal."""
        self.log_message.emit(f"[SUCCESS] Comment posted successfully")

    def on_comment_failed(self, comment_text, post_url):
        """Handle comment failed signal."""
        self.log_message.emit(f"[WARNING] Comment posting failed")

    def load_profiles(self, file_path=None):
        """
        Load profile IDs from a file or database.

        Args:
            file_path (str, optional): Path to the profiles file.

        Returns:
            list: List of profile IDs.
        """
        if file_path:
            # Import from file to database
            count = self.db.import_profiles_from_file(file_path)
            self.log_message.emit(f"[INFO] Imported {count} profiles from file to database")

        # Get profiles from database
        profiles_data = self.db.get_profiles()
        profiles = [p['profile_id'] for p in profiles_data]

        self.log_message.emit(f"[INFO] Loaded {len(profiles)} profiles from database")
        return profiles

    def load_posts(self, file_path=None):
        """
        Load post links from a file or database.

        Args:
            file_path (str, optional): Path to the posts file.

        Returns:
            list: List of post links.
        """
        if file_path:
            # Import from file to database
            count = self.db.import_posts_from_file(file_path)
            self.log_message.emit(f"[INFO] Imported {count} posts from file to database")

        # Get posts from database
        posts_data = self.db.get_posts()
        posts = [p['post_url'] for p in posts_data]

        self.log_message.emit(f"[INFO] Loaded {len(posts)} posts from database")
        return posts

    def load_comments(self, file_path=None):
        """
        Load comments from a file or database.

        Args:
            file_path (str, optional): Path to the comments file.

        Returns:
            list: List of comments.
        """
        if file_path:
            # Import from file to database
            count = self.db.import_comments_from_file(file_path)
            self.log_message.emit(f"[INFO] Imported {count} comments from file to database")

        # Get comments from database
        comments_data = self.db.get_comments()
        comments = [c['content'] for c in comments_data]

        self.log_message.emit(f"[INFO] Loaded {len(comments)} comments from database")
        return comments

    def save_profiles(self, profiles, file_path=None):
        """
        Save profile IDs to the database and optionally to a file.

        Args:
            profiles (list): List of profile IDs.
            file_path (str, optional): Path to the profiles file.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Save to database
            count = 0
            for profile_id in profiles:
                if self.db.save_profile(profile_id):
                    count += 1

            self.log_message.emit(f"[INFO] Saved {count} profiles to database")

            # Optionally save to file
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(profiles))
                self.log_message.emit(f"[INFO] Saved {len(profiles)} profiles to {file_path}")

            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save profiles: {e}")
            return False

    def save_posts(self, posts, file_path=None):
        """
        Save post links to the database and optionally to a file.

        Args:
            posts (list): List of post links.
            file_path (str, optional): Path to the posts file.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Save to database
            count = 0
            for post_url in posts:
                if self.db.save_post(post_url):
                    count += 1

            self.log_message.emit(f"[INFO] Saved {count} posts to database")

            # Optionally save to file
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(posts))
                self.log_message.emit(f"[INFO] Saved {len(posts)} posts to {file_path}")

            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save posts: {e}")
            return False

    def save_comments(self, comments, file_path=None):
        """
        Save comments to the database and optionally to a file.

        Args:
            comments (list): List of comments.
            file_path (str, optional): Path to the comments file.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Save to database
            count = 0
            for comment in comments:
                # Generate a unique comment ID
                comment_id = f"comment_{hash(comment)}"
                if self.db.save_comment(comment_id, comment):
                    count += 1

            self.log_message.emit(f"[INFO] Saved {count} comments to database")

            # Optionally save to file
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(comments))
                self.log_message.emit(f"[INFO] Saved {len(comments)} comments to {file_path}")

            return True
        except Exception as e:
            self.log_message.emit(f"[ERROR] Failed to save comments: {e}")
            return False

    def detect_selectors(self, driver):
        """
        Automatically detects selectors for Facebook elements.

        Args:
            driver: WebDriver instance.

        Returns:
            bool: True if new selectors were detected, False otherwise.
        """
        try:
            self.log_message.emit("[INFO] Starting automatic selector detection...")

            # Cargar el script de detección de selectores
            js_detector_code = self.selector_manager.get_js_detector_code()

            # Verificar que el código JavaScript se cargó correctamente
            if not js_detector_code:
                self.log_message.emit("[ERROR] Could not load JavaScript code for selector detection")
                return False

            # Ejecutar el script y verificar que se creó el objeto facebookSelectorDetector
            self.log_message.emit("[INFO] Executing selector detection script...")
            driver.execute_script(js_detector_code)

            # Verificar que el objeto facebookSelectorDetector existe
            detector_exists = driver.execute_script("return typeof window.facebookSelectorDetector !== 'undefined'")

            if not detector_exists:
                self.log_message.emit("[ERROR] The facebookSelectorDetector object was not created correctly")
                return False

            # Ejecutar la detección de selectores
            self.log_message.emit("[INFO] Analyzing page to detect selectors...")
            driver.execute_script("window.facebookSelectorDetector.generateDynamicSelectors()")
            driver.execute_script("window.facebookSelectorDetector.detectAllSelectors()")

            # Probar los selectores en los posts visibles
            self.log_message.emit("[INFO] Testing selectors on visible posts...")
            driver.execute_script("window.facebookSelectorDetector.testSelectorsOnAllPosts()")

            # Obtener los resultados finales
            detected_selectors_json = driver.execute_script("return window.facebookSelectorDetector.saveDetectedSelectors()")

            # Procesar los selectores detectados
            updated = self.selector_manager.process_detected_selectors(detected_selectors_json)

            if updated:
                self.log_message.emit("[SUCCESS] New selectors detected and saved")
            else:
                self.log_message.emit("[INFO] No new selectors detected")

            return updated
        except Exception as e:
            self.log_message.emit(f"[ERROR] Error detecting selectors: {e}")
            return False

    @pyqtSlot(list, list, list, dict)
    def start(self, profiles, posts, comments, settings):
        """
        Start the spammer process.

        Args:
            profiles (list): List of profile IDs.
            posts (list): List of post links.
            comments (list): List of comments.
            settings (dict): Spammer settings.

        Returns:
            bool: True if started successfully, False otherwise.
        """
        if self.worker is not None and self.worker.isRunning():
            self.log_message.emit("[WARNING] Spammer is already running")
            return False

        # Validate inputs
        if not profiles:
            self.log_message.emit("[ERROR] No profiles loaded")
            return False

        if not posts:
            self.log_message.emit("[ERROR] No posts loaded")
            return False

        if not comments:
            self.log_message.emit("[ERROR] No comments loaded")
            return False

        # Agregar configuración para detectar selectores antes de comenzar
        settings["detect_selectors"] = True

        # Create and start worker thread
        self.worker = SpammerWorker(self.spammer, profiles, posts, comments, settings)

        # Connect signals
        self.worker.progress_updated.connect(self.on_progress_updated)
        self.worker.log_message.connect(self.log_message)
        self.worker.finished_successfully.connect(self.on_finished_successfully)

        # Start worker
        self.worker.start()

        self.status_changed.emit("Running")
        self.log_message.emit("[INFO] Spammer started")
        return True

    @pyqtSlot()
    def stop(self):
        """
        Stop the spammer process.

        Returns:
            bool: True if stopped successfully, False otherwise.
        """
        if self.worker is None or not self.worker.isRunning():
            self.log_message.emit("[WARNING] Spammer is not running")
            return False

        self.worker.stop()
        self.status_changed.emit("Stopping")
        return True

    @pyqtSlot()
    def skip_profile(self):
        """
        Skip the current profile.

        Returns:
            bool: True if skipped successfully, False otherwise.
        """
        if self.spammer:
            success = self.spammer.skip_profile()
            if success:
                self.log_message.emit("[INFO] Skipping current profile")
            return success
        return False

    @pyqtSlot(int, int, int, int, str)
    def on_progress_updated(self, profile_index, total_profiles, comments_posted, total_comments_posted, status):
        """
        Handle progress update from worker.

        Args:
            profile_index (int): Current profile index.
            total_profiles (int): Total number of profiles.
            comments_posted (int): Number of comments posted for current profile.
            total_comments_posted (int): Total number of comments posted.
            status (str): Current status message.
        """
        # Forward the signal
        self.progress_updated.emit(
            profile_index,
            total_profiles,
            comments_posted,
            total_comments_posted,
            status
        )

    @pyqtSlot()
    def on_finished_successfully(self):
        """Handle worker finished successfully signal."""
        self.status_changed.emit("Completed")

    def close(self):
        """
        Close all resources.

        Returns:
            bool: True if closed successfully, False otherwise.
        """
        if self.worker and self.worker.isRunning():
            self.stop()
            self.worker.wait()

        if self.spammer:
            self.spammer.close()

        self.log_message.emit("[INFO] Spammer controller closed")
        return True
