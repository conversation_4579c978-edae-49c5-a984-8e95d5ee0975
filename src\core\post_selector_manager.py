"""
Post Selector Manager

This module manages the detection and updating of selectors for Facebook posts.
It allows automatic detection of selectors when Facebook changes its HTML structure.
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class PostSelectorManager:
    """Manages the detection and updating of selectors for Facebook posts."""

    def __init__(self, selectors_file_path=None):
        """
        Initialize the post selector manager.

        Args:
            selectors_file_path (str, optional): Path to the selectors file.
                If not provided, the default path will be used.
        """
        # Determine the path to the selectors file
        if selectors_file_path is None:
            # Use the default path
            base_dir = Path(__file__).resolve().parent.parent.parent
            self.selectors_file_path = os.path.join(base_dir, "Files", "facebook_post_selectors.json")
        else:
            self.selectors_file_path = selectors_file_path

        # Load existing selectors or create new ones
        self.selectors = self._load_selectors()

        # Path to the JavaScript selector detector script
        self.js_detector_path = os.path.join(
            base_dir, "src", "gui", "widgets", "tabs", "js", "facebook_post_selector_detector.js"
        )

        logger.info(f"Post Selector Manager initialized with file: {self.selectors_file_path}")

    def _load_selectors(self):
        """
        Load selectors from the JSON file.

        Returns:
            dict: Dictionary with the loaded selectors.
        """
        try:
            if os.path.exists(self.selectors_file_path):
                with open(self.selectors_file_path, 'r', encoding='utf-8') as f:
                    selectors = json.load(f)
                logger.info(f"Loaded post selectors from {self.selectors_file_path}")
                return selectors
            else:
                logger.warning(f"Post selectors file not found at {self.selectors_file_path}, creating default")
                return self._create_default_selectors()
        except Exception as e:
            logger.error(f"Error loading post selectors: {e}")
            return self._create_default_selectors()

    def _create_default_selectors(self):
        """
        Create a dictionary of default selectors.

        Returns:
            dict: Dictionary with default selectors.
        """
        default_selectors = {
            "timestamp": datetime.now().isoformat(),
            "version": "1.0",
            "selectors": {
                "post": [
                    'div[role="article"]',
                    'div.x1yztbdb',
                    'div.x1lliihq'
                ],
                "author": [
                    'a[role="link"][tabindex="0"]',
                    'a.x1i10hfl',
                    'h3.x1heor9g a',
                    'span.x193iq5w a'
                ],
                "content": [
                    'div[data-ad-preview="message"]',
                    'div.xdj266r',
                    'div.x11i5rnm',
                    'div.x1iorvi4'
                ],
                "time": [
                    'a[href*="/posts/"] > abbr',
                    'a.x1i10hfl.xjbqb8w',
                    'span.x4k7w5x a',
                    'span.x1yrsyyn a'
                ],
                "reactions": [
                    'span.x193iq5w',
                    'div.x1n2onr6.x1vqgdyp',
                    'div.x78zum5 span',
                    'div.x1i10hfl span'
                ],
                "media": [
                    'img.x1ey2m1c.xds687c.x5yr21d',
                    'img.x1bwycvy',
                    'video.x1lliihq',
                    'div.x1qjc9v5 img'
                ],
                "postLink": [
                    'a[href*="/posts/"]',
                    'a[href*="/permalink/"]',
                    'a[href*="/groups/"][href*="/permalink/"]',
                    'a[href*="/groups/"][href*="/posts/"]',
                    'span.x4k7w5x a[href*="/posts/"]',
                    'span.x1yrsyyn a[href*="/posts/"]'
                ]
            },
            "stats": {}
        }

        # Save the default selectors
        self._save_selectors(default_selectors)

        return default_selectors

    def _save_selectors(self, selectors):
        """
        Save selectors to the JSON file.

        Args:
            selectors (dict): Dictionary with selectors to save.
        """
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.selectors_file_path), exist_ok=True)

            with open(self.selectors_file_path, 'w', encoding='utf-8') as f:
                json.dump(selectors, f, indent=2)

            logger.info(f"Saved post selectors to {self.selectors_file_path}")
        except Exception as e:
            logger.error(f"Error saving post selectors: {e}")

    def get_selectors(self, selector_type=None):
        """
        Get selectors.

        Args:
            selector_type (str, optional): Type of selector to get.
                If not provided, all selectors will be returned.

        Returns:
            dict or list: Dictionary with all selectors or list with selectors of the specified type.
        """
        if selector_type is None:
            return self.selectors

        return self.selectors.get("selectors", {}).get(selector_type, [])

    def update_selectors(self, new_selectors):
        """
        Update selectors with new values.

        Args:
            new_selectors (dict): Dictionary with the new selectors.
        """
        try:
            # Update timestamp
            new_selectors["timestamp"] = datetime.now().isoformat()

            # Save the new selectors
            self._save_selectors(new_selectors)

            # Update selectors in memory
            self.selectors = new_selectors

            logger.info("Post selectors updated successfully")
        except Exception as e:
            logger.error(f"Error updating post selectors: {e}")

    def add_selector(self, selector_type, selector):
        """
        Add a new selector to the list.

        Args:
            selector_type (str): Type of selector (post, author, content, etc.).
            selector (str): CSS selector to add.
        """
        try:
            if selector_type not in self.selectors.get("selectors", {}):
                self.selectors["selectors"][selector_type] = []

            if selector not in self.selectors["selectors"][selector_type]:
                self.selectors["selectors"][selector_type].append(selector)
                self._save_selectors(self.selectors)
                logger.info(f"Added new post selector '{selector}' to {selector_type}")
        except Exception as e:
            logger.error(f"Error adding post selector: {e}")

    def get_js_detector_code(self):
        """
        Get the JavaScript code for selector detection.

        Returns:
            str: JavaScript code for selector detection.
        """
        try:
            with open(self.js_detector_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error loading JS post detector code: {e}")
            return ""

    def process_detected_selectors(self, detected_selectors_json):
        """
        Process selectors detected by the JavaScript script.

        Args:
            detected_selectors_json (str): JSON with the detected selectors.

        Returns:
            bool: True if selectors were updated, False otherwise.
        """
        try:
            detected_selectors = json.loads(detected_selectors_json)

            # Check if there are new selectors
            updated = False

            for selector_type, selectors in detected_selectors.get("selectors", {}).items():
                for selector in selectors:
                    if selector_type not in self.selectors.get("selectors", {}):
                        self.selectors["selectors"][selector_type] = []

                    if selector not in self.selectors["selectors"][selector_type]:
                        self.selectors["selectors"][selector_type].append(selector)
                        updated = True

            if updated:
                # Update timestamp
                self.selectors["timestamp"] = datetime.now().isoformat()

                # Save the updated selectors
                self._save_selectors(self.selectors)

                logger.info("Post selectors updated with newly detected selectors")

            return updated
        except Exception as e:
            logger.error(f"Error processing detected post selectors: {e}")
            return False

    def get_selectors_as_js_object(self):
        """
        Get selectors as a JavaScript object string.

        Returns:
            str: JavaScript object string with selectors.
        """
        try:
            selectors_dict = self.selectors.get("selectors", {})
            js_object = "{\n"
            
            for selector_type, selectors in selectors_dict.items():
                js_object += f"    {selector_type}: [\n"
                for selector in selectors:
                    js_object += f"        '{selector}',\n"
                js_object += "    ],\n"
            
            js_object += "}"
            
            return js_object
        except Exception as e:
            logger.error(f"Error converting selectors to JS object: {e}")
            return "{}"

    def detect_selectors(self, driver):
        """
        Detect selectors using the provided WebDriver.

        Args:
            driver: WebDriver instance.

        Returns:
            bool: True if new selectors were detected, False otherwise.
        """
        try:
            logger.info("Starting automatic post selector detection...")

            # Load the selector detection script
            js_detector_code = self.get_js_detector_code()

            # Check that the JavaScript code loaded correctly
            if not js_detector_code:
                logger.error("Could not load JavaScript code for post selector detection")
                return False

            # Execute the script and check that the facebookPostSelectorDetector object was created
            logger.info("Executing post selector detection script...")
            driver.execute_script(js_detector_code)

            # Check that the facebookPostSelectorDetector object exists
            detector_exists = driver.execute_script("return typeof window.facebookPostSelectorDetector !== 'undefined'")

            if not detector_exists:
                logger.error("The facebookPostSelectorDetector object was not created correctly")
                return False

            # Execute selector detection
            logger.info("Analyzing page to detect post selectors...")
            driver.execute_script("window.facebookPostSelectorDetector.generateDynamicSelectors()")
            driver.execute_script("window.facebookPostSelectorDetector.detectAllSelectors()")

            # Test selectors on visible posts
            logger.info("Testing post selectors on visible posts...")
            driver.execute_script("window.facebookPostSelectorDetector.testSelectorsOnAllPosts()")

            # Get the final results
            detected_selectors_json = driver.execute_script("return window.facebookPostSelectorDetector.saveDetectedSelectors()")

            # Process the detected selectors
            updated = self.process_detected_selectors(detected_selectors_json)

            if updated:
                logger.info("New post selectors detected and saved")
            else:
                logger.info("No new post selectors detected")

            return updated
        except Exception as e:
            logger.error(f"Error detecting post selectors: {e}")
            return False
