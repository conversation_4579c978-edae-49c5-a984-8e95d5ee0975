/**
 * Facebook XHR Scraper
 * 
 * This script extracts Facebook posts and comments by intercepting XHR requests.
 * It captures GraphQL responses to extract detailed post and comment data.
 */

function initFacebookXHRScraper() {
    'use strict';
    
    // Store extracted posts
    const extractedPosts = [];
    const seenPostIds = new Set();
    
    // Configuration
    const config = {
        scrollDelay: 1000,
        scrollStep: 800,
        maxScrolls: 10,
        waitAfterClick: 1000
    };
    
    // Function to intercept XHR requests
    function interceptXHRRequests() {
        console.log('Setting up XHR interception...');
        
        // Save the original open method
        const originalOpen = XMLHttpRequest.prototype.open;
        
        // Override the open method
        XMLHttpRequest.prototype.open = function(method, url, async) {
            // Only intercept GraphQL requests
            if (url.includes('graphql')) {
                // Save the original send method
                const originalSend = this.send;
                
                // Override the send method to capture request body
                this.send = function(body) {
                    // Add load event listener to capture response
                    this.addEventListener('load', function() {
                        try {
                            // Process the response if it's a valid JSON
                            if (this.responseText && this.responseText.trim()) {
                                processXHRResponse(this.responseText, body);
                            }
                        } catch (error) {
                            console.error('Error processing XHR response:', error);
                        }
                    });
                    
                    // Call the original send method
                    return originalSend.apply(this, arguments);
                };
            }
            
            // Call the original open method
            return originalOpen.apply(this, arguments);
        };
        
        console.log('XHR interception set up successfully');
    }
    
    // Function to process XHR response
    function processXHRResponse(responseText, requestBody) {
        try {
            // Check if the response contains multiple JSON objects (separated by newlines)
            const jsonLines = responseText.split('\n').filter(line => line.trim());
            
            if (jsonLines.length > 1) {
                // Process each JSON object
                jsonLines.forEach(line => {
                    try {
                        const json = JSON.parse(line);
                        extractPostsFromJSON(json);
                    } catch (e) {
                        console.error('Error parsing JSON line:', e);
                    }
                });
            } else {
                // Process single JSON object
                const json = JSON.parse(responseText);
                extractPostsFromJSON(json);
            }
        } catch (error) {
            console.error('Error processing response:', error);
        }
    }
    
    // Function to extract posts from JSON
    function extractPostsFromJSON(json) {
        // Extract posts from GroupsCometFeedRegularStoriesPaginationQuery
        if (json?.data?.node?.group_feed?.edges) {
            const edges = json.data.node.group_feed.edges;
            
            edges.forEach(edge => {
                try {
                    const node = edge.node;
                    
                    // Extract post ID
                    const postId = node?.comet_sections?.feedback?.story?.post_id;
                    if (!postId || seenPostIds.has(postId)) return;
                    
                    // Extract post author
                    const actor = node?.comet_sections?.content?.story?.comet_sections?.context_layout?.story?.comet_sections?.actor_photo?.story?.actors?.[0];
                    const authorName = actor?.name;
                    const authorId = actor?.id;
                    const authorUrl = actor?.url;
                    
                    // Extract post content
                    const postText = node?.comet_sections?.content?.story?.comet_sections?.message_container?.story?.message?.text || '';
                    
                    // Extract post timestamp
                    const timestamp = node?.comet_sections?.content?.story?.comet_sections?.context_layout?.story?.comet_sections?.metadata?.[0]?.story?.creation_time;
                    
                    // Extract post URL
                    let postUrl = '';
                    const feedbackSection = node?.comet_sections?.feedback;
                    if (feedbackSection) {
                        const permalinkUrl = feedbackSection?.story?.url;
                        if (permalinkUrl) {
                            try {
                                const url = new URL(permalinkUrl);
                                if (url.pathname.includes('/posts/') || url.pathname.includes('/permalink/')) {
                                    postUrl = `${url.origin}${url.pathname}`;
                                } else if (url.searchParams.has('story_fbid')) {
                                    const storyFbid = url.searchParams.get('story_fbid');
                                    const id = url.searchParams.get('id');
                                    if (storyFbid && id) {
                                        postUrl = `${url.origin}/groups/${id}/posts/${storyFbid}`;
                                    }
                                }
                            } catch (e) {
                                console.error('Error parsing post URL:', e);
                            }
                        }
                    }
                    
                    // If we couldn't extract a URL, construct one from the post ID
                    if (!postUrl && postId) {
                        // Try to extract group ID from the current URL
                        const currentUrl = window.location.href;
                        const groupIdMatch = currentUrl.match(/groups\/([^\/\?]+)/);
                        if (groupIdMatch && groupIdMatch[1]) {
                            const groupId = groupIdMatch[1];
                            postUrl = `https://web.facebook.com/groups/${groupId}/posts/${postId}`;
                        }
                    }
                    
                    // Only add the post if we have a URL
                    if (postUrl) {
                        seenPostIds.add(postId);
                        extractedPosts.push({
                            id: postId,
                            url: postUrl,
                            author: authorName || '',
                            authorId: authorId || '',
                            authorUrl: authorUrl || '',
                            content: postText || '',
                            timestamp: timestamp || ''
                        });
                        
                        console.log(`Extracted post: ${postUrl}`);
                    }
                } catch (e) {
                    console.error('Error extracting post data:', e);
                }
            });
        }
        
        // Extract posts from other GraphQL queries
        // ... add more extraction logic for different query types if needed
    }
    
    // Function to scroll down
    async function scrollDown(iterations = 1) {
        return new Promise(resolve => {
            let scrolled = 0;
            
            const scrollInterval = setInterval(() => {
                window.scrollBy(0, config.scrollStep);
                scrolled++;
                
                if (scrolled >= iterations) {
                    clearInterval(scrollInterval);
                    setTimeout(resolve, 1000);
                }
            }, config.scrollDelay);
        });
    }
    
    // Function to click "See More" buttons
    function clickSeeMoreButtons() {
        const seeMoreButtons = document.querySelectorAll('div[role="button"][tabindex="0"], a[role="button"], span[role="button"]');
        
        for (const button of seeMoreButtons) {
            const text = button.textContent.toLowerCase();
            if (text.includes('see more') || text.includes('show more') || text.includes('view more') || text.includes('see all')) {
                try {
                    const rect = button.getBoundingClientRect();
                    if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
                        button.click();
                        console.log('Clicked a "See More" button');
                        break;
                    }
                } catch (e) {
                    console.error('Error clicking button:', e);
                }
            }
        }
    }
    
    // Function to click on comments to load them
    function clickOnComments() {
        const posts = document.querySelectorAll('div[role="article"]');
        
        for (const post of posts) {
            try {
                // Find comment buttons
                const commentButtons = post.querySelectorAll('div[role="button"]');
                
                for (const button of commentButtons) {
                    const text = button.textContent.toLowerCase();
                    if (text.includes('comment') || text.includes('reply')) {
                        try {
                            const rect = button.getBoundingClientRect();
                            if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
                                button.click();
                                console.log('Clicked a comment button');
                                break;
                            }
                        } catch (e) {
                            console.error('Error clicking comment button:', e);
                        }
                    }
                }
            } catch (e) {
                console.error('Error processing post for comments:', e);
            }
        }
    }
    
    // Main function to scrape posts
    async function scrapePosts(maxScrolls = config.maxScrolls) {
        console.log(`Starting to scrape posts with up to ${maxScrolls} scrolls...`);
        
        // Set up XHR interception
        interceptXHRRequests();
        
        // Initial extraction of visible posts
        console.log('Extracting initially visible posts...');
        
        // Perform scrolling and extraction
        for (let i = 0; i < maxScrolls; i++) {
            console.log(`Scroll ${i+1}/${maxScrolls}`);
            
            // Click "See More" buttons
            clickSeeMoreButtons();
            
            // Wait a bit
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Click on comments to load them
            clickOnComments();
            
            // Wait for comments to load
            await new Promise(resolve => setTimeout(resolve, config.waitAfterClick));
            
            // Scroll down
            await scrollDown(1);
            
            // Log current progress
            console.log(`Extracted ${extractedPosts.length} posts so far`);
            
            // If we've collected enough posts, stop scrolling
            if (extractedPosts.length >= maxScrolls * 3) {
                console.log(`Collected enough posts (${extractedPosts.length}), stopping scrolling`);
                break;
            }
        }
        
        console.log(`Scraping completed. Extracted ${extractedPosts.length} posts.`);
        return extractedPosts;
    }
    
    // Return the public API
    return {
        scrapePosts,
        getExtractedPosts: () => extractedPosts,
        config
    };
}

// Export the function for use in Selenium
if (typeof module !== 'undefined') {
    module.exports = initFacebookXHRScraper;
}
