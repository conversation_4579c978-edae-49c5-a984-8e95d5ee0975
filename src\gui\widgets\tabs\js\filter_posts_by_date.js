/**
 * Filter posts by date
 * @param {Array} posts - Array of post objects
 * @param {number} daysToLookBack - Number of days to look back
 * @returns {Array} Filtered array of posts
 */
function filterPostsByDate(posts, daysToLookBack) {
    console.log(`Filtering ${posts.length} posts for the last ${daysToLookBack} days`);

    // Calculate cutoff date
    const now = new Date();
    const cutoffDate = new Date(now);
    cutoffDate.setDate(cutoffDate.getDate() - daysToLookBack);
    console.log(`Cutoff date: ${cutoffDate.toISOString()}`);

    // Function to parse Facebook date formats
    function parseDate(dateString) {
        if (!dateString) return null;

        try {
            // If it's a Unix timestamp (seconds)
            if (typeof dateString === 'number' || (typeof dateString === 'string' && /^\d+$/.test(dateString))) {
                const timestamp = parseInt(dateString, 10);
                // Convert from seconds to milliseconds if needed
                const milliseconds = timestamp < 10000000000 ? timestamp * 1000 : timestamp;
                const date = new Date(milliseconds);
                console.log(`Parsed timestamp ${dateString} as ${date.toISOString()}`);
                return date;
            }

            // Handle different Facebook date formats

            // Format: "Monday, April 10, 2025 at 7:30 PM"
            const fullDateMatch = dateString.match(/([A-Za-z]+, [A-Za-z]+ \d+, \d{4} at \d+:\d+ [AP]M)/);
            if (fullDateMatch) {
                const date = new Date(fullDateMatch[1]);
                console.log(`Parsed full date ${dateString} as ${date.toISOString()}`);
                return date;
            }

            // Format: "April 10 at 7:30 PM"
            const monthDayMatch = dateString.match(/([A-Za-z]+ \d+) at (\d+:\d+ [AP]M)/);
            if (monthDayMatch) {
                const currentYear = now.getFullYear();
                const date = new Date(`${monthDayMatch[1]}, ${currentYear} ${monthDayMatch[2]}`);
                // If the date is in the future, it's probably from last year
                if (date > now) {
                    date.setFullYear(currentYear - 1);
                }
                console.log(`Parsed month/day ${dateString} as ${date.toISOString()}`);
                return date;
            }

            // Format: "Yesterday at 7:30 PM"
            if (dateString.toLowerCase().includes("yesterday")) {
                const yesterday = new Date(now);
                yesterday.setDate(yesterday.getDate() - 1);
                const timeMatch = dateString.match(/(\d+:\d+ [AP]M)/);
                if (timeMatch) {
                    const timeStr = timeMatch[1];
                    const [hourMin, ampm] = timeStr.split(' ');
                    const [hour, min] = hourMin.split(':').map(Number);
                    yesterday.setHours(ampm === 'PM' && hour < 12 ? hour + 12 : hour);
                    yesterday.setMinutes(min);
                }
                console.log(`Parsed 'yesterday' ${dateString} as ${yesterday.toISOString()}`);
                return yesterday;
            }

            // Format: "Today at 7:30 PM"
            if (dateString.toLowerCase().includes("today")) {
                const today = new Date(now);
                const timeMatch = dateString.match(/(\d+:\d+ [AP]M)/);
                if (timeMatch) {
                    const timeStr = timeMatch[1];
                    const [hourMin, ampm] = timeStr.split(' ');
                    const [hour, min] = hourMin.split(':').map(Number);
                    today.setHours(ampm === 'PM' && hour < 12 ? hour + 12 : hour);
                    today.setMinutes(min);
                }
                console.log(`Parsed 'today' ${dateString} as ${today.toISOString()}`);
                return today;
            }

            // Format: "7 hrs" or "7 hours ago"
            const hoursMatch = dateString.match(/(\d+)\s*h(rs|ours|r)?\b/i);
            if (hoursMatch) {
                const hoursAgo = parseInt(hoursMatch[1], 10);
                const date = new Date(now);
                date.setHours(date.getHours() - hoursAgo);
                console.log(`Parsed '${hoursAgo} hours ago' ${dateString} as ${date.toISOString()}`);
                return date;
            }

            // Format: "30 mins" or "30 minutes ago"
            const minsMatch = dateString.match(/(\d+)\s*m(in|ins|inutes)?\b/i);
            if (minsMatch) {
                const minsAgo = parseInt(minsMatch[1], 10);
                const date = new Date(now);
                date.setMinutes(date.getMinutes() - minsAgo);
                console.log(`Parsed '${minsAgo} minutes ago' ${dateString} as ${date.toISOString()}`);
                return date;
            }

            // Format: "Just now"
            if (dateString.toLowerCase().includes("just now")) {
                console.log(`Parsed 'just now' ${dateString} as ${now.toISOString()}`);
                return new Date(now);
            }

            // Try to parse as ISO format
            if (dateString.includes('T') && dateString.includes('Z')) {
                try {
                    const date = new Date(dateString);
                    if (!isNaN(date.getTime())) {
                        console.log(`Parsed ISO date ${dateString} as ${date.toISOString()}`);
                        return date;
                    }
                } catch (e) {
                    console.log(`Failed to parse as ISO date: ${dateString}`);
                }
            }

            // If all else fails, try to parse the string directly
            try {
                const date = new Date(dateString);
                if (!isNaN(date.getTime())) {
                    console.log(`Parsed date string ${dateString} as ${date.toISOString()}`);
                    return date;
                }
            } catch (e) {
                console.log(`Failed to parse date string: ${dateString}`);
            }

            console.log(`Could not parse date: ${dateString}, assuming recent`);
            return now; // Default to current time if we can't parse
        } catch (e) {
            console.log(`Error parsing date "${dateString}": ${e.message}`);
            return now; // Default to current time on error
        }
    }

    // Filter posts by date
    const filteredPosts = posts.filter(post => {
        // Debug the post timestamp
        console.log(`Post timestamp: ${JSON.stringify(post.timestamp)}`);

        // Keep posts without timestamp
        if (!post.timestamp) {
            console.log('No timestamp, keeping post');
            return true;
        }

        // Parse the post date
        const postDate = parseDate(post.timestamp);
        if (!postDate) {
            console.log('Failed to parse date, keeping post');
            return true; // Keep posts with unparseable dates
        }

        // Check if post is newer than cutoff date
        const isRecent = postDate >= cutoffDate;
        console.log(`Post date: ${postDate.toISOString()}, isRecent: ${isRecent}`);
        return isRecent;
    });

    console.log(`Filtered from ${posts.length} to ${filteredPosts.length} posts`);
    return filteredPosts;
}

// Execute the function with parameters
return filterPostsByDate(arguments[0], arguments[1]);
