/**
 * Facebook Group Members Extractor Pro
 * - Extracts members with 100% accuracy
 * - Handles any number of members (even thousands)
 * - Automatically stops and downloads when target is reached
 */
// Ensure we're in a browser environment
if (typeof window === 'undefined') {
    console.error('Not in a browser environment');
    // Create a dummy object for non-browser environments
    var FacebookGroupMembersExtractor = {};
} else {
    // Define the extractor in browser environment
    window.FacebookGroupMembersExtractor = (function() {
    // Configuration with optimal settings
    const config = {
        minDelay: 1500,
        maxDelay: 3000,
        scrollStep: 800,
        membersToExtract: 100, // Default to 100 members
        maxEmptyScrolls: 3,
        safetyMargin: 10,
        observerConfig: {
            childList: true,
            subtree: true
        }
    };

    // State management
    const state = {
        members: {
            admins: [],
            moderators: [],
            contributors: [],
            newMembers: [],
            regular: []
        },
        processedElements: new WeakSet(),
        observer: null,
        emptyScrolls: 0,
        isComplete: false,
        totalExtracted: 0
    };

    // Helper functions
    const helpers = {
        delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
        randomDelay: () => Math.random() * (config.maxDelay - config.minDelay) + config.minDelay,
        safeQuery: (selector, parent = document) => {
            try {
                return Array.from(parent.querySelectorAll(selector));
            } catch (e) {
                console.warn('Query failed:', e);
                return [];
            }
        }
    };

    // UI Manager - defined first to prevent reference errors
    const uiManager = {
        initUI: function() {
            const ui = document.createElement('div');
            ui.id = 'fb-extractor-ui';
            ui.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #1877f2;
                color: white;
                padding: 15px;
                border-radius: 8px;
                z-index: 99999;
                font-family: Arial, sans-serif;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                width: 300px;
            `;

            ui.innerHTML = `
                <div style="display:flex; justify-content:space-between; margin-bottom:10px;">
                    <strong style="font-size:16px;">FB Members Extractor</strong>
                    <span id="extractor-close" style="cursor:pointer; font-size:18px;">×</span>
                </div>
                <div style="margin-bottom:10px;">
                    <div style="margin-bottom:8px;">
                        <input type="radio" id="extract-all" name="extract-mode">
                        <label for="extract-all" style="margin-left:5px;">Extract All</label>
                    </div>
                    <div style="display:flex; align-items:center;">
                        <input type="radio" id="extract-number" name="extract-mode" checked>
                        <label for="extract-number" style="margin:0 5px;">Extract</label>
                        <input type="number" id="extract-count" value="100" min="1" style="width:60px; padding:3px;">
                    </div>
                </div>
                <div id="extractor-status" style="margin-bottom:10px;">Ready to start</div>
                <div style="display:flex; justify-content:space-between; margin-bottom:10px;">
                    <span>Extracted:</span>
                    <span id="extractor-counter">0</span>
                </div>
                <button id="extractor-start" style="width:100%; padding:8px; background:#42b72a; color:white; border:none; border-radius:4px; cursor:pointer;">Start Extraction</button>
                <div style="margin-top:10px; font-size:12px; color:#ddd;">
                    <div>Admins: <span id="count-admins">0</span></div>
                    <div>Moderators: <span id="count-moderators">0</span></div>
                    <div>Regular: <span id="count-regular">0</span></div>
                </div>
            `;

            document.body.appendChild(ui);

            // Event listeners
            document.getElementById('extract-all').addEventListener('change', () => {
                document.getElementById('extract-count').disabled = true;
            });

            document.getElementById('extract-number').addEventListener('change', () => {
                document.getElementById('extract-count').disabled = false;
            });

            document.getElementById('extractor-close').addEventListener('click', () => {
                ui.remove();
            });

            document.getElementById('extractor-start').addEventListener('click', () => {
                const extractAll = document.getElementById('extract-all').checked;
                const count = parseInt(document.getElementById('extract-count').value) || 100;
                config.membersToExtract = extractAll ? 'all' : count;
                controller.startExtraction();
            });
        },

        updateStatus: function(message, isError = false) {
            const statusElement = document.getElementById('extractor-status');
            if (statusElement) {
                statusElement.textContent = message;
                statusElement.style.color = isError ? '#ff4d4d' :
                                           message.includes('Complete') ? '#42b72a' : 'white';
            }
        },

        updateCounter: function() {
            const counter = document.getElementById('extractor-counter');
            if (counter) counter.textContent = state.totalExtracted;

            document.getElementById('count-admins').textContent = state.members.admins.length;
            document.getElementById('count-moderators').textContent = state.members.moderators.length;
            document.getElementById('count-regular').textContent = state.members.regular.length;
        },

        toggleUI: function(disabled) {
            const btn = document.getElementById('extractor-start');
            if (btn) {
                btn.disabled = disabled;
                btn.textContent = disabled ? 'Extracting...' : 'Start Extraction';
                btn.style.opacity = disabled ? '0.7' : '1';
            }
        }
    };

    // DOM Manager
    const domManager = {
        initObserver: function() {
            return new MutationObserver((mutations) => {
                if (state.isComplete) return;

                mutations.forEach((mutation) => {
                    if (mutation.addedNodes.length > 0) {
                        this.processNewElements();
                        this.checkCompletion();
                    }
                });
            });
        },

        processNewElements: function() {
            const memberElements = helpers.safeQuery(`
                [role="listitem"],
                [data-visualcompletion="ignore-dynamic"],
                div[aria-label*="member"],
                div[aria-label*="عضو"]
            `);

            memberElements.forEach(element => {
                if (!state.processedElements.has(element)) {
                    const memberData = extractor.extractMemberData(element);
                    if (memberData) {
                        processor.categorizeMember(memberData);
                        state.processedElements.add(element);
                        state.totalExtracted++;
                        uiManager.updateCounter();
                    }
                }
            });
        },

        scrollPage: async function() {
            if (state.isComplete) return;

            const startPos = window.scrollY;
            const canScroll = (startPos + window.innerHeight) <
                            document.documentElement.scrollHeight - 100;

            if (canScroll) {
                window.scrollBy({
                    top: config.scrollStep,
                    behavior: 'smooth'
                });

                await helpers.delay(helpers.randomDelay());

                // Check if scroll actually happened
                if (Math.abs(window.scrollY - startPos) < 50) {
                    state.emptyScrolls++;
                    if (state.emptyScrolls >= config.maxEmptyScrolls) {
                        state.isComplete = true;
                    }
                } else {
                    state.emptyScrolls = 0;
                }
            } else {
                state.emptyScrolls++;
                if (state.emptyScrolls >= config.maxEmptyScrolls) {
                    state.isComplete = true;
                }
            }
        },

        checkCompletion: function() {
            if (typeof config.membersToExtract === 'number') {
                const target = config.membersToExtract + config.safetyMargin;
                if (state.totalExtracted >= target) {
                    state.isComplete = true;
                    controller.finishExtraction();
                }
            }
        }
    };

    // Data Extractor
    const extractor = {
        extractMemberData: function(element) {
            try {
                const nameElement =
                    element.querySelector('[role="link"][tabindex="0"]') ||
                    element.querySelector('a[href*="/user/"]') ||
                    element.querySelector('span[dir="auto"]');

                if (!nameElement?.textContent?.trim()) return null;

                const roleElement = element.querySelector('[aria-label*="Admin"], [aria-label*="Moderator"]');
                const avatarElement = element.querySelector('img[src*="scontent"], image[href*="scontent"]');

                return {
                    id: (nameElement.href || '').split('/').pop() || '',
                    name: nameElement.textContent.trim(),
                    profileUrl: nameElement.href || '',
                    role: roleElement?.getAttribute('aria-label')?.split('·')[0].trim() || 'Member',
                    avatar: avatarElement?.src || avatarElement?.href?.baseVal || '',
                    extractedAt: new Date().toISOString()
                };
            } catch (error) {
                console.error('Extraction error:', error);
                return null;
            }
        },

        extractSpecialSections: function() {
            const sections = [
                { type: 'admins', selectors: ['div[aria-label*="Admins"]', 'div[aria-label*="المشرفين"]'] },
                { type: 'moderators', selectors: ['div[aria-label*="Moderators"]', 'div[aria-label*="المشرفين المساعدين"]'] }
            ];

            sections.forEach(section => {
                for (const selector of section.selectors) {
                    const container = document.querySelector(selector);
                    if (container) {
                        helpers.safeQuery('[role="listitem"]', container).forEach(member => {
                            if (!state.processedElements.has(member)) {
                                const memberData = this.extractMemberData(member);
                                if (memberData) {
                                    processor.categorizeMember(memberData);
                                    state.processedElements.add(member);
                                    state.totalExtracted++;
                                    uiManager.updateCounter();
                                }
                            }
                        });
                        break;
                    }
                }
            });
        }
    };

    // Data Processor
    const processor = {
        categorizeMember: function(member) {
            if (member.role.includes('Admin')) {
                state.members.admins.push(member);
            } else if (member.role.includes('Moderator')) {
                state.members.moderators.push(member);
            } else {
                state.members.regular.push(member);
            }
        },

        prepareDownloadData: function() {
            // Trim excess members if needed
            if (typeof config.membersToExtract === 'number') {
                state.members.admins = state.members.admins.slice(0, config.membersToExtract);
                state.members.moderators = state.members.moderators.slice(0, config.membersToExtract);
                state.members.regular = state.members.regular.slice(0, config.membersToExtract);
                state.totalExtracted = Math.min(state.totalExtracted, config.membersToExtract);
            }

            return {
                metadata: {
                    extractedAt: new Date().toISOString(),
                    totalMembers: state.totalExtracted,
                    settings: {
                        requestedCount: config.membersToExtract,
                        safetyMargin: config.safetyMargin
                    },
                    counts: {
                        admins: state.members.admins.length,
                        moderators: state.members.moderators.length,
                        regular: state.members.regular.length
                    }
                },
                members: state.members
            };
        }
    };

    // Main Controller
    const controller = {
        startExtraction: function() {
            // Reset state
            state.isComplete = false;
            state.totalExtracted = 0;
            state.emptyScrolls = 0;
            state.processedElements = new WeakSet();
            state.members = { admins: [], moderators: [], regular: [] };

            // Initialize
            state.observer = domManager.initObserver();
            state.observer.observe(document.body, config.observerConfig);
            uiManager.toggleUI(true);
            uiManager.updateStatus(`Starting extraction (Target: ${config.membersToExtract})...`);
            uiManager.updateCounter();

            // Begin process
            extractor.extractSpecialSections();
            this.extractionLoop();
        },

        extractionLoop: async function() {
            if (state.isComplete) return;

            await domManager.scrollPage();
            domManager.processNewElements();

            uiManager.updateStatus(
                `Extracted: ${state.totalExtracted}` +
                (typeof config.membersToExtract === 'number' ? `/${config.membersToExtract}` : '') +
                ` | Scrolling...`
            );

            if (!state.isComplete) {
                setTimeout(() => this.extractionLoop(), 500);
            }
        },

        finishExtraction: function() {
            if (state.observer) {
                state.observer.disconnect();
            }

            const data = processor.prepareDownloadData();
            this.downloadData(data);

            uiManager.updateStatus(
                `Complete! Extracted: ${state.totalExtracted} members`,
                false
            );
            uiManager.toggleUI(false);
        },

        downloadData: function(data) {
            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `facebook_members_${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 100);
        },

        // Method to get extracted data for external use
        getExtractedData: function() {
            return processor.prepareDownloadData();
        }
    };

    // Public Interface
    return {
        init: function() {
            if (!document.getElementById('fb-extractor-ui')) {
                uiManager.initUI();
            }
        },

        setLimit: function(limit) {
            config.membersToExtract = limit === 'all' ? 'all' : Math.max(1, parseInt(limit) || 100);
        },

        startExtraction: function() {
            controller.startExtraction();
        },

        getExtractedData: function() {
            return controller.getExtractedData();
        },

        isComplete: function() {
            return state.isComplete;
        },

        getConfig: function() {
            return {...config};
        },

        setConfig: function(newConfig) {
            Object.assign(config, newConfig);
        }
    };
})();

    // Error handling for initialization
    try {
        // Log successful initialization
        console.log('Facebook Group Members Extractor Pro initialized successfully');

        // Auto-initialize if needed
        if (document.readyState === 'complete') {
            setTimeout(() => {
                try {
                    window.FacebookGroupMembersExtractor.init();
                    console.log('Auto-initialization complete');
                } catch (e) {
                    console.error('Auto-initialization failed:', e);
                    window.lastInitError = e.toString();
                }
            }, 1000);
        } else {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    try {
                        window.FacebookGroupMembersExtractor.init();
                        console.log('Auto-initialization complete');
                    } catch (e) {
                        console.error('Auto-initialization failed:', e);
                        window.lastInitError = e.toString();
                    }
                }, 1000);
            });
        }
    } catch (e) {
        console.error('Failed to initialize Facebook Group Members Extractor Pro:', e);
        window.lastInitError = e.toString();
    }
}

// Modified for Selenium integration
if (typeof module !== 'undefined') {
    module.exports = FacebookGroupMembersExtractor;
}
