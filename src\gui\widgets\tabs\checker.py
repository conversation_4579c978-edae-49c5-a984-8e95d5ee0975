from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QTextEdit, QListWidget, QProgressBar,
                            QCheckBox, QSplitter, QFileDialog, QMessageBox, QInputDialog,
                            QGridLayout, QLineEdit, QComboBox, QSlider, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSlot

# Import dialogs
from ...dialogs.bulk_editor import BulkEditorDialog

# Import controller
from .checker_controller import CheckerController

class CheckerTab(QWidget):
    """Checker tab for checking profile status."""

    def __init__(self):
        super().__init__()

        # Create controller
        self.controller = CheckerController()

        # Connect controller signals
        self.controller.progress_updated.connect(self.on_progress_updated)
        self.controller.log_message.connect(self.log_message)
        self.controller.profiles_updated.connect(self.on_profiles_updated)

        # Initialize worker to None
        self.controller.worker = None

        self.setup_ui()
        self.load_sample_data()

    def setup_ui(self):
        """Setup the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)

        # Create splitter for left and right panels
        splitter = QSplitter(Qt.Horizontal)

        # Left panel - lists
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Profiles group
        self.profiles_group = QGroupBox("Profiles")
        profiles_layout = QVBoxLayout()

        # Profiles list
        self.profiles_list = QListWidget()
        profiles_layout.addWidget(self.profiles_list)

        # Profiles buttons
        profiles_buttons = QHBoxLayout()
        self.btn_import_profiles = QPushButton("Import")
        self.btn_add_profile = QPushButton("Add")
        self.btn_remove_profile = QPushButton("Remove")
        self.btn_clear_profiles = QPushButton("Clear")

        profiles_buttons.addWidget(self.btn_import_profiles)
        profiles_buttons.addWidget(self.btn_add_profile)
        profiles_buttons.addWidget(self.btn_remove_profile)
        profiles_buttons.addWidget(self.btn_clear_profiles)

        profiles_layout.addLayout(profiles_buttons)
        self.profiles_group.setLayout(profiles_layout)

        # Banned group
        self.banned_group = QGroupBox("Banned Accounts")
        banned_layout = QVBoxLayout()

        # Banned list
        self.banned_list = QListWidget()
        banned_layout.addWidget(self.banned_list)

        # Banned buttons
        banned_buttons = QHBoxLayout()
        self.btn_export_banned = QPushButton("Export")
        self.btn_remove_banned = QPushButton("Remove")
        self.btn_clear_banned = QPushButton("Clear")

        banned_buttons.addWidget(self.btn_export_banned)
        banned_buttons.addWidget(self.btn_remove_banned)
        banned_buttons.addWidget(self.btn_clear_banned)

        banned_layout.addLayout(banned_buttons)
        self.banned_group.setLayout(banned_layout)

        # Add groups to left layout
        left_layout.addWidget(self.profiles_group)
        left_layout.addWidget(self.banned_group)

        # Right panel - controls and output
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Settings section
        self.settings_group = QGroupBox("Checker Settings")
        settings_layout = QVBoxLayout()

        # Checkboxes for options
        self.auto_ban = QCheckBox("Automatically add banned accounts to list")
        self.auto_ban.setChecked(True)
        settings_layout.addWidget(self.auto_ban)

        self.auto_update_status = QCheckBox("Auto-update profile status")
        self.auto_update_status.setChecked(True)
        settings_layout.addWidget(self.auto_update_status)

        self.headless_mode = QCheckBox("Headless mode")
        self.headless_mode.setChecked(True)
        settings_layout.addWidget(self.headless_mode)

        # Simulation settings
        simulation_group = QGroupBox("Simulation Settings")
        simulation_layout = QVBoxLayout()

        # Ban rate slider
        ban_rate_layout = QHBoxLayout()
        ban_rate_layout.addWidget(QLabel("Ban Rate (%):"))
        self.ban_rate_slider = QSlider(Qt.Horizontal)
        self.ban_rate_slider.setMinimum(0)
        self.ban_rate_slider.setMaximum(100)
        self.ban_rate_slider.setValue(20)  # Default 20%
        self.ban_rate_slider.setTickPosition(QSlider.TicksBelow)
        self.ban_rate_slider.setTickInterval(10)
        ban_rate_layout.addWidget(self.ban_rate_slider)
        self.ban_rate_label = QLabel("20%")
        ban_rate_layout.addWidget(self.ban_rate_label)
        simulation_layout.addLayout(ban_rate_layout)

        # Connect slider value changed signal
        self.ban_rate_slider.valueChanged.connect(self.on_ban_rate_changed)

        # Delay between checks
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("Delay between checks (ms):"))
        self.delay_spinbox = QSpinBox()
        self.delay_spinbox.setMinimum(100)
        self.delay_spinbox.setMaximum(10000)
        self.delay_spinbox.setValue(500)  # Default 500ms
        self.delay_spinbox.setSingleStep(100)
        delay_layout.addWidget(self.delay_spinbox)
        simulation_layout.addLayout(delay_layout)

        simulation_group.setLayout(simulation_layout)
        settings_layout.addWidget(simulation_group)

        self.settings_group.setLayout(settings_layout)

        # Control section
        self.control_group = QGroupBox("Control")
        control_layout = QVBoxLayout()

        # Status and progress
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("Status:"))
        self.status_label = QLabel("Ready")
        status_layout.addWidget(self.status_label, 1)
        control_layout.addLayout(status_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        control_layout.addWidget(self.progress_bar)

        # Control buttons in a single row
        control_buttons = QHBoxLayout()
        self.btn_start = QPushButton("Start Checker")
        self.btn_start.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.btn_stop = QPushButton("Stop")
        self.btn_stop.setStyleSheet("background-color: #f44336; color: white; font-weight: bold;")
        self.btn_stop.setEnabled(False)
        self.btn_clear_log = QPushButton("Clear Log")
        self.btn_clear_log.setStyleSheet("background-color: #FF9800; color: white;")

        control_buttons.addWidget(self.btn_start)
        control_buttons.addWidget(self.btn_stop)
        control_buttons.addWidget(self.btn_clear_log)

        control_layout.addLayout(control_buttons)
        self.control_group.setLayout(control_layout)

        # Log section
        self.log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()

        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # No need for clear log button here as it's now in the control section

        self.log_group.setLayout(log_layout)

        # Add sections to right layout
        right_layout.addWidget(self.settings_group)
        right_layout.addWidget(self.control_group)
        right_layout.addWidget(self.log_group)

        # Add widgets to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # Set initial sizes
        splitter.setSizes([300, 400])

        # Add splitter to main layout
        main_layout.addWidget(splitter)

        # Connect signals
        self.connect_signals()

    def connect_signals(self):
        """Connect signals to slots."""
        # Profiles buttons
        self.btn_import_profiles.clicked.connect(self.on_import_profiles)
        self.btn_add_profile.clicked.connect(self.on_add_profile)
        self.btn_remove_profile.clicked.connect(self.on_remove_profile)
        self.btn_clear_profiles.clicked.connect(self.on_clear_profiles)

        # Banned buttons
        self.btn_export_banned.clicked.connect(self.on_export_banned)
        self.btn_remove_banned.clicked.connect(self.on_remove_banned)
        self.btn_clear_banned.clicked.connect(self.on_clear_banned)

        # Control buttons
        self.btn_start.clicked.connect(self.start)
        self.btn_stop.clicked.connect(self.stop)
        self.btn_clear_log.clicked.connect(self.clear_log)

    def update_item_counts(self):
        """Update the item counts in group titles."""
        # Get counts from database
        profiles_count = self.controller.db.get_profiles_count()
        banned_count = self.controller.db.get_profiles_count(status="banned")

        # Update UI
        self.profiles_group.setTitle(f"Profiles ({profiles_count})")
        self.banned_group.setTitle(f"Banned Accounts ({banned_count})")

        # Update list widgets if they don't match the database
        if self.profiles_list.count() != profiles_count:
            self.refresh_profiles_list()

        if self.banned_list.count() != banned_count:
            self.refresh_banned_list()

    def refresh_profiles_list(self):
        """Refresh the profiles list from the database."""
        profiles = self.controller.load_profiles()
        self.profiles_list.clear()
        self.profiles_list.addItems(profiles)

    def refresh_banned_list(self):
        """Refresh the banned accounts list from the database."""
        banned = self.controller.load_banned_profiles()
        self.banned_list.clear()
        self.banned_list.addItems(banned)

    def load_sample_data(self):
        """Load sample data for demonstration."""
        # Load data from database
        self.refresh_profiles_list()
        self.refresh_banned_list()

        # Update item counts
        self.update_item_counts()

        # Log initial message
        self.log_message("[INFO] Sample data loaded")

    def log_message(self, message):
        """Add a message to the log with color formatting."""
        # Apply color formatting based on message content
        if "[BANNED]" in message:
            # Red color for banned profiles
            formatted_message = f"<span style='color:red;'>{message}</span>"
        elif "[GOOD]" in message:
            # Green color for active profiles
            formatted_message = f"<span style='color:green;'>{message}</span>"
        else:
            # Default color for other messages
            formatted_message = message

        # Append formatted message
        self.log_text.append(formatted_message)

        # Scroll to bottom
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def clear_log(self):
        """Clear the log text area."""
        self.log_text.clear()

    def on_import_profiles(self):
        """Handle import profiles button click."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Profiles File", "", "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Importing profiles from {file_path}")

            try:
                # Import profiles directly to database
                profiles = self.controller.load_profiles(file_path)

                # Refresh the profiles list
                self.refresh_profiles_list()

                # Update item counts
                self.update_item_counts()

                # Show success message
                QMessageBox.information(self, "Import Successful", f"Successfully imported {len(profiles)} profiles.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to import profiles: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import profiles: {e}")

    def on_add_profile(self):
        """Handle add profile button click."""
        profile, ok = QInputDialog.getText(
            self, "Add Profile", "Enter profile ID:"
        )

        if ok and profile.strip():
            profile_id = profile.strip()

            # Check if profile exists in database
            existing_profile = self.controller.db.get_profile(profile_id)

            if not existing_profile:
                # Save profile to database
                if self.controller.db.save_profile(profile_id):
                    self.log_message(f"[INFO] Added new profile: {profile_id}")

                    # Refresh the profiles list
                    self.refresh_profiles_list()

                    # Update item counts
                    self.update_item_counts()
                else:
                    self.log_message(f"[ERROR] Failed to add profile: {profile_id}")
                    QMessageBox.critical(self, "Error", f"Failed to add profile '{profile_id}'.")
            else:
                self.log_message(f"[WARNING] Profile already exists: {profile_id}")
                QMessageBox.warning(self, "Duplicate Profile", f"Profile '{profile_id}' already exists.")

    def on_remove_profile(self):
        """Handle remove profile button click."""
        selected_items = self.profiles_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Selection", "Please select a profile to remove.")
            return

        profile_id = selected_items[0].text()

        # Remove profile from database
        if self.controller.db.delete_profile(profile_id):
            self.log_message(f"[INFO] Removed profile: {profile_id}")

            # Refresh the profiles list
            self.refresh_profiles_list()

            # Update item counts
            self.update_item_counts()
        else:
            self.log_message(f"[ERROR] Failed to remove profile: {profile_id}")
            QMessageBox.critical(self, "Error", f"Failed to remove profile '{profile_id}'.")

    def on_clear_profiles(self):
        """Handle clear profiles button click."""
        reply = QMessageBox.question(
            self,
            "Confirm Clear",
            "Are you sure you want to clear all profiles?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Clear profiles from database
            if self.controller.db.clear_profiles():
                self.log_message("[INFO] Cleared all profiles")

                # Refresh the profiles list
                self.refresh_profiles_list()

                # Update item counts
                self.update_item_counts()
            else:
                self.log_message("[ERROR] Failed to clear profiles")
                QMessageBox.critical(self, "Error", "Failed to clear profiles.")

    def on_export_banned(self):
        """Handle export banned button click."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Banned Accounts", "", "Text Files (*.txt);;All Files (*)"
        )
        if file_path:
            self.log_message(f"[INFO] Exporting banned accounts to {file_path}")

            try:
                # Get banned accounts from database
                banned = self.controller.load_banned_profiles()

                # Write to file
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write("\n".join(banned))

                self.log_message(f"[INFO] Exported {len(banned)} banned accounts to {file_path}")
                QMessageBox.information(self, "Export Successful", f"Successfully exported {len(banned)} banned accounts.")
            except Exception as e:
                self.log_message(f"[ERROR] Failed to export banned accounts: {e}")
                QMessageBox.critical(self, "Export Error", f"Failed to export banned accounts: {e}")

    def on_remove_banned(self):
        """Handle remove banned button click."""
        selected_items = self.banned_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Selection", "Please select a banned account to remove.")
            return

        profile_id = selected_items[0].text()

        # Update profile status in database
        if self.controller.db.save_profile(profile_id, status="active"):
            self.log_message(f"[INFO] Removed from banned: {profile_id}")

            # Refresh the banned list
            self.refresh_banned_list()

            # Update item counts
            self.update_item_counts()
        else:
            self.log_message(f"[ERROR] Failed to update profile status: {profile_id}")
            QMessageBox.critical(self, "Error", f"Failed to update profile status for '{profile_id}'.")

    def on_clear_banned(self):
        """Handle clear banned button click."""
        reply = QMessageBox.question(
            self,
            "Confirm Clear",
            "Are you sure you want to clear all banned accounts?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Update all banned profiles to active
            banned = self.controller.load_banned_profiles()
            count = 0

            for profile_id in banned:
                if self.controller.db.save_profile(profile_id, status="active"):
                    count += 1

            self.log_message(f"[INFO] Cleared {count} banned accounts")

            # Refresh the banned list
            self.refresh_banned_list()

            # Update item counts
            self.update_item_counts()

    def on_ban_rate_changed(self, value):
        """Handle ban rate slider value changed."""
        self.ban_rate_label.setText(f"{value}%")

    def start(self):
        """Start the checker process."""
        # Get profiles from list
        profiles = [self.profiles_list.item(i).text() for i in range(self.profiles_list.count())]

        # Get settings
        settings = {
            "auto_ban": self.auto_ban.isChecked(),
            "auto_update_status": self.auto_update_status.isChecked(),
            "headless_mode": self.headless_mode.isChecked(),
            "ban_rate": self.ban_rate_slider.value(),
            "delay": self.delay_spinbox.value()
        }

        # Validate inputs
        if not profiles:
            QMessageBox.warning(self, "Warning", "No profiles loaded.")
            return

        # Update UI
        self.btn_start.setEnabled(False)
        self.btn_stop.setEnabled(True)
        self.status_label.setText("Status: Running")
        self.progress_bar.setValue(0)

        # Start checker process
        self.controller.start_checker(profiles, settings)

    def stop(self):
        """Stop the checker process."""
        reply = QMessageBox.question(
            self,
            "Confirm Stop",
            "Are you sure you want to stop the checker?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop checker process
            self.controller.stop_checker()

            # Update UI
            self.btn_start.setEnabled(True)
            self.btn_stop.setEnabled(False)
            self.status_label.setText("Status: Stopped")

            # Log stop
            self.log_message("[WARNING] Checker stopped by user")

    @pyqtSlot(int, int, str)
    def on_progress_updated(self, current, total, message):
        """Handle progress update from controller."""
        # Update progress bar
        progress = int(current / total * 100) if total > 0 else 0
        self.progress_bar.setValue(progress)

        # Update status label
        self.status_label.setText(f"Status: {message}")

    @pyqtSlot(str)
    def on_profile_banned(self, profile):
        """Handle profile banned signal from worker thread."""
        self.log_message(f"[INFO] Adding {profile} to banned accounts list")

        # Check if profile is already in the banned list
        for i in range(self.banned_list.count()):
            if self.banned_list.item(i).text() == profile:
                return  # Profile already in the list

        # Add profile to banned list
        self.banned_list.addItem(profile)

        # Remove profile from profiles list if it exists there
        for i in range(self.profiles_list.count() - 1, -1, -1):  # Iterate backwards
            if self.profiles_list.item(i).text() == profile:
                self.log_message(f"[INFO] Removing {profile} from profiles list")
                self.profiles_list.takeItem(i)  # Remove item from list
                break  # Only remove the first occurrence

        # Update item counts
        self.update_item_counts()

        # Refresh banned list from database to ensure it's up to date
        self.refresh_banned_list()

    @pyqtSlot()
    def on_profiles_updated(self):
        """Handle profiles updated signal from controller."""
        self.log_message("[INFO] Updating profiles lists after checker completion")

        # Get banned profiles from worker if available
        banned_profiles = []
        if self.controller.worker and hasattr(self.controller.worker, 'banned_profiles'):
            banned_profiles = self.controller.worker.banned_profiles
            self.log_message(f"[INFO] Found {len(banned_profiles)} banned profiles to move")

        # Move banned profiles from profiles list to banned list
        if banned_profiles:
            # First, add them to the banned list if they're not already there
            for profile_id in banned_profiles:
                # Check if profile is already in the banned list
                found = False
                for i in range(self.banned_list.count()):
                    if self.banned_list.item(i).text() == profile_id:
                        found = True
                        break

                if not found:
                    self.log_message(f"[INFO] Moving {profile_id} to banned accounts list")
                    self.banned_list.addItem(profile_id)

            # Then, remove them from the profiles list
            for i in range(self.profiles_list.count() - 1, -1, -1):  # Iterate backwards
                profile_id = self.profiles_list.item(i).text()
                if profile_id in banned_profiles:
                    self.log_message(f"[INFO] Removing {profile_id} from profiles list")
                    self.profiles_list.takeItem(i)  # Remove item from list

        # Update item counts
        self.update_item_counts()

        # Update UI
        self.btn_start.setEnabled(True)
        self.btn_stop.setEnabled(False)
        self.status_label.setText("Status: Ready")
