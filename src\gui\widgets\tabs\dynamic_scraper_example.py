"""
Example usage of the dynamic page handlers for Facebook and Twitter.
"""

import os
import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

from .facebook_handler import <PERSON><PERSON><PERSON><PERSON>
from .twitter_handler import <PERSON><PERSON><PERSON><PERSON>

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('dynamic_scraper.log')
    ]
)

logger = logging.getLogger('dynamic_scraper')

def setup_driver(headless=False, profile_path=None):
    """Set up and return a Chrome WebDriver instance.
    
    Args:
        headless: Whether to run in headless mode
        profile_path: Path to Chrome user profile
        
    Returns:
        WebDriver: Configured Chrome WebDriver instance
    """
    logger.info("Setting up Chrome WebDriver")
    
    chrome_options = Options()
    
    if headless:
        chrome_options.add_argument('--headless')
    
    # Add common options for better performance and stability
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-notifications')
    chrome_options.add_argument('--disable-popup-blocking')
    chrome_options.add_argument('--start-maximized')
    
    # Use a specific user profile if provided
    if profile_path and os.path.exists(profile_path):
        chrome_options.add_argument(f'--user-data-dir={profile_path}')
        logger.info(f"Using Chrome profile at: {profile_path}")
    
    # Set up the WebDriver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        logger.info("Chrome WebDriver set up successfully")
        return driver
    except Exception as e:
        logger.error(f"Error setting up Chrome WebDriver: {e}")
        raise

def facebook_example():
    """Example of using the FacebookHandler."""
    logger.info("Starting Facebook example")
    
    # Set up the WebDriver
    driver = setup_driver(headless=False)
    
    try:
        # Create a FacebookHandler instance
        fb_handler = FacebookHandler(driver, logger)
        
        # Navigate to a Facebook group
        group_url = "https://www.facebook.com/groups/502282678734729"
        if not fb_handler.navigate_to_group(group_url):
            logger.error("Failed to navigate to Facebook group")
            return
        
        # Extract post links
        logger.info("Extracting post links")
        post_links = fb_handler.extract_post_links(max_posts=5)
        
        if post_links:
            logger.info(f"Extracted {len(post_links)} post links:")
            for i, link in enumerate(post_links):
                logger.info(f"{i+1}. {link}")
            
            # Extract data from the first post
            if len(post_links) > 0:
                logger.info(f"Extracting data from post: {post_links[0]}")
                post_data = fb_handler.extract_post_data(post_links[0])
                logger.info(f"Post data: {post_data}")
        else:
            logger.warning("No post links extracted, trying click method")
            post_links = fb_handler.extract_post_links_by_clicking(max_posts=5)
            
            if post_links:
                logger.info(f"Extracted {len(post_links)} post links using click method:")
                for i, link in enumerate(post_links):
                    logger.info(f"{i+1}. {link}")
            else:
                logger.error("Failed to extract any post links")
        
        # Extract multiple posts with scrolling
        logger.info("Extracting multiple posts with scrolling")
        posts_data = fb_handler.extract_multiple_posts(max_posts=10, scroll_count=3, extract_details=True)
        
        if posts_data:
            logger.info(f"Extracted {len(posts_data)} posts")
            
            # Save to file
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            # Save in different formats
            fb_handler.save_posts_to_file(posts_data, os.path.join(output_dir, "facebook_posts.json"), format='json')
            fb_handler.save_posts_to_file(posts_data, os.path.join(output_dir, "facebook_posts.txt"), format='txt')
            fb_handler.save_posts_to_file(posts_data, os.path.join(output_dir, "facebook_posts.csv"), format='csv')
        else:
            logger.error("Failed to extract any posts")
        
    except Exception as e:
        logger.error(f"Error in Facebook example: {e}")
    finally:
        # Clean up
        driver.quit()
        logger.info("Facebook example completed")

def twitter_example():
    """Example of using the TwitterHandler."""
    logger.info("Starting Twitter example")
    
    # Set up the WebDriver
    driver = setup_driver(headless=False)
    
    try:
        # Create a TwitterHandler instance
        twitter_handler = TwitterHandler(driver, logger)
        
        # Navigate to a Twitter profile
        username = "elonmusk"
        if not twitter_handler.navigate_to_profile(username):
            logger.error(f"Failed to navigate to Twitter profile: {username}")
            return
        
        # Extract tweet links
        logger.info("Extracting tweet links")
        tweet_links = twitter_handler.extract_tweet_links(max_tweets=5)
        
        if tweet_links:
            logger.info(f"Extracted {len(tweet_links)} tweet links:")
            for i, link in enumerate(tweet_links):
                logger.info(f"{i+1}. {link}")
            
            # Extract data from the first tweet
            if len(tweet_links) > 0:
                logger.info(f"Extracting data from tweet: {tweet_links[0]}")
                tweet_data = twitter_handler.extract_tweet_data(tweet_links[0])
                logger.info(f"Tweet data: {tweet_data}")
        else:
            logger.error("Failed to extract any tweet links")
        
        # Navigate to a search query
        search_query = "python programming"
        if not twitter_handler.navigate_to_search(search_query):
            logger.error(f"Failed to navigate to Twitter search: {search_query}")
            return
        
        # Extract multiple tweets with scrolling
        logger.info("Extracting multiple tweets with scrolling")
        tweets_data = twitter_handler.extract_multiple_tweets(max_tweets=10, scroll_count=3, extract_details=True)
        
        if tweets_data:
            logger.info(f"Extracted {len(tweets_data)} tweets")
            
            # Save to file
            output_dir = "output"
            os.makedirs(output_dir, exist_ok=True)
            
            # Save in different formats
            twitter_handler.save_tweets_to_file(tweets_data, os.path.join(output_dir, "twitter_tweets.json"), format='json')
            twitter_handler.save_tweets_to_file(tweets_data, os.path.join(output_dir, "twitter_tweets.txt"), format='txt')
            twitter_handler.save_tweets_to_file(tweets_data, os.path.join(output_dir, "twitter_tweets.csv"), format='csv')
        else:
            logger.error("Failed to extract any tweets")
        
    except Exception as e:
        logger.error(f"Error in Twitter example: {e}")
    finally:
        # Clean up
        driver.quit()
        logger.info("Twitter example completed")

if __name__ == "__main__":
    # Run the examples
    facebook_example()
    twitter_example()
