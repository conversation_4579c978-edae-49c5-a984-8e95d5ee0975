import sys
import os
import logging
import time
import json
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# Add src directory to path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import main window and browser manager
from gui.main_window import MainWindow
from core.utils.browser import BrowserManager

# Setup logging
def setup_logging():
    """Setup logging configuration."""
    logger = logging.getLogger('zpammer')
    logger.setLevel(logging.DEBUG)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create file handler
    os.makedirs('logs', exist_ok=True)
    file_handler = logging.FileHandler('logs/zpammer.log')
    file_handler.setLevel(logging.DEBUG)

    # Create formatters
    console_formatter = logging.Formatter('%(levelname)s: %(message)s')
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Set formatters
    console_handler.setFormatter(console_formatter)
    file_handler.setFormatter(file_formatter)

    # Add handlers
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger

def handle_nstbrowser_update_error():
    """Handle the NstBrowser update error by creating bypass files and modifying settings."""
    logger = logging.getLogger('zpammer')
    logger.info("Handling NstBrowser update error...")

    try:
        # Get the NstBrowser app data directory
        if sys.platform == "win32":
            # Method 1: Create bypass file in AppData
            app_data = os.environ.get('APPDATA', '')
            nst_config_dir = os.path.join(app_data, 'NstBrowser')
            os.makedirs(nst_config_dir, exist_ok=True)

            # Create or update the bypass file
            bypass_file = os.path.join(nst_config_dir, 'update_bypass.json')
            bypass_data = {
                "bypass_update": True,
                "last_check": time.time(),
                "version": "1.16.1",
                "auto_update": False
            }

            with open(bypass_file, 'w') as f:
                json.dump(bypass_data, f, indent=4)
            logger.info(f"Created update bypass file at {bypass_file}")

            # Method 2: Create settings file in LocalAppData
            local_app_data = os.environ.get('LOCALAPPDATA', '')
            nst_settings_dir = os.path.join(local_app_data, 'NstBrowser', 'User Data', 'Default')
            os.makedirs(nst_settings_dir, exist_ok=True)

            # Create or update the settings file
            settings_file = os.path.join(nst_settings_dir, 'Preferences')

            # Try to read existing settings if available
            settings_data = {}
            try:
                if os.path.exists(settings_file):
                    with open(settings_file, 'r') as f:
                        settings_data = json.load(f)
            except:
                # If reading fails, start with empty settings
                settings_data = {}

            # Update settings to disable updates
            if 'browser' not in settings_data:
                settings_data['browser'] = {}
            if 'check_default_browser' not in settings_data['browser']:
                settings_data['browser']['check_default_browser'] = False

            # Add update settings
            settings_data['update'] = {
                "enabled": False,
                "check_automatically": False,
                "last_check_time": str(int(time.time())),
                "version": "1.16.1"
            }

            # Write updated settings
            with open(settings_file, 'w') as f:
                json.dump(settings_data, f, indent=4)
            logger.info(f"Updated NstBrowser settings at {settings_file}")

            return True
        else:
            # For non-Windows platforms
            logger.warning("Update bypass is currently only implemented for Windows.")
            return False
    except Exception as e:
        logger.error(f"Failed to create update bypass files: {e}")
        return False

def check_nstbrowser():
    """Check if NstBrowser is running but don't start it automatically."""
    logger = logging.getLogger('zpammer')
    logger.info("Checking NstBrowser status...")

    # Create browser manager instance
    browser_manager = BrowserManager()

    # Check if NstBrowser is running
    if "nstbrowser" in browser_manager.config and browser_manager.config["nstbrowser"].get("executable_path"):
        executable_path = browser_manager.config["nstbrowser"].get("executable_path")

        # Check if NstBrowser is already running
        is_running = False
        try:
            if sys.platform == "win32":
                import subprocess
                result = subprocess.run(["tasklist", "/FI", "IMAGENAME eq nstchrome.exe"],
                                        capture_output=True, text=True)
                if "nstchrome.exe" in result.stdout:
                    logger.info("NstBrowser is already running.")
                    is_running = True
            else:  # Linux/macOS
                result = os.system("pgrep -f nstchrome > /dev/null")
                if result == 0:  # Process found
                    logger.info("NstBrowser is already running.")
                    is_running = True
        except Exception as e:
            logger.warning(f"Error checking if NstBrowser is running: {e}")

        # Just return the status, don't start automatically
        return is_running
    else:
        logger.warning("NstBrowser configuration not found. Please configure NstBrowser path in settings.")
        return False

def main():
    """Main entry point for the application."""
    # Setup logging
    logger = setup_logging()
    logger.info("Starting Zpammer GUI application")

    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Zpammer")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Zpammer Team")

    # Set application style
    app.setStyle("Fusion")

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run application
    exit_code = app.exec_()
    logger.info(f"Application exited with code {exit_code}")
    return exit_code

if __name__ == "__main__":
    sys.exit(main())
