import os
import time
import random
import logging
import sqlite3
import threading
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
# Import common exceptions for better error handling in try-except blocks
from selenium.common.exceptions import WebDriverException
from PyQt5.QtCore import QObject, pyqtSignal

from .utils.browser import BrowserManager

# Setup logger
logger = logging.getLogger('zpammer.spammer')

class SpammerSignals(QObject):
    """Signals for the Spammer class."""
    comment_ready = pyqtSignal(str, str)  # comment_text, post_url
    comment_posted = pyqtSignal(str, str)  # comment_text, post_url
    comment_failed = pyqtSignal(str, str)  # comment_text, post_url

class Spammer:
    """
    Handles automated commenting on Facebook posts.

    This class manages:
    - Loading profiles, posts, and comments
    - Connecting to Facebook
    - Posting comments on posts
    - Tracking commented posts
    """

    def __init__(self, config_path=None):
        """
        Initialize the spammer.

        Args:
            config_path (str, optional): Path to the configuration file.
        """
        self.browser_manager = BrowserManager(config_path)
        self.db_connection = None
        self.current_profile = None
        self.current_driver = None
        self.is_running = False
        self.skip_current_profile = False
        self.settings = {}

        # Initialize settings with defaults from original Spammer.py
        self.settings = {
            "sleep_range": {"min": 1.0, "max": 4.0},
            "spam_count": 3,
            "execute_spam_comment": True,
            "auto_close": True,
            "confirm_comments": True,
            "skip_commented": True,
            "use_first_profile_for_selectors": True,  # Use first profile only for selector detection
            "selectors_detection_complete": False  # Flag to track if selectors detection is complete
        }

        # Setup signals and events for comment confirmation
        self.signals = SpammerSignals()
        self.comment_confirmed_event = threading.Event()

    def connect_to_database(self, db_path=None):
        """
        Connect to the SQLite database.

        Args:
            db_path (str, optional): Path to the database file.

        Returns:
            bool: True if successful, False otherwise.
        """
        if db_path is None:
            db_path = os.path.join("Files", "comments.db")

        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(db_path), exist_ok=True)

            # Connect to database with better error handling
            self.db_connection = sqlite3.connect(db_path)

            # Enable foreign keys support
            self.db_connection.execute("PRAGMA foreign_keys = ON")

            # Convert rows to dictionaries
            self.db_connection.row_factory = sqlite3.Row

            self._create_tables()
            logger.info(f"Connected to database: {db_path}")
            return True
        except sqlite3.Error as e:
            logger.error(f"SQLite error connecting to database: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to database: {e}")
            return False

    def _create_tables(self):
        """
        Create necessary tables in the database if they don't exist.
        """
        if not self.db_connection:
            logger.error("No database connection.")
            return

        cursor = self.db_connection.cursor()

        # Create comments table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS comments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            comment_id TEXT UNIQUE NOT NULL,
            profile_id TEXT,
            post_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create index for faster lookups
        cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_comment_id ON comments (comment_id)
        ''')

        self.db_connection.commit()
        logger.info("Database tables created/verified.")

    def load_profiles(self, file_path=None):
        """
        Load profile IDs from a file.

        Args:
            file_path (str, optional): Path to the profiles file.

        Returns:
            list: List of profile IDs.
        """
        if file_path is None:
            file_path = os.path.join("Files", "profile_ids.txt")

        profiles = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                profiles = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(profiles)} profiles from {file_path}")
        except Exception as e:
            logger.error(f"Failed to load profiles: {e}")

        return profiles

    def load_posts(self, file_path=None):
        """
        Load post links from a file.

        Args:
            file_path (str, optional): Path to the posts file.

        Returns:
            list: List of post links.
        """
        if file_path is None:
            file_path = os.path.join("Files", "post_links.txt")

        posts = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                posts = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(posts)} posts from {file_path}")
        except Exception as e:
            logger.error(f"Failed to load posts: {e}")

        return posts

    def load_comments(self, file_path=None):
        """
        Load comments from a file.

        Args:
            file_path (str, optional): Path to the comments file.

        Returns:
            list: List of comments.
        """
        if file_path is None:
            file_path = os.path.join("Files", "spam_comments.txt")

        comments = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                comments = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(comments)} comments from {file_path}")
        except Exception as e:
            logger.error(f"Failed to load comments: {e}")

        return comments

    def check_comment_exists(self, comment_id):
        """
        Check if a comment ID exists in the database.

        Args:
            comment_id (str): Comment ID to check.

        Returns:
            bool: True if exists, False otherwise.
        """
        if not self.db_connection:
            logger.error("No database connection.")
            return False

        cursor = self.db_connection.cursor()
        cursor.execute("SELECT 1 FROM comments WHERE comment_id = ? LIMIT 1", (comment_id,))
        result = cursor.fetchone()
        return result is not None

    def save_comment(self, comment_id, profile_id, post_id=None):
        """
        Save a comment to the database.

        Args:
            comment_id (str): Comment ID.
            profile_id (str): Profile ID.
            post_id (str, optional): Post ID.

        Returns:
            bool: True if successful, False otherwise.
        """
        if not self.db_connection:
            logger.error("No database connection.")
            return False

        try:
            cursor = self.db_connection.cursor()
            cursor.execute(
                "INSERT OR IGNORE INTO comments (comment_id, profile_id, post_id) VALUES (?, ?, ?)",
                (comment_id, profile_id, post_id)
            )
            self.db_connection.commit()
            return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Failed to save comment: {e}")
            return False

    def random_sleep(self, min_seconds=None, max_seconds=None, _=None):  # _ is for backward compatibility
        """
        Sleep for a random amount of time based on settings.
        This is a direct port of the random_sleep function from Bin/Spammer.py

        Args:
            min_seconds (float, optional): Override minimum sleep time in seconds.
            max_seconds (float, optional): Override maximum sleep time in seconds.
            _ (any): Ignored parameter kept for backward compatibility.

        Returns:
            float: Actual sleep time in seconds.
        """
        # Get sleep range from settings (from original Spammer.py)
        sleep_range = self.settings.get("sleep_range", {"min": 1.0, "max": 4.0})

        # Use provided min/max if specified, otherwise use from settings
        min_sleep = min_seconds if min_seconds is not None else sleep_range.get("min", 1.0)
        max_sleep = max_seconds if max_seconds is not None else sleep_range.get("max", 4.0)

        # Calculate sleep time (from original Spammer.py)
        base_sleep = random.uniform(min_sleep, max_sleep)
        extra_sleep = random.uniform(0.1, 0.5)
        total_sleep = base_sleep + extra_sleep

        # Sleep for the calculated time
        time.sleep(total_sleep)
        return total_sleep

    def select_newest_comments(self, driver):
        """
        Select the 'Newest' comments sorting option.
        This is a direct port of the select_new_comments function from Bin/Spammer.py

        Args:
            driver: WebDriver instance.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            if "This content isn't available" in driver.page_source:
                logger.warning("This content isn't available right now. Skipping...")
                return False

            logger.info("Waiting for the Sort Comments button...")
            dropdown_button = WebDriverWait(driver, 20).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, ".xe0p6wg > div:nth-child(1) > span:nth-child(1)"))
            )
            logger.info("Sort Comments button found, clicking...")
            dropdown_button.click()

            # JavaScript to select "Newest" and extract texts - from original Bin/Spammer.py
            js_select_newest_and_extract = r"""
            // 1. Define the CSS selector with all the specified classes
            const selector = '.x4k7w5x.x1h91t0o.x1beo9mf.xaigb6o.x12ejxvf.x3igimt.xarpa2k.xedcshv.x1lytzrv.x1t2pt76.x7ja8zs.x1n2onr6.x1qrby5j.x1jfb8zj';

            // 2. Select all elements matching the selector
            const elements = document.querySelectorAll(selector);

            // Array to hold extracted texts
            const extractedTexts = [];
            let clicked = false;

            // 3. Iterate over each element
            for (let index = 0; index < elements.length; index++) {
                const element = elements[index];

                // 3a. Check if the element has exactly 3 direct children
                if (element.children.length === 3) {
                    // 3b. Get the second child (index 1)
                    const secondChild = element.children[1];

                    // 4. Find all <span> elements within the second child
                    const spans = secondChild.querySelectorAll('span');

                    if (spans.length > 0) {
                        // 4a. Get the first <span>
                        const firstSpan = spans[0];

                        // 4b. Extract the text content
                        const extractedText = firstSpan.textContent.trim();
                        extractedTexts.push(extractedText);

                        // 4c. Click the <span> element if not already clicked
                        if (!clicked) {
                            firstSpan.click();
                            clicked = true;
                            console.log(`Clicked on span with text: "${extractedText}"`);
                        }
                    } else {
                        console.log(`Element ${index + 1} - No <span> found in the second child.`);
                    }
                }
            }

            // 5. Return the extracted texts and click status
            return {
                texts: extractedTexts,
                clicked: clicked
            };
            """

            # Execute the JavaScript and wait for it to click the "Newest" option
            result = WebDriverWait(driver, 20).until(
                lambda d: d.execute_script(js_select_newest_and_extract)
            )

            clicked = result.get('clicked', False)
            texts = result.get('texts', [])

            if clicked:
                logger.info(f"'Newest' option clicked successfully. Available options: {texts}")
                self.random_sleep(2, 4)

                # Wait for comments to load after sorting
                # Corrected CSS selector without the extra parenthesis
                corrected_js_selector = r"""
                return document.querySelectorAll('.html-div.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x1gslohp > :not([class])').length > 0;
                """

                # Wait until comments are loaded by executing the corrected selector
                WebDriverWait(driver, 20).until(
                    lambda d: d.execute_script(corrected_js_selector)
                )
                logger.info("Comments sorted and loaded successfully.")
                self.random_sleep(2, 4)
                return True
            else:
                logger.warning("Failed to click the 'Newest' option.")
                return False
        except WebDriverException as e:
            logger.error(f"WebDriver error selecting 'Newest' comments: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error selecting 'Newest' comments: {e}")
            return False

    def post_comment(self, driver, comment_text, post_url=None):
        """
        Post a comment on a Facebook post.
        This is a direct port of the spam_comment function from Bin/Spammer.py

        Args:
            driver: WebDriver instance.
            comment_text (str): Comment text to post.
            post_url (str, optional): URL of the post being commented on.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Check if comment is empty or just whitespace
            if not comment_text or comment_text.strip() == "":
                logger.info("Empty comment detected, skipping...")
                return False

            # First check if execute_spam_comment is enabled (from original Spammer.py)
            execute_spam_comment = self.settings.get("execute_spam_comment", True)

            # If execute_spam_comment is disabled, don't type anything and return success
            # This simulates posting a comment without actually doing it
            if not execute_spam_comment:
                logger.info("execute_spam_comment is disabled. Skipping comment typing.")
                # Signal that we're simulating a comment
                self.signals.comment_posted.emit("[Simulated comment - execute_spam_comment disabled]", post_url)
                return True

            # Get active element (comment box) - this ensures focus is on the comment box
            driver.switch_to.active_element

            # Create action chain for typing
            actions = ActionChains(driver)

            # Get typing speed setting
            typing_speed = self.settings.get("typing_speed", "Normal")

            # Define typing speed factors based on the setting
            typing_speed_factors = {
                "Very Slow": 3.0,
                "Slow": 2.0,
                "Normal": 1.0,
                "Fast": 0.5,
                "Very Fast": 0.2
            }

            # Get the speed factor (default to 1.0 if not found)
            speed_factor = typing_speed_factors.get(typing_speed, 1.0)

            # Type comment with random typing simulation adjusted by typing speed
            for char in comment_text:
                # Simulate typo occasionally (2% chance) - adjusted by typing speed
                if random.random() < 0.02:
                    typo_char = random.choice('abcdefghijklmnopqrstuvwxyz')
                    actions.send_keys(typo_char)
                    # Use typing speed factor for typo pause
                    self.random_sleep(0.05 * speed_factor, 0.3 * speed_factor)  # Pause for typo
                    actions.send_keys(Keys.BACKSPACE)

                # Type character with random delay
                actions.send_keys(char)
                # Use a very short delay between keystrokes, adjusted by typing speed
                pause_time = random.uniform(0.05, 0.3) * speed_factor
                actions.pause(pause_time)

            # Handle mentions if present
            if "@" in comment_text:
                actions.send_keys(Keys.ENTER)

            # Minimal wait before submitting
            self.random_sleep(0.3, 0.5)

            # Perform the typing action
            actions.perform()

            # Get current URL if not provided
            if post_url is None:
                post_url = driver.current_url

            # We already checked execute_spam_comment at the beginning of the method
            # Now we just need to handle the actual posting
            execute_spam_comment = self.settings.get("execute_spam_comment", True)

            # Then check if comment confirmation is enabled
            if self.settings.get("confirm_comments", True):
                # Signal that a comment is ready for confirmation
                logger.info(f"Waiting for user confirmation to post comment: {comment_text[:50]}...")
                self.signals.comment_ready.emit(comment_text, post_url)

                # Wait for confirmation
                self.comment_confirmed_event.clear()
                confirmed = self.comment_confirmed_event.wait(timeout=60)  # Wait up to 60 seconds for confirmation

                if confirmed and self.is_running:
                    # User confirmed, check if we should actually post the comment
                    if execute_spam_comment:
                        logger.info("User confirmed comment, posting...")
                        actions = ActionChains(driver)
                        actions.send_keys(Keys.ENTER)
                        actions.perform()

                        # Wait for comment to be posted
                        self.random_sleep(3, 5)

                        logger.info("Comment posted successfully after confirmation.")
                        self.signals.comment_posted.emit(comment_text, post_url)
                        return True
                    else:
                        # This should never happen now since we check at the beginning,
                        # but keeping it for safety
                        logger.info("execute_spam_comment is disabled. Simulating but not posting comment.")
                        # Clear the comment box by pressing Escape
                        actions = ActionChains(driver)
                        actions.send_keys(Keys.ESCAPE)
                        actions.perform()
                        self.random_sleep(1, 2)  # Short wait after clearing

                        # Still count as success for simulation purposes
                        self.signals.comment_posted.emit("[Simulated comment - execute_spam_comment disabled]", post_url)
                        return True
                else:
                    # User cancelled or timeout - just skip to the next comment without posting anything
                    logger.info("Comment skipped by user. Moving to next comment.")
                    # Clear the comment box by pressing Escape
                    actions = ActionChains(driver)
                    actions.send_keys(Keys.ESCAPE)
                    actions.perform()
                    self.random_sleep(1, 2)  # Short wait after clearing

                    # Signal that we're skipping this comment
                    self.signals.comment_failed.emit(comment_text, post_url)
                    return False
            else:
                # When execute_spam_comment is enabled and confirm_comments is disabled, post the comment
                if execute_spam_comment:
                    logger.info("Posting comment without confirmation...")
                    actions = ActionChains(driver)
                    actions.send_keys(Keys.ENTER)
                    actions.perform()

                    # Wait for comment to be posted
                    self.random_sleep(3, 5)

                    logger.info("Comment posted successfully.")
                    self.signals.comment_posted.emit(comment_text, post_url)
                    return True
                else:
                    # This should never happen now since we check at the beginning,
                    # but keeping it for safety
                    logger.info("execute_spam_comment is disabled. Exiting comment box.")
                    # Clear the comment box by pressing Escape
                    actions = ActionChains(driver)
                    actions.send_keys(Keys.ESCAPE)
                    actions.perform()
                    self.random_sleep(1, 2)  # Short wait after clearing

                    # Still count as success for simulation purposes
                    self.signals.comment_posted.emit("[Simulated comment - execute_spam_comment disabled]", post_url)
                    return True
        except WebDriverException as e:
            logger.error(f"WebDriver error posting comment: {e}")
            self.signals.comment_failed.emit(comment_text, post_url or "unknown")
            return False
        except Exception as e:
            logger.error(f"Unexpected error posting comment: {e}")
            self.signals.comment_failed.emit(comment_text, post_url or "unknown")
            return False

    def confirm_comment(self, confirm=True):
        """
        Confirm or cancel a pending comment.

        Args:
            confirm (bool): Whether to confirm (True) or cancel (False) the comment.

        Returns:
            bool: True if successful, False otherwise.
        """
        if confirm:
            # Set the event to signal confirmation
            self.comment_confirmed_event.set()
            return True
        else:
            # Clear the event to signal cancellation
            self.comment_confirmed_event.clear()
            return False

    def process_post(self, driver, post_url, comments, comments_per_post=3):
        """
        Process a single post by posting comments.

        Args:
            driver: WebDriver instance.
            post_url (str): URL of the post to process.
            comments (list): List of comments to choose from.
            comments_per_post (int): Number of comments to post per post.

        Returns:
            int: Number of comments successfully posted.
        """
        if not self.is_running:
            return 0

        logger.info(f"Processing post: {post_url}")

        try:
            # Navigate to post
            driver.get(post_url)
            self.random_sleep(3, 5)

            # Check if post is available
            if ("This content isn't available" in driver.page_source or
                "content isn't available right now" in driver.page_source or
                "When this happens, it's usually because the owner only shared it with a small group of people" in driver.page_source):
                logger.warning(f"Post not available (deleted or restricted): {post_url}")
                # Return a special code to indicate this post should be marked as processed but not retried
                return -1

            # Sort comments by newest
            if not self.select_newest_comments(driver):
                logger.warning(f"Failed to sort comments for post: {post_url}")
                return 0

            # JavaScript to check for end of comments - from original Bin/Spammer.py
            js_check_end_comment = r"""
            return (function() {
                // Selector for the end comment element
                const endCommentSelector = ".html-div.xdj266r.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x78zum5.x13a6bvl";
                const endCommentElement = document.querySelector(endCommentSelector);
                if (!endCommentElement) {
                    console.error("End comment element not found.");
                    return { canLoadMore: false, actionPerformed: false };
                }
                // Check if the end comment element has any nested elements
                const hasNestedElements = endCommentElement.children.length > 0;

                if (hasNestedElements) {
                    // Scroll the end comment element into view to trigger loading more comments
                    endCommentElement.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
                    console.log("Scrolled to the end comment element to load additional comments.");
                    return { canLoadMore: true, actionPerformed: true };
                } else {
                    console.log("No nested elements in end comment element. No more comments to load.");
                    return { canLoadMore: false, actionPerformed: false };
                }
            })();
            """

            # JavaScript to get reply buttons - exact copy from original Bin/Spammer.py
            js_get_replies = r"""
            return (function() {
                const knownPath = [0, 0, 1, 1, 1, 0, 2, 0, 0];
                const knownDepth = 10;
                const tailLength = 3;
                const knownTail = knownPath.slice(-tailLength);
                const TIMESTAMP_SELECTOR = 'a[role="link"][tabindex="0"][href*="/posts/"]';

                function getNodesAtDepth(root, targetDepth) {
                    const nodesAtDepth = [];
                    function dfs(node, level, path) {
                        if (level === targetDepth) {
                            nodesAtDepth.push({ node: node, path: path });
                            return;
                        }
                        Array.from(node.children).forEach((child, index) => {
                            dfs(child, level + 1, path.concat(index));
                        });
                    }
                    dfs(root, 1, []);
                    return nodesAtDepth;
                }

                const parent = document.querySelector(".html-div.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x1gslohp");
                if (!parent) return [];

                const comments = Array.from(parent.children).filter(child => !child.hasAttribute("class"));
                let replyDetails = [];

                comments.forEach((child, childIdx) => {
                    let detail = {
                        commentIndex: childIdx,
                        replyButton: null,
                        comment_id: `comment_${childIdx}`,
                        comment_text: "",
                        full_text: "N/A",
                        author_name: "Unknown",
                        author_profile_link: "#",
                        timestamp_value: "N/A",
                        timestamp_unit: "N/A",
                        timestamp_link: "N/A",
                        has_replies: false
                    };

                    // Text extraction
                    try {
                        detail.full_text = child.textContent
                            .replace(/\\s+/g, ' ')
                            .replace(/[\u200B-\u200D\uFEFF]/g, '')
                            .trim();

                        const textDiv = child.querySelector('div[dir="auto"]');
                        if (textDiv) {
                            detail.comment_text = textDiv.textContent
                                .replace(/[\\s\u200B-\u200D\uFEFF]+/g, ' ')
                                .trim();
                        }
                    } catch (e) {
                        console.error('Text extraction error:', e);
                    }

                    // Timestamp and comment ID processing
                    const timestampElement = child.querySelector(TIMESTAMP_SELECTOR);
                    if (timestampElement) {
                        try {
                            const url = new URL(timestampElement.href);
                            detail.comment_id = url.searchParams.get('comment_id') || detail.comment_id;
                            detail.timestamp_link = url.href.split('&')[0];

                            const timeMatch = timestampElement.textContent.match(/(\d+)\s*(\p{L}+)/u);
                            if (timeMatch) {
                                detail.timestamp_value = timeMatch[1];
                                detail.timestamp_unit = timeMatch[2];
                            }
                        } catch (e) {
                            console.error('Timestamp processing error:', e);
                        }
                    }

                    // Author information
                    const userProfile = child.querySelector('a[attributionsrc]');
                    if (userProfile) {
                        try {
                            detail.author_name = userProfile.textContent.trim();
                            const profilePath = new URL(userProfile.href).pathname.split('/');
                            detail.author_profile_link = profilePath[profilePath.indexOf('user') + 1] || "#";
                        } catch (e) {
                            console.error('Profile parsing error:', e);
                        }
                    }

                    // Reply detection
                    const firstNestedDiv = child.children[0];
                    if (firstNestedDiv) {
                        const secondNestedDiv = firstNestedDiv.children[1];
                        detail.has_replies = secondNestedDiv?.children?.length > 0;
                    }

                    // Find reply button
                    const nodesAtDepth = getNodesAtDepth(child, knownDepth);
                    nodesAtDepth.forEach(({ node, path }) => {
                        const nodeTail = path.slice(-tailLength);
                        if (JSON.stringify(nodeTail) === JSON.stringify(knownTail)) {
                            detail.replyButton = node;
                        }
                    });

                    replyDetails.push(detail);
                });

                return replyDetails;
            })();
            """

            # Implement the reply_to_comments function from Bin/Spammer.py
            comments_posted = 0
            processed_comments = set()
            # Maximum number of attempts without finding new comments before giving up
            no_new_comments_counter = 0
            max_no_new_comments_attempts = 3

            # Main loop to reply to comments
            while comments_posted < comments_per_post and self.is_running:
                # Load more comments if needed
                try:
                    load_result = driver.execute_script(js_check_end_comment)
                    can_load_more = load_result.get('canLoadMore', False)
                    action_performed = load_result.get('actionPerformed', False)
                    logger.info(f"Comments status: {'Available' if can_load_more else 'Exhausted'}, Action: {'Taken' if action_performed else 'None'}")

                    if can_load_more and action_performed:
                        self.random_sleep(0.5, 1)
                except WebDriverException as e:
                    logger.error(f"WebDriver error during load check: {e}")
                    can_load_more = False
                except Exception as e:
                    logger.error(f"Unexpected error during load check: {e}")
                    can_load_more = False

                if not can_load_more:
                    logger.info("No more comments available after maximum attempts.")
                    break

                # Process comments
                try:
                    # Get all reply buttons at once to minimize script execution time
                    reply_buttons = driver.execute_script(js_get_replies)
                    logger.info(f"Found {len(reply_buttons)} comments to process")

                    if not reply_buttons:
                        # Quick scroll to load more comments
                        logger.info("Scrolling to load more comments")
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        # Minimal wait - just enough for the page to respond
                        self.random_sleep(0.3, 0.5)
                        continue

                    new_comment_found = False

                    # Reset counter if we found comments to process
                    if reply_buttons:
                        no_new_comments_counter = 0

                    for detail in reply_buttons:
                        if not self.is_running or comments_posted >= comments_per_post:
                            break

                        comment_id = detail.get("comment_id")
                        if not comment_id:
                            logger.warning("No comment_id found for this comment. Skipping...")
                            continue

                        # Skip if already processed
                        if comment_id in processed_comments or self.check_comment_exists(comment_id):
                            continue

                        # Get author profile ID
                        author_id = detail.get("author_profile_link", "#")

                        # Try to reply to this comment
                        reply_button = detail.get("replyButton")
                        if reply_button:
                            try:
                                # Extract metadata for logging
                                metadata = {
                                    'index': detail.get("commentIndex"),
                                    'text': detail.get("comment_text", "N/A"),
                                    'author': detail.get("author_name", "Unknown"),
                                    'profile': detail.get("author_profile_link", "#"),
                                    'time_value': detail.get("timestamp_value", "N/A"),
                                    'time_unit': detail.get("timestamp_unit", "N/A"),
                                    'timestamp_link': detail.get("timestamp_link", "N/A"),
                                    'replies': detail.get("has_replies", False),
                                    'fulltext': detail.get("full_text", "N/A")
                                }

                                # Log comment details
                                text_preview = (metadata['text'][:75] + '...') if len(metadata['text']) > 75 else metadata['text']
                                logger.info(
                                    f"Comment Content:\n"
                                    f"Index: {metadata['index']}\n"
                                    f"Comment ID: {comment_id}\n"
                                    f"Profile ID: {metadata['profile']}\n"
                                    f"Comment Text: {text_preview}"
                                )

                                # Click the reply button with instant scrolling for faster response
                                driver.execute_script(
                                    "arguments[0].scrollIntoView({behavior: 'auto', block: 'center'});"
                                    "arguments[0].click();",
                                    reply_button
                                )
                                # Minimal wait - just enough for the comment box to appear
                                self.random_sleep(0.2, 0.3)

                                # Check if we've already posted the maximum number of comments
                                if comments_posted >= comments_per_post:
                                    logger.info(f"Reached the target of {comments_per_post} comments for this post. Moving to next post.")
                                    break

                                # Post comment
                                comment_text = random.choice(comments)
                                if self.post_comment(driver, comment_text):
                                    comments_posted += 1
                                    processed_comments.add(comment_id)
                                    self.save_comment(comment_id, author_id, post_url)
                                    logger.info(f"Posted comment {comments_posted}/{comments_per_post}")
                                    new_comment_found = True

                                    # Check if we've reached the target after posting
                                    if comments_posted >= comments_per_post:
                                        logger.info(f"Reached the target of {comments_per_post} comments for this post. Moving to next post.")
                                        break

                                # No need to wait here - we're ready to move to the next comment immediately
                            except WebDriverException as e:
                                logger.error(f"WebDriver error replying to comment ID {comment_id}: {e}")
                            except Exception as e:
                                logger.error(f"Unexpected error replying to comment ID {comment_id}: {e}")

                    # If we found new comments, try to load more - optimized for speed
                    if new_comment_found:
                        if driver.execute_script(js_check_end_comment).get('canLoadMore', False):
                            logger.info("Loading more comments")
                            # Use faster scrolling method
                            driver.execute_script("window.scrollTo(0, document.body.scrollHeight + 500);")
                            # Minimal wait - just enough for the page to respond
                            self.random_sleep(0.2, 0.3)
                        else:
                            logger.info("No additional comments found")
                            break
                    else:
                        # If no new comments found, increment counter
                        no_new_comments_counter += 1
                        logger.info(f"No new comments found. Attempt {no_new_comments_counter}/{max_no_new_comments_attempts}")

                        # If we've reached the maximum attempts, break
                        if no_new_comments_counter >= max_no_new_comments_attempts:
                            logger.info("Maximum attempts reached without finding new comments. Stopping.")
                            break

                        # Otherwise, try to load more comments
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight + 500);")
                        self.random_sleep(0.5, 1)

                except WebDriverException as e:
                    logger.error(f"WebDriver error during comment processing: {e}")
                    break
                except Exception as e:
                    logger.error(f"Unexpected error during comment processing: {e}")
                    break

            logger.info(f"Final result: {comments_posted}/{comments_per_post}.")
            return comments_posted
        except WebDriverException as e:
            logger.error(f"WebDriver error processing post {post_url}: {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error processing post {post_url}: {e}")
            return 0

    def process_profile(self, profile_id, posts, comments, comments_per_profile=3):
        """
        Process a single profile by posting comments on posts.

        Args:
            profile_id (str): Profile ID to process.
            posts (list): List of post URLs.
            comments (list): List of comments to choose from.
            comments_per_profile (int): Number of comments to post per profile.

        Returns:
            int: Number of comments successfully posted.
        """
        if not self.is_running:
            return 0

        self.current_profile = profile_id
        self.skip_current_profile = False

        logger.info(f"Processing profile: {profile_id}")
        logger.info(f"Target comments for this profile: {comments_per_profile}")

        # Kill any existing NstChrome processes first (like in original script)
        self.browser_manager.kill_nstchrome_processes()

        # Get existing browser instance or launch a new one if needed
        # This now matches the behavior of the original script in Bin/Spammer.py

        # Check if headless mode is enabled
        headless_mode = self.settings.get("headless", False)
        logger.info(f"Using headless mode: {headless_mode}")

        # Update browser_manager config with the current headless setting
        if "nstbrowser" in self.browser_manager.config:
            self.browser_manager.config["nstbrowser"]["headless_mode"] = headless_mode

        driver = self.browser_manager.get_browser(profile_id)
        if not driver:
            logger.error(f"Failed to get or launch browser for profile {profile_id}.")
            return 0

        self.current_driver = driver

        # Check if logged in
        if not self.browser_manager.is_logged_in(driver):
            logger.warning(f"Profile {profile_id} is not logged in to Facebook")
            self.browser_manager.close_browser(driver)
            return 0

        comments_posted = 0
        post_index = 0
        max_post_attempts = len(posts) * 3  # Increased from 2 to 3 to give more attempts per post
        post_attempts = 0
        processed_posts = set()  # Track which posts we've already processed
        consecutive_failures = 0  # Track consecutive failures to detect stalled progress
        max_consecutive_failures = 3  # Maximum allowed consecutive failures before trying a different post

        # Get post selection mode from settings
        post_selection_mode = self.settings.get("post_selection_mode", "Sequential")
        logger.info(f"Using post selection mode: {post_selection_mode}")

        # Get max posts setting
        max_posts = self.settings.get("max_posts", 0)
        logger.info(f"Max posts to process: {max_posts if max_posts > 0 else 'ALL'}")

        # Create a copy of posts list for processing
        posts_to_process = posts.copy()

        # If max_posts is set, limit the number of posts
        if max_posts > 0 and len(posts_to_process) > max_posts:
            posts_to_process = posts_to_process[:max_posts]
            logger.info(f"Limited posts to process to {max_posts} posts")

        # If random mode, shuffle the posts
        if post_selection_mode == "Random":
            import random
            random.shuffle(posts_to_process)
            logger.info("Posts shuffled for random selection mode")

        try:
            # Continue until we've posted the exact number of comments or reached the maximum attempts
            while comments_posted < comments_per_profile and post_attempts < max_post_attempts and self.is_running and not self.skip_current_profile:
                # If we've gone through all posts, start over
                if post_index >= len(posts_to_process):
                    post_index = 0
                    logger.info("Reached end of posts list, starting over")
                    # Clear processed posts if we've gone through all posts but still need to post more comments
                    if len(processed_posts) >= len(posts_to_process) and comments_posted < comments_per_profile:
                        logger.info("Processed all posts but still need more comments. Resetting processed posts tracking.")
                        processed_posts.clear()

                        # If random mode, shuffle the posts again for a different order
                        if post_selection_mode == "Random":
                            random.shuffle(posts_to_process)
                            logger.info("Posts reshuffled for random selection mode")

                post_url = posts_to_process[post_index]

                # Skip this post if we've already processed it and there are still unprocessed posts
                if post_url in processed_posts and len(processed_posts) < len(posts_to_process):
                    logger.info(f"Skipping already processed post: {post_url}")
                    post_index += 1
                    continue

                logger.info(f"Processing post {post_index + 1}/{len(posts_to_process)}: {post_url}")
                logger.info(f"Comments posted so far: {comments_posted}/{comments_per_profile}")

                # Process the post and get the number of comments posted
                new_comments = self.process_post(
                    driver,
                    post_url,
                    comments,
                    comments_per_profile - comments_posted  # Only post the remaining number of comments
                )

                # Track if we were successful
                if new_comments > 0:
                    comments_posted += new_comments
                    logger.info(f"Posted {new_comments} new comments on this post. Total: {comments_posted}/{comments_per_profile}")
                    processed_posts.add(post_url)  # Mark this post as processed
                    consecutive_failures = 0  # Reset consecutive failures counter
                elif new_comments == -1:  # Special code for deleted/unavailable posts
                    logger.warning(f"Post is unavailable (deleted or restricted): {post_url}")
                    processed_posts.add(post_url)  # Mark this post as processed to avoid retrying
                    # Don't increment consecutive_failures as this is not a failure of our system
                else:
                    logger.warning(f"Failed to post any comments on post: {post_url}")
                    consecutive_failures += 1

                    # If we've had too many consecutive failures, try a different post
                    if consecutive_failures >= max_consecutive_failures:
                        logger.warning(f"Too many consecutive failures ({consecutive_failures}). Trying a different post.")
                        consecutive_failures = 0  # Reset counter
                        # Jump ahead by a few posts to try to find a working one
                        post_index = (post_index + 3) % len(posts_to_process)
                        continue

                post_index = (post_index + 1) % len(posts_to_process)  # Use modulo to wrap around
                post_attempts += 1

            # Check if we've posted the exact number of comments or if we've been stopped
            if comments_posted >= comments_per_profile:
                logger.info(f"Successfully posted all {comments_per_profile} comments for profile {profile_id}")
            elif self.skip_current_profile:
                logger.info(f"Skipped profile {profile_id} after posting {comments_posted}/{comments_per_profile} comments")
            elif not self.is_running:
                logger.info(f"Spammer stopped after posting {comments_posted}/{comments_per_profile} comments for profile {profile_id}")
            else:
                logger.warning(f"Could not post all comments for profile {profile_id}. Posted {comments_posted}/{comments_per_profile}")

            return comments_posted
        except WebDriverException as e:
            logger.error(f"WebDriver error processing profile {profile_id}: {e}")
            return comments_posted
        except Exception as e:
            logger.error(f"Unexpected error processing profile {profile_id}: {e}")
            return comments_posted
        finally:
            # Check if auto_close is enabled (from original Spammer.py)
            auto_close = self.settings.get("auto_close", True)

            if auto_close:
                # Close browser if auto_close is enabled
                logger.info(f"Auto-closing browser for profile {profile_id}")
                self.browser_manager.close_browser(driver)
            else:
                logger.info(f"Auto-close disabled, leaving browser open for profile {profile_id}")

            # Reset current driver and profile references
            self.current_driver = None
            self.current_profile = None

    def start(self, profiles=None, posts=None, comments=None, comments_per_profile=3, callback=None):
        """
        Start the spamming process.

        Args:
            profiles (list, optional): List of profile IDs. If None, load from file.
            posts (list, optional): List of post URLs. If None, load from file.
            comments (list, optional): List of comments. If None, load from file.
            comments_per_profile (int): Number of comments to post per profile.
            callback (function, optional): Callback function to report progress.
                The callback function should accept the following parameters:
                - profile_index (int): Current profile index.
                - total_profiles (int): Total number of profiles.
                - comments_posted (int): Number of comments posted for current profile.
                - total_comments_posted (int): Total number of comments posted.
                - status (str): Current status message.

        Returns:
            bool: True if successful, False otherwise.
        """
        if self.is_running:
            logger.warning("Spammer is already running.")
            return False

        self.is_running = True

        # Load data if not provided
        if profiles is None:
            profiles = self.load_profiles()

        if posts is None:
            posts = self.load_posts()

        if comments is None:
            comments = self.load_comments()

        # Validate data
        if not profiles:
            logger.error("No profiles loaded.")
            self.is_running = False
            return False

        if not posts:
            logger.error("No posts loaded.")
            self.is_running = False
            return False

        if not comments:
            logger.error("No comments loaded.")
            self.is_running = False
            return False

        # Connect to database
        if not self.db_connection:
            self.connect_to_database()

        # Process profiles
        total_comments_posted = 0

        try:
            # Check if we should use the first profile for selector detection
            use_first_profile_for_selectors = self.settings.get("use_first_profile_for_selectors", True)
            selectors_detection_complete = self.settings.get("selectors_detection_complete", False)

            if use_first_profile_for_selectors and not selectors_detection_complete and len(profiles) > 0:
                # Use the first profile to detect selectors for all posts
                first_profile_id = profiles[0]
                logger.info(f"Using first profile {first_profile_id} to detect selectors for all posts")

                if callback:
                    callback(
                        profile_index=0,
                        total_profiles=len(profiles),
                        comments_posted=0,
                        total_comments_posted=0,
                        status=f"Detecting selectors using profile {first_profile_id}"
                    )

                # Detect selectors for all posts
                selector_detection_success = self.detect_selectors_for_all_posts(first_profile_id, posts)

                if not selector_detection_success:
                    logger.warning("Selector detection failed. Will proceed with existing selectors.")
                else:
                    logger.info("Selector detection completed successfully. Proceeding with commenting.")

                # Update progress
                if callback:
                    callback(
                        profile_index=0,
                        total_profiles=len(profiles),
                        comments_posted=0,
                        total_comments_posted=0,
                        status=f"Selector detection completed. Starting commenting process."
                    )

            # Now process all profiles for commenting
            for i, profile_id in enumerate(profiles):
                if not self.is_running:
                    break

                # Skip the first profile if it was already used for selector detection and we don't want to use it for commenting
                if i == 0 and use_first_profile_for_selectors and not self.settings.get("use_first_profile_for_commenting", True):
                    logger.info(f"Skipping first profile {profile_id} for commenting as it was used for selector detection")
                    continue

                # Report progress
                if callback:
                    callback(
                        profile_index=i,
                        total_profiles=len(profiles),
                        comments_posted=0,
                        total_comments_posted=total_comments_posted,
                        status=f"Processing profile {i+1}/{len(profiles)}: {profile_id}"
                    )

                try:
                    # Process profile
                    comments_posted = self.process_profile(
                        profile_id,
                        posts,
                        comments,
                        comments_per_profile
                    )

                    total_comments_posted += comments_posted

                    # Report progress
                    if callback:
                        callback(
                            profile_index=i,
                            total_profiles=len(profiles),
                            comments_posted=comments_posted,
                            total_comments_posted=total_comments_posted,
                            status=f"Completed profile {i+1}/{len(profiles)}: {profile_id}"
                        )
                except Exception as e:
                    logger.error(f"Error processing profile {profile_id}: {e}")
                    # Continue with next profile
                    if callback:
                        callback(
                            profile_index=i,
                            total_profiles=len(profiles),
                            comments_posted=0,
                            total_comments_posted=total_comments_posted,
                            status=f"Error with profile {i+1}/{len(profiles)}: {profile_id}"
                        )

            logger.info(f"Spamming completed. Posted {total_comments_posted} comments.")
            return True
        except Exception as e:
            logger.error(f"Unexpected error in spamming process: {e}")
            return False
        finally:
            self.is_running = False

    def detect_selectors_for_all_posts(self, profile_id, posts):
        """
        Use the first profile to detect selectors for all posts before starting the commenting process.

        Args:
            profile_id (str): Profile ID to use for selector detection.
            posts (list): List of post URLs to scan for selectors.

        Returns:
            bool: True if successful, False otherwise.
        """
        if not self.is_running:
            return False

        # Get the selector detection time from settings (default to 30 seconds if not specified)
        selector_detection_time = self.settings.get("selector_detection_time", 30)
        logger.info(f"Starting selector detection using profile {profile_id}")
        logger.info(f"Will scan {len(posts)} posts for selectors with {selector_detection_time} seconds per post")

        # Kill any existing NstChrome processes first
        self.browser_manager.kill_nstchrome_processes()

        # Check if headless mode is enabled
        headless_mode = self.settings.get("headless", False)
        logger.info(f"Using headless mode for selector detection: {headless_mode}")

        # Update browser_manager config with the current headless setting
        if "nstbrowser" in self.browser_manager.config:
            self.browser_manager.config["nstbrowser"]["headless_mode"] = headless_mode

        # Get browser instance
        driver = self.browser_manager.get_browser(profile_id)
        if not driver:
            logger.error(f"Failed to get browser for profile {profile_id}.")
            return False

        self.current_driver = driver
        self.current_profile = profile_id

        # Check if logged in
        if not self.browser_manager.is_logged_in(driver):
            logger.warning(f"Profile {profile_id} is not logged in to Facebook")
            self.browser_manager.close_browser(driver)
            self.current_driver = None
            self.current_profile = None
            return False

        # Create SelectorManager instance if not already created
        if not hasattr(self, 'selector_manager'):
            from .selector_manager import SelectorManager
            self.selector_manager = SelectorManager()

        # Track if we found any new selectors
        new_selectors_found = False

        # Get post selection mode from settings
        post_selection_mode = self.settings.get("post_selection_mode", "Sequential")
        logger.info(f"Using post selection mode: {post_selection_mode} for selector detection")

        # Get max posts setting
        max_posts = self.settings.get("max_posts", 0)
        logger.info(f"Max posts to process for selector detection: {max_posts if max_posts > 0 else 'ALL'}")

        # Create a copy of posts list for processing
        posts_to_process = posts.copy()

        # If max_posts is set, limit the number of posts
        if max_posts > 0 and len(posts_to_process) > max_posts:
            posts_to_process = posts_to_process[:max_posts]
            logger.info(f"Limited posts for selector detection to {max_posts} posts")

        # If random mode, shuffle the posts
        if post_selection_mode == "Random":
            import random
            random.shuffle(posts_to_process)
            logger.info("Posts shuffled for random selection mode during selector detection")

        try:
            # Process each post to detect selectors
            for i, post_url in enumerate(posts_to_process):
                if not self.is_running:
                    break

                logger.info(f"Scanning post {i+1}/{len(posts_to_process)} for selectors: {post_url}")

                try:
                    # Navigate to post
                    logger.info(f"Navigating to post: {post_url}")
                    driver.get(post_url)

                    # Increase initial wait time to ensure page is fully loaded
                    logger.info(f"Waiting for post to load: {post_url}")
                    self.random_sleep(5, 8)  # Increased from 3-5 to 5-8 seconds

                    # Check if post is available
                    if ("This content isn't available" in driver.page_source or
                        "content isn't available right now" in driver.page_source or
                        "When this happens, it's usually because the owner only shared it with a small group of people" in driver.page_source):
                        logger.warning(f"Post not available (deleted or restricted): {post_url}")
                        continue

                    # Try to maximize visibility of elements
                    driver.execute_script("""
                        // Attempt to reveal any hidden elements
                        document.querySelectorAll('[style*="visibility: hidden"]').forEach(el => {
                            el.style.visibility = 'visible';
                        });
                        document.querySelectorAll('[style*="display: none"]').forEach(el => {
                            el.style.display = 'block';
                        });
                    """)

                    # Additional wait after revealing elements
                    self.random_sleep(2, 3)

                    # Sort comments by newest to ensure we see the latest UI elements
                    logger.info(f"Sorting comments by newest for post: {post_url}")
                    sort_attempts = 0
                    max_sort_attempts = 3

                    while sort_attempts < max_sort_attempts:
                        if self.select_newest_comments(driver):
                            logger.info(f"Successfully sorted comments for post: {post_url}")
                            break
                        else:
                            sort_attempts += 1
                            logger.warning(f"Failed to sort comments for post: {post_url} (attempt {sort_attempts}/{max_sort_attempts})")

                            # Try scrolling a bit to reveal the sort button
                            driver.execute_script("window.scrollBy(0, 200);")
                            self.random_sleep(2, 3)

                            if sort_attempts >= max_sort_attempts:
                                logger.warning(f"Failed to sort comments after {max_sort_attempts} attempts for post: {post_url}")
                                # Continue anyway, we might still find selectors

                    # Wait after sorting to ensure UI updates
                    self.random_sleep(3, 5)

                    # Detect selectors for this post
                    logger.info(f"Detecting selectors for post: {post_url}")
                    selectors_updated = self.selector_manager.detect_selectors(driver)

                    if selectors_updated:
                        logger.info(f"New selectors found in post: {post_url}")
                        new_selectors_found = True
                    else:
                        logger.info(f"No new selectors found in post: {post_url}")

                    # Calculate how many scroll attempts based on the selector detection time
                    # Each scroll attempt takes about 5-8 seconds, so we'll divide the total time by 6
                    scroll_attempts = max(3, selector_detection_time // 6)  # Minimum 3 attempts

                    # Scroll down to load more content and check for different UI elements
                    logger.info(f"Will perform {scroll_attempts} scroll attempts for post: {post_url} based on detection time of {selector_detection_time} seconds")

                    for scroll_attempt in range(scroll_attempts):
                        logger.info(f"Scroll attempt {scroll_attempt+1}/{scroll_attempts} for post: {post_url}")

                        # Use a more robust scrolling method
                        driver.execute_script("""
                            window.scrollTo(0, document.body.scrollHeight);
                            // Try to reveal any hidden elements
                            document.querySelectorAll('[style*="display: none"]').forEach(el => {
                                el.style.display = '';
                            });
                            // Try to reveal any hidden elements with visibility
                            document.querySelectorAll('[style*="visibility: hidden"]').forEach(el => {
                                el.style.visibility = 'visible';
                            });
                        """)

                        # Adjust wait time based on total detection time and number of attempts
                        wait_time = max(2, min(5, selector_detection_time // (scroll_attempts * 2)))
                        self.random_sleep(wait_time, wait_time + 2)

                        # Try to detect selectors again after scrolling
                        selectors_updated = self.selector_manager.detect_selectors(driver)
                        if selectors_updated:
                            logger.info(f"New selectors found after scrolling (attempt {scroll_attempt+1}) in post: {post_url}")
                            new_selectors_found = True

                        # Additional scroll to middle of page to reveal potentially hidden elements
                        if scroll_attempt % 2 == 1:  # On odd-numbered attempts
                            driver.execute_script("""
                                window.scrollTo(0, document.body.scrollHeight / 2);
                                // Try to click on any 'See more' or 'Show more' buttons
                                document.querySelectorAll('div[role="button"]').forEach(el => {
                                    if (el.textContent.includes('See more') || el.textContent.includes('Show more')) {
                                        el.click();
                                    }
                                });
                            """)
                            self.random_sleep(wait_time, wait_time + 1)

                            # Check for selectors again
                            if self.selector_manager.detect_selectors(driver):
                                logger.info(f"New selectors found after mid-page scrolling in post: {post_url}")
                                new_selectors_found = True

                except Exception as e:
                    logger.error(f"Error scanning post {post_url} for selectors: {e}")
                    continue

            # Mark selector detection as complete
            self.settings["selectors_detection_complete"] = True
            logger.info(f"Selector detection completed. New selectors found: {new_selectors_found}")
            return True

        except Exception as e:
            logger.error(f"Error during selector detection process: {e}")
            return False
        finally:
            # Close browser
            if self.current_driver:
                self.browser_manager.close_browser(self.current_driver)
                self.current_driver = None
                self.current_profile = None

    def stop(self):
        """
        Stop the spamming process.

        Returns:
            bool: True if successful, False otherwise.
        """
        if not self.is_running:
            logger.warning("Spammer is not running.")
            return False

        self.is_running = False

        # Close current browser if any
        if self.current_profile and self.current_driver:
            self.browser_manager.close_browser(self.current_driver)
            self.current_driver = None
            self.current_profile = None

        logger.info("Spamming stopped.")
        return True

    def skip_profile(self):
        """
        Skip the current profile.

        Returns:
            bool: True if successful, False otherwise.
        """
        if not self.is_running or not self.current_profile:
            logger.warning("No profile to skip.")
            return False

        self.skip_current_profile = True
        logger.info(f"Skipping profile {self.current_profile}")
        return True

    def close(self):
        """
        Close all resources.

        Returns:
            bool: True if successful, False otherwise.
        """
        # Stop if running
        if self.is_running:
            self.stop()

        # Close all browsers
        self.browser_manager.close_all_browsers()

        # Close database connection
        if self.db_connection:
            self.db_connection.close()
            self.db_connection = None

        logger.info("Spammer resources closed.")
        return True
