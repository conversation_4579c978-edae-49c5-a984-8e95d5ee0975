/**
 * Advanced Facebook Post Links Extractor
 */
const FacebookPostLinkExtractor = (function() {
    // Configuration
    const config = {
        minDelay: 2000,
        maxDelay: 5000,
        maxAttempts: 25,
        scrollStep: 800,
        maxPosts: 3000  // Establecido en 3000 como máximo
    };

    const state = {
        collectedPosts: [],
        processedElements: new WeakSet(),
        attempts: 0
    };

    // Helper functions
    const helpers = {
        delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

        randomDelay: function() {
            const delay = config.minDelay + Math.random() * (config.maxDelay - config.minDelay);
            console.log(`Wait ${(delay/1000).toFixed(1)} seconds to avoid restrictions`);
            return this.delay(delay);
        },

        cleanText: (text) => text ? text.replace(/\s+/g, ' ').trim() : '',

        smartScroll: async function() {
            const currentPos = window.scrollY;
            const remaining = document.documentElement.scrollHeight - window.innerHeight - currentPos;

            if (remaining > 0) {
                const scrollTo = currentPos + Math.min(remaining, config.scrollStep);
                window.scrollTo({
                    top: scrollTo,
                    behavior: 'smooth'
                });
                await this.delay(1000 + Math.random() * 2000);
            }
        }
    };

    // Data extraction functions
    const extractors = {
        findPostElements: function() {
            const selectors = [
                '[role="article"]', // Primary elements
                'div[data-pagelet^="Feed"] > div > div', // Fallback
                'div[class*="userContentWrapper"]' // Additional fallback
            ];

            let results = [];
            for (const selector of selectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results = Array.from(elements).filter(el => !state.processedElements.has(el));
                        break;
                    }
                } catch(e) { console.error(e); }
            }
            return results;
        },

        extractPostLink: function(element) {
            // First attempt: Look in time/date element
            const timeSelectors = [
                'a[href*="/posts/"][role="link"]', // New structure
                'a[href*="/permalink/"]', // Alternative structure
                'a[aria-label*="post"]', // Alternative description
                'abbr + a', // Element next to time marker
                'span[data-utime] a' // Element with time data
            ];

            for (const selector of timeSelectors) {
                try {
                    const linkElement = element.querySelector(selector);
                    if (linkElement) {
                        const href = linkElement.getAttribute('href');
                        if (href) {
                            return href.includes('facebook.com') ? href : `https://facebook.com${href}`;
                        }
                    }
                } catch(e) { console.error(e); }
            }

            // Second attempt: Search anywhere in post
            const backupSelectors = [
                'a[href*="/posts/"]',
                'a[href*="/story.php"]'
            ];

            for (const selector of backupSelectors) {
                try {
                    const links = element.querySelectorAll(selector);
                    for (const link of links) {
                        const href = link.getAttribute('href');
                        if (href && href.match(/\/posts\/|\/permalink\//)) {
                            return href.includes('facebook.com') ? href : `https://facebook.com${href}`;
                        }
                    }
                } catch(e) { console.error(e); }
            }

            return null;
        },

        extractFullPostData: async function(element) {
            const postLink = this.extractPostLink(element);
            if (!postLink) return null;

            const postId = postLink.split('/posts/')[1]?.split('/')[0] ||
                          postLink.split('/permalink/')[1]?.split('/')[0] ||
                          Math.random().toString(36).substring(2, 15);

            // Extract basic data
            const authorElement = element.querySelector('[data-testid="story_actor_line"] a') ||
                                element.querySelector('a[href*="/user/"], a[href*="/profile/"]');

            const contentElement = element.querySelector('[data-testid="post_message"]') ||
                                 element.querySelector('div[dir="auto"]');

            const timeElement = element.querySelector('abbr[data-utime]') ||
                              element.querySelector('a[aria-label*="post"]');

            return {
                postId,
                url: postLink,
                author: {
                    name: authorElement?.textContent?.trim() || 'Unknown',
                    profile: authorElement?.href || ''
                },
                content: contentElement?.textContent?.trim() || '',
                timestamp: timeElement?.getAttribute('data-utime') ||
                          timeElement?.getAttribute('aria-label') || '',
                images: Array.from(element.querySelectorAll('img[src*="scontent-"]')).map(img => img.src)
            };
        }
    };

    // Main interface
    return {
        async start() {
            console.log('Starting post links extraction...');

            while (state.attempts < config.maxAttempts && state.collectedPosts.length < config.maxPosts) {
                await helpers.randomDelay();

                const posts = extractors.findPostElements();
                if (posts.length === 0) {
                    state.attempts++;
                    console.log(`No new posts found (Attempt ${state.attempts}/${config.maxAttempts})`);
                    await helpers.smartScroll();
                    continue;
                }

                state.attempts = 0;
                for (const post of posts) {
                    try {
                        const postData = await extractors.extractFullPostData(post);
                        if (postData) {
                            state.collectedPosts.push(postData);
                            state.processedElements.add(post);
                            console.log('Extracted:', postData.url);

                            // Check if we've reached the maximum number of posts
                            if (state.collectedPosts.length >= config.maxPosts) {
                                console.log(`Reached maximum number of posts (${config.maxPosts})`);
                                break;
                            }
                        }
                    } catch(e) {
                        console.error('Extraction error:', e);
                    }
                    await helpers.delay(500 + Math.random() * 1000);
                }

                // Check again if we've reached the maximum number of posts
                if (state.collectedPosts.length >= config.maxPosts) {
                    break;
                }

                await helpers.smartScroll();
            }

            this.downloadResults();
        },

        downloadResults() {
            if (state.collectedPosts.length === 0) {
                console.log('No data to download');
                return;
            }

            const blob = new Blob([JSON.stringify(state.collectedPosts, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `facebook_posts_${new Date().toISOString()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // Don't log downloading photos
            console.log(`Processed ${state.collectedPosts.length} posts`);
        },

        // Method to get the collected posts (for use with Selenium)
        getCollectedPosts() {
            return state.collectedPosts;
        },

        // Method to set the maximum number of posts
        setMaxPosts(maxPosts) {
            config.maxPosts = maxPosts;
            console.log(`Maximum posts set to ${maxPosts}`);
        }
    };
})();

// Safe execution after page load
if (document.readyState === 'complete') {
    setTimeout(() => FacebookPostLinkExtractor.start(), 3000);
} else {
    window.addEventListener('load', () => {
        setTimeout(() => FacebookPostLinkExtractor.start(), 3000);
    });
}

// Export for use with Selenium
if (typeof module !== 'undefined') {
    module.exports = FacebookPostLinkExtractor;
}
