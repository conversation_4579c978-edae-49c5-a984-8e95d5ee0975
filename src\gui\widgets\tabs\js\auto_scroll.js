/**
 * Scroll down the page to load more posts
 * @param {number} scrollCount - Number of times to scroll
 * @param {number} scrollDelay - Delay between scrolls in milliseconds
 * @returns {Promise} Promise that resolves when scrolling is complete
 */
function autoScroll(scrollCount, scrollDelay) {
    return new Promise((resolve) => {
        let scrolls = 0;
        
        function scroll() {
            if (scrolls >= scrollCount) {
                resolve(true);
                return;
            }
            
            // Scroll down
            window.scrollTo(0, document.body.scrollHeight);
            scrolls++;
            
            // Log progress
            console.log(`Scroll ${scrolls}/${scrollCount} completed`);
            
            // Wait for content to load
            setTimeout(scroll, scrollDelay);
        }
        
        // Start scrolling
        scroll();
    });
}

// Execute the function with parameters
return autoScroll(arguments[0], arguments[1]);
