"""
Module for handling dynamic pages like Facebook and Twitter.
Provides utilities for interacting with constantly changing pages.
"""

import time
import logging
import traceback
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException, 
    StaleElementReferenceException, 
    NoSuchElementException,
    ElementNotInteractableException
)

class DynamicPageHandler:
    """Handler for dynamic pages like Facebook and Twitter."""
    
    def __init__(self, driver, logger=None):
        """Initialize the handler with a WebDriver instance.
        
        Args:
            driver: Selenium WebDriver instance
            logger: Logger instance (optional)
        """
        self.driver = driver
        self.logger = logger or logging.getLogger(__name__)
        
    def find_element_with_retry(self, locator_strategies, max_attempts=3, wait_time=10):
        """Find an element using multiple locator strategies with retry logic.
        
        Args:
            locator_strategies: List of tuples (By.X, "locator")
            max_attempts: Maximum number of retry attempts
            wait_time: Wait time in seconds for each attempt
            
        Returns:
            WebElement if found, None otherwise
        """
        for attempt in range(max_attempts):
            for by, locator in locator_strategies:
                try:
                    self.logger.debug(f"Attempt {attempt+1}/{max_attempts}: Finding element with {by}={locator}")
                    element = WebDriverWait(self.driver, wait_time).until(
                        EC.presence_of_element_located((by, locator))
                    )
                    if element.is_displayed():
                        self.logger.info(f"Found element with {by}={locator}")
                        return element
                except (TimeoutException, StaleElementReferenceException, NoSuchElementException) as e:
                    self.logger.debug(f"Failed to find element with {by}={locator}: {str(e)}")
                    continue
            
            # If we get here, none of the strategies worked on this attempt
            self.logger.warning(f"Attempt {attempt+1}/{max_attempts}: No locator strategy worked")
            
            # Wait before retrying
            if attempt < max_attempts - 1:
                time.sleep(2)
                # Refresh the page on the last attempt
                if attempt == max_attempts - 2:
                    self.logger.info("Refreshing page before final attempt")
                    self.driver.refresh()
                    time.sleep(3)
        
        self.logger.error(f"Failed to find element after {max_attempts} attempts with any strategy")
        return None
    
    def click_with_retry(self, element, max_attempts=3, use_js=True):
        """Click an element with retry logic.
        
        Args:
            element: WebElement to click
            max_attempts: Maximum number of retry attempts
            use_js: Whether to try JavaScript click as fallback
            
        Returns:
            bool: True if click was successful, False otherwise
        """
        for attempt in range(max_attempts):
            try:
                self.logger.debug(f"Attempt {attempt+1}/{max_attempts}: Clicking element")
                
                # Try regular click first
                try:
                    element.click()
                    self.logger.info("Successfully clicked element")
                    return True
                except (ElementNotInteractableException, StaleElementReferenceException) as e:
                    self.logger.debug(f"Regular click failed: {str(e)}")
                    
                    # Try JavaScript click as fallback
                    if use_js:
                        self.logger.debug("Trying JavaScript click")
                        self.driver.execute_script("arguments[0].click();", element)
                        self.logger.info("Successfully clicked element using JavaScript")
                        return True
            
            except Exception as e:
                self.logger.warning(f"Click attempt {attempt+1} failed: {str(e)}")
                if attempt < max_attempts - 1:
                    time.sleep(1)
        
        self.logger.error(f"Failed to click element after {max_attempts} attempts")
        return False
    
    def wait_for_page_load(self, timeout=30):
        """Wait for page to load completely.
        
        Args:
            timeout: Maximum wait time in seconds
            
        Returns:
            bool: True if page loaded, False otherwise
        """
        try:
            self.logger.debug(f"Waiting for page to load (timeout: {timeout}s)")
            WebDriverWait(self.driver, timeout).until(
                lambda d: d.execute_script('return document.readyState') == 'complete'
            )
            # Additional wait for dynamic content
            time.sleep(2)
            self.logger.info("Page loaded successfully")
            return True
        except TimeoutException:
            self.logger.error("Timeout waiting for page to load")
            return False
    
    def wait_for_ajax_complete(self, timeout=30):
        """Wait for all AJAX requests to complete.
        
        Args:
            timeout: Maximum wait time in seconds
            
        Returns:
            bool: True if AJAX completed, False otherwise
        """
        try:
            self.logger.debug(f"Waiting for AJAX requests to complete (timeout: {timeout}s)")
            WebDriverWait(self.driver, timeout).until(
                lambda d: d.execute_script('return (typeof jQuery !== "undefined") ? jQuery.active == 0 : true')
            )
            self.logger.info("AJAX requests completed")
            return True
        except TimeoutException:
            self.logger.error("Timeout waiting for AJAX requests to complete")
            return False
    
    def scroll_to_element(self, element, align_to_top=True):
        """Scroll to make an element visible.
        
        Args:
            element: WebElement to scroll to
            align_to_top: Whether to align element to top of viewport
            
        Returns:
            bool: True if scroll was successful, False otherwise
        """
        try:
            self.logger.debug("Scrolling to element")
            self.driver.execute_script(
                "arguments[0].scrollIntoView(arguments[1]);", 
                element, 
                align_to_top
            )
            # Give time for any animations to complete
            time.sleep(1)
            self.logger.info("Scrolled to element successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to scroll to element: {str(e)}")
            return False
    
    def scroll_page(self, amount=300, direction="down"):
        """Scroll the page by a specific amount.
        
        Args:
            amount: Number of pixels to scroll
            direction: "up" or "down"
            
        Returns:
            bool: True if scroll was successful, False otherwise
        """
        try:
            scroll_value = amount if direction == "down" else -amount
            self.logger.debug(f"Scrolling {direction} by {amount} pixels")
            self.driver.execute_script(f"window.scrollBy(0, {scroll_value});")
            time.sleep(0.5)
            return True
        except Exception as e:
            self.logger.error(f"Failed to scroll page: {str(e)}")
            return False
    
    def infinite_scroll(self, max_scrolls=10, scroll_pause_time=2):
        """Perform infinite scrolling to load more content.
        
        Args:
            max_scrolls: Maximum number of scrolls
            scroll_pause_time: Time to pause between scrolls
            
        Returns:
            int: Number of successful scrolls performed
        """
        self.logger.info(f"Starting infinite scroll (max: {max_scrolls} scrolls)")
        scrolls_performed = 0
        
        # Get initial scroll height
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        
        for i in range(max_scrolls):
            # Scroll down to bottom
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            scrolls_performed += 1
            
            # Wait to load page
            time.sleep(scroll_pause_time)
            
            # Calculate new scroll height and compare with last scroll height
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            
            self.logger.debug(f"Scroll {i+1}/{max_scrolls}: Old height: {last_height}, New height: {new_height}")
            
            if new_height == last_height:
                # If heights are the same, content might be fully loaded
                self.logger.info(f"No new content loaded after scroll {i+1}, stopping")
                break
                
            last_height = new_height
        
        self.logger.info(f"Completed {scrolls_performed} scrolls")
        return scrolls_performed
    
    def execute_js_function(self, js_function, *args):
        """Execute a JavaScript function with arguments.
        
        Args:
            js_function: JavaScript function as string
            *args: Arguments to pass to the function
            
        Returns:
            Result of the JavaScript execution
        """
        try:
            self.logger.debug(f"Executing JavaScript function with {len(args)} arguments")
            script = f"return ({js_function})({','.join(['arguments[' + str(i) + ']' for i in range(len(args))])});"
            result = self.driver.execute_script(script, *args)
            self.logger.info("JavaScript function executed successfully")
            return result
        except Exception as e:
            self.logger.error(f"Failed to execute JavaScript function: {str(e)}")
            self.logger.debug(f"JavaScript error details: {traceback.format_exc()}")
            return None
    
    def wait_for_element_state(self, element, state="visible", timeout=10):
        """Wait for an element to be in a specific state.
        
        Args:
            element: WebElement to wait for
            state: "visible", "clickable", "invisible", "selected", etc.
            timeout: Maximum wait time in seconds
            
        Returns:
            bool: True if element reached the state, False otherwise
        """
        try:
            self.logger.debug(f"Waiting for element to be {state} (timeout: {timeout}s)")
            
            if state == "visible":
                WebDriverWait(self.driver, timeout).until(
                    EC.visibility_of(element)
                )
            elif state == "clickable":
                WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable(element)
                )
            elif state == "invisible":
                WebDriverWait(self.driver, timeout).until(
                    EC.invisibility_of_element(element)
                )
            elif state == "selected":
                WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_selected(element)
                )
            else:
                self.logger.error(f"Unknown state: {state}")
                return False
                
            self.logger.info(f"Element is now {state}")
            return True
        except TimeoutException:
            self.logger.error(f"Timeout waiting for element to be {state}")
            return False
        except Exception as e:
            self.logger.error(f"Error waiting for element state: {str(e)}")
            return False
    
    def handle_dynamic_content(self, content_locator_strategies, action_function, max_attempts=3):
        """Handle dynamic content by finding elements and performing actions.
        
        Args:
            content_locator_strategies: List of tuples (By.X, "locator") to find content
            action_function: Function to call with each found element
            max_attempts: Maximum number of retry attempts
            
        Returns:
            list: Results from action_function for each element
        """
        results = []
        attempt = 0
        
        while attempt < max_attempts:
            try:
                # Find all matching elements
                elements = []
                for by, locator in content_locator_strategies:
                    try:
                        found_elements = self.driver.find_elements(by, locator)
                        elements.extend(found_elements)
                        self.logger.debug(f"Found {len(found_elements)} elements with {by}={locator}")
                    except Exception as e:
                        self.logger.debug(f"Error finding elements with {by}={locator}: {str(e)}")
                
                if not elements:
                    self.logger.warning(f"Attempt {attempt+1}/{max_attempts}: No elements found")
                    attempt += 1
                    time.sleep(2)
                    continue
                
                # Process each element
                for element in elements:
                    try:
                        if element.is_displayed():
                            result = action_function(element)
                            results.append(result)
                    except StaleElementReferenceException:
                        self.logger.debug("Element became stale, continuing to next element")
                        continue
                    except Exception as e:
                        self.logger.debug(f"Error processing element: {str(e)}")
                        continue
                
                # If we processed at least some elements, consider it a success
                if results:
                    self.logger.info(f"Successfully processed {len(results)} elements")
                    break
                
                attempt += 1
                time.sleep(2)
                
            except Exception as e:
                self.logger.error(f"Error in handle_dynamic_content: {str(e)}")
                attempt += 1
                time.sleep(2)
        
        return results
    
    def detect_and_handle_popups(self, popup_strategies):
        """Detect and handle popups that might interfere with interactions.
        
        Args:
            popup_strategies: Dict mapping (By.X, "locator") to action function
            
        Returns:
            bool: True if any popup was handled, False otherwise
        """
        handled_any = False
        
        for (by, locator), action in popup_strategies.items():
            try:
                elements = self.driver.find_elements(by, locator)
                for element in elements:
                    if element.is_displayed():
                        self.logger.info(f"Detected popup with {by}={locator}")
                        action(element)
                        self.logger.info("Handled popup")
                        handled_any = True
                        # Give time for popup to disappear
                        time.sleep(1)
            except Exception as e:
                self.logger.debug(f"Error handling popup {by}={locator}: {str(e)}")
        
        return handled_any
    
    def monitor_network_requests(self, url_pattern, timeout=30):
        """Monitor network requests for a specific URL pattern.
        
        Args:
            url_pattern: String pattern to match in URLs
            timeout: Maximum wait time in seconds
            
        Returns:
            list: Captured network requests matching the pattern
        """
        self.logger.info(f"Monitoring network requests for pattern: {url_pattern}")
        
        # Execute JavaScript to set up request monitoring
        script = """
        var pattern = arguments[0];
        var requests = [];
        
        // Create a performance observer
        if (window.PerformanceObserver) {
            var observer = new PerformanceObserver(function(list) {
                list.getEntries().forEach(function(entry) {
                    if (entry.name.includes(pattern)) {
                        requests.push({
                            url: entry.name,
                            duration: entry.duration,
                            startTime: entry.startTime,
                            initiatorType: entry.initiatorType
                        });
                    }
                });
            });
            
            observer.observe({entryTypes: ['resource']});
        }
        
        // Return a function to get the collected requests
        return function() {
            return requests;
        };
        """
        
        try:
            get_requests_func = self.driver.execute_script(script, url_pattern)
            
            # Wait for some time to collect requests
            start_time = time.time()
            while time.time() - start_time < timeout:
                time.sleep(2)
                requests = self.driver.execute_script("return arguments[0]();", get_requests_func)
                if requests and len(requests) > 0:
                    self.logger.info(f"Captured {len(requests)} matching network requests")
                    return requests
            
            self.logger.warning(f"No matching network requests captured within {timeout} seconds")
            return []
            
        except Exception as e:
            self.logger.error(f"Error monitoring network requests: {str(e)}")
            return []
