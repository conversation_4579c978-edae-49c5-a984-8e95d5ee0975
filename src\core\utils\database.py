import os
import sqlite3
import logging

# Setup logger
logger = logging.getLogger('zpammer.database')

class Database:
    """
    Database manager for Zpammer GUI.

    This class handles:
    - Connecting to SQLite database
    - Creating tables
    - Executing queries
    """

    def __init__(self, db_path=None, check_same_thread=False):
        """
        Initialize the database manager.

        Args:
            db_path (str, optional): Path to the database file.
            check_same_thread (bool, optional): Whether to check if the connection is used in the same thread.
                                              Set to False to allow using the connection in different threads.
        """
        if db_path is None:
            self.db_path = os.path.join("Files", "comments.db")
        else:
            self.db_path = db_path

        self.connection = None
        self.check_same_thread = check_same_thread

        # Connect to database and create tables if they don't exist
        if self.connect(check_same_thread=check_same_thread):
            self.create_tables()

    def __enter__(self):
        """
        Enter method for context manager support.

        Returns:
            Database: Self instance.
        """
        if not self.connection and not self.connect(check_same_thread=self.check_same_thread):
            raise Exception("Failed to connect to database")
        return self

    def __exit__(self, *_):
        """
        Exit method for context manager support.
        Closes the database connection.
        """
        self.close()

    def connect(self, check_same_thread=False):
        """
        Connect to the database.

        Args:
            check_same_thread (bool, optional): Whether to check if the connection is used in the same thread.
                                              Set to False to allow using the connection in different threads.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            # Usar check_same_thread=False para permitir el uso de la conexión en diferentes hilos
            # Esto soluciona el error: "SQLite objects created in a thread can only be used in that same thread"
            self.connection = sqlite3.connect(self.db_path, check_same_thread=check_same_thread)
            logger.info(f"Connected to database: {self.db_path} (check_same_thread={check_same_thread})")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            return False

    def close(self):
        """
        Close the database connection.

        Returns:
            bool: True if successful, False otherwise.
        """
        if self.connection:
            try:
                self.connection.close()
                self.connection = None
                logger.info("Closed database connection")
                return True
            except Exception as e:
                logger.error(f"Failed to close database connection: {e}")
                return False
        return True

    def execute(self, query, parameters=None, commit=False, max_retries=3):
        """
        Execute a query with retry logic and improved error handling.

        Args:
            query (str): SQL query.
            parameters (tuple, optional): Query parameters.
            commit (bool, optional): Whether to commit the transaction.
            max_retries (int, optional): Maximum number of retries on failure.

        Returns:
            cursor: SQLite cursor object or None if all retries fail.
        """
        if not query or not isinstance(query, str):
            logger.error(f"Invalid query: {query}")
            return None

        # Ensure we have a valid connection
        if not self.connection:
            logger.info("No active database connection, attempting to connect...")
            if not self.connect(check_same_thread=self.check_same_thread):
                logger.error("Failed to establish database connection")
                return None

        # Initialize retry counter
        retries = 0
        last_error = None

        while retries <= max_retries:
            try:
                # Enable foreign keys support
                self.connection.execute("PRAGMA foreign_keys = ON")

                # Convert rows to dictionaries
                self.connection.row_factory = sqlite3.Row

                cursor = self.connection.cursor()

                if parameters:
                    cursor.execute(query, parameters)
                else:
                    cursor.execute(query)

                if commit:
                    self.connection.commit()

                # If we get here, the query was successful
                if retries > 0:
                    logger.info(f"Query succeeded after {retries} retries")

                return cursor

            except sqlite3.OperationalError as e:
                last_error = e
                error_msg = str(e)
                retries += 1

                # Handle specific SQLite errors
                if "database is locked" in error_msg:
                    logger.warning(f"Database is locked, retrying ({retries}/{max_retries})...")
                    import time
                    time.sleep(0.5)  # Wait before retrying
                elif "no such table" in error_msg:
                    logger.error(f"Table does not exist: {error_msg}")
                    # Try to create the table if it's a common one
                    if "members" in error_msg or "groups" in error_msg:
                        logger.info(f"Attempting to create missing table mentioned in: {error_msg}")
                        self.create_tables()
                        retries += 1  # Count this as a retry
                    else:
                        break  # Don't retry for other missing tables
                else:
                    logger.error(f"SQLite operational error: {error_msg}")
                    break  # Don't retry for other operational errors

            except sqlite3.IntegrityError as e:
                # Don't retry integrity errors (like unique constraint violations)
                logger.error(f"SQLite integrity error: {e}")
                logger.error(f"Query: {query}")
                logger.error(f"Parameters: {parameters}")
                return None

            except Exception as e:
                last_error = e
                logger.error(f"Failed to execute query: {e}")
                logger.error(f"Query: {query}")
                logger.error(f"Parameters: {parameters}")
                retries += 1

                # Try reconnecting on connection errors
                if "not connected" in str(e).lower() or "connection" in str(e).lower():
                    logger.info("Attempting to reconnect to database...")
                    self.close()
                    if not self.connect():
                        logger.error("Failed to reconnect to database")
                        break
                else:
                    # Don't retry for other exceptions
                    break

        if retries > max_retries:
            logger.error(f"Query failed after {max_retries} retries. Last error: {last_error}")

        return None

    def create_tables(self):
        """
        Create necessary tables if they don't exist.

        Returns:
            bool: True if successful, False otherwise.
        """
        # Use reset_database with drop_tables=False to create tables without dropping existing ones
        return self.reset_database(drop_tables=False)

    def verify_database_integrity(self):
        """
        Verify the integrity of the database and attempt to fix any issues.

        This method checks:
        1. If the database file exists
        2. If all required tables exist
        3. If the database connection is valid
        4. If the database structure is valid (using PRAGMA integrity_check)

        Returns:
            dict: A dictionary with the verification results
        """
        results = {
            'status': 'success',
            'message': 'Database integrity verified successfully',
            'issues': [],
            'fixes': []
        }

        try:
            # Check if database file exists
            if not os.path.exists(self.db_path):
                results['status'] = 'error'
                results['issues'].append('Database file does not exist')
                results['message'] = 'Database file does not exist'

                # Try to create the database file
                try:
                    os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
                    self.connect(check_same_thread=self.check_same_thread)
                    self.create_tables()
                    results['fixes'].append('Created new database file and tables')
                except Exception as e:
                    results['issues'].append(f'Failed to create database file: {str(e)}')
                    return results

            # Ensure we have a valid connection
            if not self.connection:
                if not self.connect(check_same_thread=self.check_same_thread):
                    results['status'] = 'error'
                    results['issues'].append('Failed to connect to database')
                    results['message'] = 'Failed to connect to database'
                    return results

            # Check if all required tables exist
            required_tables = ['profiles', 'posts', 'comments', 'groups', 'members', 'settings']
            cursor = self.execute("SELECT name FROM sqlite_master WHERE type='table'")
            if cursor:
                existing_tables = [row[0] for row in cursor.fetchall()]
                missing_tables = [table for table in required_tables if table not in existing_tables]

                if missing_tables:
                    results['status'] = 'warning'
                    results['issues'].append(f'Missing tables: {missing_tables}')

                    # Try to create missing tables
                    self.create_tables()
                    results['fixes'].append('Created missing tables')
            else:
                results['status'] = 'error'
                results['issues'].append('Failed to query database tables')
                results['message'] = 'Failed to query database tables'
                return results

            # Run integrity check
            cursor = self.execute("PRAGMA integrity_check")
            if cursor:
                integrity_result = cursor.fetchone()
                if integrity_result and integrity_result[0] != 'ok':
                    results['status'] = 'error'
                    results['issues'].append(f'Database integrity check failed: {integrity_result[0]}')
                    results['message'] = f'Database integrity check failed: {integrity_result[0]}'

                    # Try to fix by creating a new database and copying data
                    try:
                        # Backup the corrupted database
                        import shutil
                        import datetime
                        backup_path = f"{self.db_path}.corrupted.{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
                        shutil.copy2(self.db_path, backup_path)
                        results['fixes'].append(f'Created backup of corrupted database at {backup_path}')

                        # Create a new database
                        self.close()
                        os.remove(self.db_path)
                        self.connect(check_same_thread=self.check_same_thread)
                        self.create_tables()
                        results['fixes'].append('Created new database with fresh tables')
                        results['message'] = 'Database was corrupted but has been recreated. Some data may have been lost.'
                    except Exception as e:
                        results['issues'].append(f'Failed to fix corrupted database: {str(e)}')
            else:
                results['status'] = 'warning'
                results['issues'].append('Failed to run integrity check')

            # Final status update
            if results['status'] == 'success' and results['issues']:
                results['status'] = 'warning'
                results['message'] = 'Database integrity verified with warnings'

            return results
        except Exception as e:
            results['status'] = 'error'
            results['issues'].append(f'Exception during database verification: {str(e)}')
            results['message'] = f'Exception during database verification: {str(e)}'
            return results

    # Profile methods
    def get_profiles(self, status="active"):
        """
        Get all profiles with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            list: List of profile dictionaries.
        """
        query = "SELECT * FROM profiles"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            # Convert rows to dictionaries
            columns = [column[0] for column in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        return []

    def get_profile(self, profile_id):
        """
        Get a profile by ID.

        Args:
            profile_id (str): Profile ID.

        Returns:
            dict: Profile dictionary or None if not found.
        """
        cursor = self.execute(
            "SELECT * FROM profiles WHERE profile_id = ? LIMIT 1",
            (profile_id,)
        )

        if cursor:
            row = cursor.fetchone()
            if row:
                columns = [column[0] for column in cursor.description]
                return dict(zip(columns, row))

        return None

    def save_profile(self, profile_id, name=None, status="active"):
        """
        Save a profile to the database.

        Args:
            profile_id (str): Profile ID.
            name (str, optional): Profile name.
            status (str, optional): Profile status.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Check if profile exists
            existing_profile = self.get_profile(profile_id)

            if existing_profile:
                # Update existing profile
                result = self.execute(
                    "UPDATE profiles SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE profile_id = ?",
                    (status, profile_id),
                    commit=True
                )
                if not result:
                    return False

                # If name is provided, update it too
                if name is not None:
                    result = self.execute(
                        "UPDATE profiles SET name = ? WHERE profile_id = ?",
                        (name, profile_id),
                        commit=True
                    )
                    if not result:
                        return False
            else:
                # Insert new profile
                result = self.execute(
                    "INSERT INTO profiles (profile_id, name, status) VALUES (?, ?, ?)",
                    (profile_id, name or profile_id, status),
                    commit=True
                )
                if not result:
                    return False

            # Force commit to ensure changes are saved
            if self.connection:
                self.connection.commit()

            return True
        except Exception as e:
            logger.error(f"Failed to save profile {profile_id}: {e}")
            return False

    def delete_profile(self, profile_id):
        """
        Delete a profile from the database.

        Args:
            profile_id (str): Profile ID.

        Returns:
            bool: True if successful, False otherwise.
        """
        cursor = self.execute(
            "DELETE FROM profiles WHERE profile_id = ?",
            (profile_id,),
            commit=True
        )

        if cursor:
            return cursor.rowcount > 0

        return False

    def clear_profiles(self):
        """
        Clear all profiles from the database.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            result = self.execute(
                "DELETE FROM profiles",
                commit=True
            )

            if not result:
                return False

            # Force commit to ensure changes are saved
            if self.connection:
                self.connection.commit()

            return True
        except Exception as e:
            logger.error(f"Failed to clear profiles: {e}")
            return False

    def get_profiles_count(self, status="active"):
        """
        Get the number of profiles with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of profiles.
        """
        query = "SELECT COUNT(*) FROM profiles"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            result = cursor.fetchone()
            return result[0] if result else 0

        return 0

    # Post methods
    def get_posts(self, status="active"):
        """
        Get all posts with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            list: List of post dictionaries.
        """
        try:
            # Ensure the metadata column exists
            self._ensure_metadata_column()

            query = "SELECT * FROM posts"
            params = ()

            if status is not None:
                query += " WHERE status = ?"
                params = (status,)

            cursor = self.execute(query, params)

            if cursor:
                # Convert rows to dictionaries
                columns = [column[0] for column in cursor.description]
                posts = [dict(zip(columns, row)) for row in cursor.fetchall()]
                print(f"Retrieved {len(posts)} posts from database")
                return posts

            return []
        except Exception as e:
            print(f"Error getting posts: {e}")
            return []

    def get_post(self, post_url):
        """
        Get a post by URL.

        Args:
            post_url (str): Post URL.

        Returns:
            dict: Post dictionary or None if not found.
        """
        cursor = self.execute(
            "SELECT * FROM posts WHERE post_url = ? LIMIT 1",
            (post_url,)
        )

        if cursor:
            row = cursor.fetchone()
            if row:
                columns = [column[0] for column in cursor.description]
                return dict(zip(columns, row))

        return None

    def save_post(self, post_url, group_id=None, status="active", metadata=None):
        """
        Save a post to the database.

        Args:
            post_url (str): Post URL.
            group_id (str, optional): Group ID.
            status (str, optional): Post status.
            metadata (str, optional): Additional metadata for the post (engagement metrics).

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Check if post exists
            existing_post = self.get_post(post_url)

            # Ensure the metadata column exists
            self._ensure_metadata_column()

            # Make sure the database connection is valid
            if not self.connection:
                print("Database connection is not valid, reconnecting...")
                self.connect()

            # Make sure metadata is a string
            if metadata is not None and not isinstance(metadata, str):
                metadata = str(metadata)

            if existing_post:
                # Update existing post
                cursor = self.execute(
                    "UPDATE posts SET group_id = ?, status = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP WHERE post_url = ?",
                    (group_id, status, metadata, post_url),
                    commit=True
                )
            else:
                # Insert new post
                cursor = self.execute(
                    "INSERT INTO posts (post_url, group_id, status, metadata) VALUES (?, ?, ?, ?)",
                    (post_url, group_id, status, metadata),
                    commit=True
                )

            if cursor:
                print(f"Successfully saved post: {post_url}")
                return cursor.rowcount > 0

            return False
        except Exception as e:
            print(f"Error saving post: {e}")
            return False

    def _ensure_metadata_column(self):
        """Ensure the metadata column exists in the posts table."""
        try:
            # Make sure the database connection is valid
            if not self.connection:
                print("Database connection is not valid, reconnecting...")
                self.connect()

            # Check if posts table exists
            cursor = self.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='posts'")
            if not cursor or not cursor.fetchone():
                # Create posts table if it doesn't exist
                self.execute('''
                CREATE TABLE IF NOT EXISTS posts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_url TEXT UNIQUE NOT NULL,
                    group_id TEXT,
                    status TEXT DEFAULT 'active',
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                ''', commit=True)
                print("Created posts table with metadata column")
                return True

            # Check if metadata column exists
            cursor = self.execute("PRAGMA table_info(posts)")
            if not cursor:
                return False

            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'metadata' not in column_names:
                # Add metadata column
                self.execute("ALTER TABLE posts ADD COLUMN metadata TEXT", commit=True)
                print("Added metadata column to posts table")
            return True
        except Exception as e:
            print(f"Error ensuring metadata column: {e}")
            return False

    def update_post_metadata(self, post_url, metadata):
        """
        Update the metadata for a post.

        Args:
            post_url (str): Post URL.
            metadata (str): New metadata for the post.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Ensure the metadata column exists
            self._ensure_metadata_column()

            # Make sure the database connection is valid
            if not self.connection:
                print("Database connection is not valid, reconnecting...")
                self.connect()

            # Make sure metadata is a string
            if metadata is not None and not isinstance(metadata, str):
                metadata = str(metadata)

            # Update the post metadata
            cursor = self.execute(
                "UPDATE posts SET metadata = ?, updated_at = CURRENT_TIMESTAMP WHERE post_url = ?",
                (metadata, post_url),
                commit=True
            )

            if cursor:
                print(f"Successfully updated metadata for post: {post_url}")
                return cursor.rowcount > 0

            return False
        except Exception as e:
            print(f"Error updating post metadata: {e}")
            return False

    def delete_post(self, post_url):
        """
        Delete a post from the database.

        Args:
            post_url (str): Post URL.

        Returns:
            bool: True if successful, False otherwise.
        """
        cursor = self.execute(
            "DELETE FROM posts WHERE post_url = ?",
            (post_url,),
            commit=True
        )

        if cursor:
            return cursor.rowcount > 0

        return False

    def clear_posts(self):
        """
        Clear all posts from the database.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            result = self.execute(
                "DELETE FROM posts",
                commit=True
            )

            if not result:
                return False

            # Force commit to ensure changes are saved
            if self.connection:
                self.connection.commit()

            return True
        except Exception as e:
            print(f"Failed to clear posts: {e}")
            return False

    def get_posts_count(self, status="active"):
        """
        Get the number of posts with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of posts.
        """
        query = "SELECT COUNT(*) FROM posts"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            result = cursor.fetchone()
            return result[0] if result else 0

        return 0

    # Comment methods
    def get_comments(self, status="active"):
        """
        Get all comments with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            list: List of comment dictionaries.
        """
        query = "SELECT * FROM comments"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            # Convert rows to dictionaries
            columns = [column[0] for column in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        return []

    def check_comment_exists(self, comment_id):
        """
        Check if a comment ID exists in the database.

        Args:
            comment_id (str): Comment ID to check.

        Returns:
            bool: True if exists, False otherwise.
        """
        cursor = self.execute(
            "SELECT 1 FROM comments WHERE comment_id = ? LIMIT 1",
            (comment_id,)
        )

        if cursor:
            result = cursor.fetchone()
            return result is not None

        return False

    def save_comment(self, comment_id, content, profile_id=None, post_id=None, status="active"):
        """
        Save a comment to the database.

        Args:
            comment_id (str): Comment ID.
            content (str): Comment content.
            profile_id (str, optional): Profile ID.
            post_id (str, optional): Post ID.
            status (str, optional): Comment status.

        Returns:
            bool: True if successful, False otherwise.
        """
        # Check if comment exists
        if self.check_comment_exists(comment_id):
            # Update existing comment
            cursor = self.execute(
                "UPDATE comments SET content = ?, profile_id = ?, post_id = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE comment_id = ?",
                (content, profile_id, post_id, status, comment_id),
                commit=True
            )
        else:
            # Insert new comment
            cursor = self.execute(
                "INSERT INTO comments (comment_id, content, profile_id, post_id, status) VALUES (?, ?, ?, ?, ?)",
                (comment_id, content, profile_id, post_id, status),
                commit=True
            )

        if cursor:
            return cursor.rowcount > 0

        return False

    def delete_comment(self, comment_id):
        """
        Delete a comment from the database.

        Args:
            comment_id (str): Comment ID.

        Returns:
            bool: True if successful, False otherwise.
        """
        cursor = self.execute(
            "DELETE FROM comments WHERE comment_id = ?",
            (comment_id,),
            commit=True
        )

        if cursor:
            return cursor.rowcount > 0

        return False

    def get_comments_count(self, status="active"):
        """
        Get the number of comments with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of comments.
        """
        query = "SELECT COUNT(*) FROM comments"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            result = cursor.fetchone()
            return result[0] if result else 0

        return 0

    # Group methods
    def get_groups(self, status="active"):
        """
        Get all groups with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            list: List of group dictionaries.
        """
        query = "SELECT * FROM groups"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            # Convert rows to dictionaries
            columns = [column[0] for column in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        return []

    def get_group(self, group_url):
        """
        Get a group by URL.

        Args:
            group_url (str): Group URL.

        Returns:
            dict: Group dictionary or None if not found.
        """
        cursor = self.execute(
            "SELECT * FROM groups WHERE group_url = ? LIMIT 1",
            (group_url,)
        )

        if cursor:
            row = cursor.fetchone()
            if row:
                columns = [column[0] for column in cursor.description]
                return dict(zip(columns, row))

        return None

    def save_group(self, group_url, name=None, status="active"):
        """
        Save a group to the database.

        Args:
            group_url (str): Group URL.
            name (str, optional): Group name.
            status (str, optional): Group status.

        Returns:
            dict: A dictionary with keys:
                - 'status': 'new', 'updated', 'error', or 'success'
                - 'message': A message describing the result
                - 'group': The group data if successful
                - 'error': The error message if status is 'error'
        """
        try:
            # Validate inputs
            if not group_url or not isinstance(group_url, str):
                return {
                    'status': 'error',
                    'message': 'Invalid group URL',
                    'error': f'Group URL must be a non-empty string, got: {type(group_url)}'
                }

            # Sanitize group_url
            group_url = group_url.strip()
            if not group_url:
                return {
                    'status': 'error',
                    'message': 'Empty group URL after sanitization',
                    'error': 'Group URL contains only whitespace'
                }

            # Check if the groups table exists
            cursor = self.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='groups'")
            if cursor and not cursor.fetchone():
                # Create groups table if it doesn't exist
                self.execute('''
                CREATE TABLE IF NOT EXISTS groups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_url TEXT UNIQUE NOT NULL,
                    name TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                ''', commit=True)
                logger.info("Created groups table")

            # Check if group exists
            existing_group = self.get_group(group_url)

            if existing_group:
                # Update existing group
                cursor = self.execute(
                    "UPDATE groups SET name = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE group_url = ?",
                    (name, status, group_url),
                    commit=True
                )
                if cursor and cursor.rowcount > 0:
                    # Return the updated group
                    updated_group = self.get_group(group_url)
                    if updated_group:
                        return {
                            'status': 'updated',
                            'message': f'Group {group_url} updated successfully',
                            'group': updated_group
                        }
            else:
                # Insert new group
                cursor = self.execute(
                    "INSERT INTO groups (group_url, name, status) VALUES (?, ?, ?)",
                    (group_url, name, status),
                    commit=True
                )
                if cursor and cursor.rowcount > 0:
                    # Return the new group
                    new_group = self.get_group(group_url)
                    if new_group:
                        return {
                            'status': 'new',
                            'message': f'Group {group_url} created successfully',
                            'group': new_group
                        }

            return {
                'status': 'error',
                'message': f'Failed to save group {group_url}',
                'error': 'Database operation returned no rows affected or group not found after save'
            }
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to save group {group_url}: {error_msg}")
            return {
                'status': 'error',
                'message': f'Exception while saving group {group_url}',
                'error': error_msg
            }

    def delete_group(self, group_url):
        """
        Delete a group from the database.

        Args:
            group_url (str): Group URL.

        Returns:
            bool: True if successful, False otherwise.
        """
        cursor = self.execute(
            "DELETE FROM groups WHERE group_url = ?",
            (group_url,),
            commit=True
        )

        if cursor:
            return cursor.rowcount > 0

        return False

    def get_groups_count(self, status="active"):
        """
        Get the number of groups with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of groups.
        """
        query = "SELECT COUNT(*) FROM groups"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            result = cursor.fetchone()
            return result[0] if result else 0

        return 0

    # Member methods
    def get_members(self, group_id=None, status="active"):
        """
        Get all members with the specified status.

        Args:
            group_id (int, optional): Group ID filter.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            list: List of member dictionaries.
        """
        query = "SELECT * FROM members"
        params = ()
        conditions = []

        if group_id is not None:
            conditions.append("group_id = ?")
            params = params + (group_id,)

        if status is not None:
            conditions.append("status = ?")
            params = params + (status,)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        cursor = self.execute(query, params)

        if cursor:
            # Convert rows to dictionaries
            columns = [column[0] for column in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        return []

    def get_member(self, member_id):
        """
        Get a member by ID.

        Args:
            member_id (str): Member ID.

        Returns:
            dict: Member dictionary or None if not found.
        """
        cursor = self.execute(
            "SELECT * FROM members WHERE member_id = ? LIMIT 1",
            (member_id,)
        )

        if cursor:
            row = cursor.fetchone()
            if row:
                columns = [column[0] for column in cursor.description]
                return dict(zip(columns, row))

        return None

    def save_member(self, member_id, name=None, group_id=None, status="active"):
        """
        Save a member to the database.

        Args:
            member_id (str): Member ID.
            name (str, optional): Member name.
            group_id (int, optional): Group ID.
            status (str, optional): Member status.

        Returns:
            dict: A dictionary with keys:
                - 'status': 'new', 'updated', or 'error'
                - 'message': A message describing the result
                - 'error': The error message if status is 'error'
        """
        try:
            # Validate inputs
            if not member_id or not isinstance(member_id, str):
                return {
                    'status': 'error',
                    'message': 'Invalid member ID',
                    'error': f'Member ID must be a non-empty string, got: {type(member_id)}'
                }

            # Sanitize member_id - remove any characters that might cause SQL injection
            member_id = member_id.strip()
            if not member_id:
                return {
                    'status': 'error',
                    'message': 'Empty member ID after sanitization',
                    'error': 'Member ID contains only whitespace'
                }

            # Validate group_id if provided
            if group_id is not None:
                try:
                    group_id = int(group_id)
                except (ValueError, TypeError):
                    return {
                        'status': 'error',
                        'message': f'Invalid group ID: {group_id}',
                        'error': f'Group ID must be an integer, got: {type(group_id)}'
                    }

            # Check if member exists
            existing_member = self.get_member(member_id)

            if existing_member:
                # Update existing member
                cursor = self.execute(
                    "UPDATE members SET name = ?, group_id = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE member_id = ?",
                    (name, group_id, status, member_id),
                    commit=True
                )
                if cursor and cursor.rowcount > 0:
                    return {
                        'status': 'updated',
                        'message': f'Member {member_id} updated successfully',
                        'member_id': member_id
                    }
            else:
                # Insert new member
                cursor = self.execute(
                    "INSERT INTO members (member_id, name, group_id, status) VALUES (?, ?, ?, ?)",
                    (member_id, name, group_id, status),
                    commit=True
                )
                if cursor and cursor.rowcount > 0:
                    return {
                        'status': 'new',
                        'message': f'Member {member_id} created successfully',
                        'member_id': member_id
                    }

            return {
                'status': 'error',
                'message': f'Failed to save member {member_id}',
                'error': 'Database operation returned no rows affected'
            }
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Failed to save member {member_id}: {error_msg}")
            return {
                'status': 'error',
                'message': f'Exception while saving member {member_id}',
                'error': error_msg
            }

    def delete_member(self, member_id):
        """
        Delete a member from the database.

        Args:
            member_id (str): Member ID.

        Returns:
            bool: True if successful, False otherwise.
        """
        cursor = self.execute(
            "DELETE FROM members WHERE member_id = ?",
            (member_id,),
            commit=True
        )

        if cursor:
            return cursor.rowcount > 0

        return False

    def get_members_count(self, group_id=None, status="active"):
        """
        Get the number of members with the specified status.

        Args:
            group_id (int, optional): Group ID filter.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of members.
        """
        query = "SELECT COUNT(*) FROM members"
        params = ()
        conditions = []

        if group_id is not None:
            conditions.append("group_id = ?")
            params = params + (group_id,)

        if status is not None:
            conditions.append("status = ?")
            params = params + (status,)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        cursor = self.execute(query, params)

        if cursor:
            result = cursor.fetchone()
            return result[0] if result else 0

        return 0

    # Account methods
    def get_accounts(self, status="active"):
        """
        Get all accounts with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            list: List of account dictionaries.
        """
        query = "SELECT * FROM accounts"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            # Convert rows to dictionaries
            columns = [column[0] for column in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        return []

    def get_account(self, account_id):
        """
        Get an account by ID.

        Args:
            account_id (int): Account ID.

        Returns:
            dict: Account dictionary or None if not found.
        """
        cursor = self.execute(
            "SELECT * FROM accounts WHERE id = ? LIMIT 1",
            (account_id,)
        )

        if cursor:
            row = cursor.fetchone()
            if row:
                columns = [column[0] for column in cursor.description]
                return dict(zip(columns, row))

        return None

    def save_account(self, fb_id=None, profile_id=None, browser_id=None, username=None, password=None, status="active"):
        """
        Save an account to the database.

        Args:
            fb_id (str, optional): Facebook ID.
            profile_id (str, optional): Profile ID.
            browser_id (str, optional): Browser ID.
            username (str, optional): Username.
            password (str, optional): Password.
            status (str, optional): Account status.

        Returns:
            int: Account ID if successful, None otherwise.
        """
        # Check if account exists by profile_id
        account_id = None
        if profile_id:
            cursor = self.execute(
                "SELECT id FROM accounts WHERE profile_id = ? LIMIT 1",
                (profile_id,)
            )
            if cursor:
                row = cursor.fetchone()
                if row:
                    account_id = row[0]

        if account_id:
            # Update existing account
            cursor = self.execute(
                "UPDATE accounts SET fb_id = ?, browser_id = ?, username = ?, password = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (fb_id, browser_id, username, password, status, account_id),
                commit=True
            )
            if cursor and cursor.rowcount > 0:
                return account_id
        else:
            # Insert new account
            cursor = self.execute(
                "INSERT INTO accounts (fb_id, profile_id, browser_id, username, password, status) VALUES (?, ?, ?, ?, ?, ?)",
                (fb_id, profile_id, browser_id, username, password, status),
                commit=True
            )
            if cursor:
                return cursor.lastrowid

        return None

    def delete_account(self, account_id):
        """
        Delete an account from the database.

        Args:
            account_id (int): Account ID.

        Returns:
            bool: True if successful, False otherwise.
        """
        cursor = self.execute(
            "DELETE FROM accounts WHERE id = ?",
            (account_id,),
            commit=True
        )

        if cursor:
            return cursor.rowcount > 0

        return False

    def get_accounts_count(self, status="active"):
        """
        Get the number of accounts with the specified status.

        Args:
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of accounts.
        """
        query = "SELECT COUNT(*) FROM accounts"
        params = ()

        if status is not None:
            query += " WHERE status = ?"
            params = (status,)

        cursor = self.execute(query, params)

        if cursor:
            result = cursor.fetchone()
            return result[0] if result else 0

        return 0

    # Settings methods
    def get_setting(self, key):
        """
        Get a setting by key.

        Args:
            key (str): Setting key.

        Returns:
            str: Setting value or None if not found.
        """
        cursor = self.execute(
            "SELECT value FROM settings WHERE key = ? LIMIT 1",
            (key,)
        )

        if cursor:
            row = cursor.fetchone()
            if row:
                return row[0]

        return None

    def save_setting(self, key, value, category=None):
        """
        Save a setting to the database.

        Args:
            key (str): Setting key.
            value (str): Setting value.
            category (str, optional): Setting category.

        Returns:
            bool: True if successful, False otherwise.
        """
        # Check if setting exists
        existing_value = self.get_setting(key)

        if existing_value is not None:
            # Update existing setting
            cursor = self.execute(
                "UPDATE settings SET value = ?, category = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
                (value, category, key),
                commit=True
            )
        else:
            # Insert new setting
            cursor = self.execute(
                "INSERT INTO settings (key, value, category) VALUES (?, ?, ?)",
                (key, value, category),
                commit=True
            )

        if cursor:
            return cursor.rowcount > 0

        return False

    def delete_setting(self, key):
        """
        Delete a setting from the database.

        Args:
            key (str): Setting key.

        Returns:
            bool: True if successful, False otherwise.
        """
        cursor = self.execute(
            "DELETE FROM settings WHERE key = ?",
            (key,),
            commit=True
        )

        if cursor:
            return cursor.rowcount > 0

        return False

    def get_settings_by_category(self, category):
        """
        Get all settings in a category.

        Args:
            category (str): Setting category.

        Returns:
            dict: Dictionary of key-value pairs.
        """
        cursor = self.execute(
            "SELECT key, value FROM settings WHERE category = ?",
            (category,)
        )

        if cursor:
            return {row[0]: row[1] for row in cursor.fetchall()}

        return {}

    def clear_settings(self, category=None):
        """
        Clear all settings or settings in a specific category.

        Args:
            category (str, optional): Setting category. If None, all settings are cleared.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            if category:
                cursor = self.execute(
                    "DELETE FROM settings WHERE category = ?",
                    (category,),
                    commit=True
                )
            else:
                cursor = self.execute(
                    "DELETE FROM settings",
                    commit=True
                )

            if cursor:
                logger.info(f"Cleared settings{' for category: ' + category if category else ''}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to clear settings: {e}")
            return False

    def reset_database(self, drop_tables=True):
        """
        Reset the database by dropping all tables and recreating them.

        Args:
            drop_tables (bool, optional): Whether to drop existing tables. Default is True.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            if drop_tables:
                # Drop all tables
                tables = [
                    "profiles", "posts", "comments", "accounts",
                    "groups", "members", "comment_history", "settings"
                ]

                for table in tables:
                    self.execute(f"DROP TABLE IF EXISTS {table}", commit=True)

                logger.info("All tables dropped successfully")

            # Recreate tables
            # Create profiles table
            self.execute('''
            CREATE TABLE IF NOT EXISTS profiles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                profile_id TEXT UNIQUE NOT NULL,
                name TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''', commit=True)

            # Create posts table
            self.execute('''
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                post_url TEXT UNIQUE NOT NULL,
                group_id TEXT,
                status TEXT DEFAULT 'active',
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''', commit=True)

            # Create comments table
            self.execute('''
            CREATE TABLE IF NOT EXISTS comments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                comment_id TEXT UNIQUE NOT NULL,
                content TEXT NOT NULL,
                profile_id TEXT,
                post_id TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''', commit=True)

            # Create accounts table
            self.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                fb_id TEXT,
                profile_id TEXT,
                browser_id TEXT,
                username TEXT,
                password TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''', commit=True)

            # Create groups table
            self.execute('''
            CREATE TABLE IF NOT EXISTS groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                group_url TEXT UNIQUE NOT NULL,
                name TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''', commit=True)

            # Create members table
            self.execute('''
            CREATE TABLE IF NOT EXISTS members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                member_id TEXT UNIQUE NOT NULL,
                name TEXT,
                group_id INTEGER,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (group_id) REFERENCES groups (id)
            )
            ''', commit=True)

            # Create comment_history table
            self.execute('''
            CREATE TABLE IF NOT EXISTS comment_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                profile_id INTEGER,
                post_id INTEGER,
                comment_id INTEGER,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (profile_id) REFERENCES profiles (id),
                FOREIGN KEY (post_id) REFERENCES posts (id),
                FOREIGN KEY (comment_id) REFERENCES comments (id)
            )
            ''', commit=True)

            # Create settings table
            self.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                category TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''', commit=True)

            # Create indexes for faster lookups
            self.execute('CREATE INDEX IF NOT EXISTS idx_comment_id ON comments (comment_id)', commit=True)
            self.execute('CREATE INDEX IF NOT EXISTS idx_profile_id ON profiles (profile_id)', commit=True)
            self.execute('CREATE INDEX IF NOT EXISTS idx_post_url ON posts (post_url)', commit=True)
            self.execute('CREATE INDEX IF NOT EXISTS idx_group_url ON groups (group_url)', commit=True)
            self.execute('CREATE INDEX IF NOT EXISTS idx_member_id ON members (member_id)', commit=True)
            self.execute('CREATE INDEX IF NOT EXISTS idx_settings_key ON settings (key)', commit=True)

            logger.info("Database tables created/verified successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to reset database: {e}")
            return False

    # Search methods
    def search_profiles(self, query, status=None, limit=100, offset=0):
        """
        Search profiles by query.

        Args:
            query (str): Search query.
            status (str, optional): Filter by status.
            limit (int, optional): Maximum number of results.
            offset (int, optional): Offset for pagination.

        Returns:
            list: List of matching profiles.
        """
        try:
            sql = "SELECT * FROM profiles WHERE profile_id LIKE ? OR name LIKE ?"
            params = [f"%{query}%", f"%{query}%"]

            if status:
                sql += " AND status = ?"
                params.append(status)

            sql += " ORDER BY id DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor = self.execute(sql, tuple(params))
            if cursor:
                return [dict(row) for row in cursor.fetchall()]
            return []
        except Exception as e:
            logger.error(f"Failed to search profiles: {e}")
            return []

    def search_posts(self, query, status=None, limit=100, offset=0):
        """
        Search posts by query.

        Args:
            query (str): Search query.
            status (str, optional): Filter by status.
            limit (int, optional): Maximum number of results.
            offset (int, optional): Offset for pagination.

        Returns:
            list: List of matching posts.
        """
        try:
            sql = "SELECT * FROM posts WHERE post_url LIKE ? OR group_id LIKE ?"
            params = [f"%{query}%", f"%{query}%"]

            if status:
                sql += " AND status = ?"
                params.append(status)

            sql += " ORDER BY id DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor = self.execute(sql, tuple(params))
            if cursor:
                return [dict(row) for row in cursor.fetchall()]
            return []
        except Exception as e:
            logger.error(f"Failed to search posts: {e}")
            return []

    def search_comments(self, query, status=None, limit=100, offset=0):
        """
        Search comments by query.

        Args:
            query (str): Search query.
            status (str, optional): Filter by status.
            limit (int, optional): Maximum number of results.
            offset (int, optional): Offset for pagination.

        Returns:
            list: List of matching comments.
        """
        try:
            sql = "SELECT * FROM comments WHERE content LIKE ? OR comment_id LIKE ?"
            params = [f"%{query}%", f"%{query}%"]

            if status:
                sql += " AND status = ?"
                params.append(status)

            sql += " ORDER BY id DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor = self.execute(sql, tuple(params))
            if cursor:
                return [dict(row) for row in cursor.fetchall()]
            return []
        except Exception as e:
            logger.error(f"Failed to search comments: {e}")
            return []

    def search_accounts(self, query, status=None, limit=100, offset=0):
        """
        Search accounts by query.

        Args:
            query (str): Search query.
            status (str, optional): Filter by status.
            limit (int, optional): Maximum number of results.
            offset (int, optional): Offset for pagination.

        Returns:
            list: List of matching accounts.
        """
        try:
            sql = "SELECT * FROM accounts WHERE fb_id LIKE ? OR profile_id LIKE ? OR username LIKE ?"
            params = [f"%{query}%", f"%{query}%", f"%{query}%"]

            if status:
                sql += " AND status = ?"
                params.append(status)

            sql += " ORDER BY id DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor = self.execute(sql, tuple(params))
            if cursor:
                return [dict(row) for row in cursor.fetchall()]
            return []
        except Exception as e:
            logger.error(f"Failed to search accounts: {e}")
            return []

    def search_groups(self, query, status=None, limit=100, offset=0):
        """
        Search groups by query.

        Args:
            query (str): Search query.
            status (str, optional): Filter by status.
            limit (int, optional): Maximum number of results.
            offset (int, optional): Offset for pagination.

        Returns:
            list: List of matching groups.
        """
        try:
            sql = "SELECT * FROM groups WHERE group_url LIKE ? OR name LIKE ?"
            params = [f"%{query}%", f"%{query}%"]

            if status:
                sql += " AND status = ?"
                params.append(status)

            sql += " ORDER BY id DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor = self.execute(sql, tuple(params))
            if cursor:
                return [dict(row) for row in cursor.fetchall()]
            return []
        except Exception as e:
            logger.error(f"Failed to search groups: {e}")
            return []

    def search_members(self, query, group_id=None, status=None, limit=100, offset=0):
        """
        Search members by query.

        Args:
            query (str): Search query.
            group_id (int, optional): Filter by group ID.
            status (str, optional): Filter by status.
            limit (int, optional): Maximum number of results.
            offset (int, optional): Offset for pagination.

        Returns:
            list: List of matching members.
        """
        try:
            sql = "SELECT * FROM members WHERE member_id LIKE ? OR name LIKE ?"
            params = [f"%{query}%", f"%{query}%"]

            if group_id:
                sql += " AND group_id = ?"
                params.append(group_id)

            if status:
                sql += " AND status = ?"
                params.append(status)

            sql += " ORDER BY id DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor = self.execute(sql, tuple(params))
            if cursor:
                return [dict(row) for row in cursor.fetchall()]
            return []
        except Exception as e:
            logger.error(f"Failed to search members: {e}")
            return []

    # Statistics methods
    def get_statistics(self):
        """
        Get general statistics about the database.

        Returns:
            dict: Dictionary with statistics.
        """
        try:
            stats = {
                "profiles": {
                    "total": self.get_profiles_count(),
                    "active": self.get_profiles_count("active"),
                    "banned": self.get_profiles_count("banned"),
                    "not_logged_in": self.get_profiles_count("not_logged_in")
                },
                "posts": {
                    "total": self.get_posts_count(),
                    "active": self.get_posts_count("active")
                },
                "comments": {
                    "total": self.get_comments_count(),
                    "active": self.get_comments_count("active")
                },
                "accounts": {
                    "total": self.get_accounts_count(),
                    "active": self.get_accounts_count("active")
                },
                "groups": {
                    "total": self.get_groups_count(),
                    "active": self.get_groups_count("active")
                },
                "members": {
                    "total": self.get_members_count(),
                    "active": self.get_members_count("active")
                },
                "database_size": self.get_database_size()
            }
            return stats
        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return {}

    def get_database_size(self):
        """
        Get the size of the database file in bytes.

        Returns:
            int: Size in bytes.
        """
        try:
            import os
            return os.path.getsize(self.db_path)
        except Exception as e:
            logger.error(f"Failed to get database size: {e}")
            return 0

    def get_activity_stats(self, days=30):
        """
        Get activity statistics for the last X days.

        Args:
            days (int, optional): Number of days to look back.

        Returns:
            dict: Dictionary with activity statistics.
        """
        try:
            # Get date X days ago
            import datetime
            date_from = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime("%Y-%m-%d")

            # Get counts for each table by day
            stats = {
                "profiles": self.get_daily_counts("profiles", date_from),
                "posts": self.get_daily_counts("posts", date_from),
                "comments": self.get_daily_counts("comments", date_from),
                "accounts": self.get_daily_counts("accounts", date_from),
                "groups": self.get_daily_counts("groups", date_from),
                "members": self.get_daily_counts("members", date_from)
            }
            return stats
        except Exception as e:
            logger.error(f"Failed to get activity statistics: {e}")
            return {}

    def get_daily_counts(self, table, date_from):
        """
        Get daily counts for a table since a specific date.

        Args:
            table (str): Table name.
            date_from (str): Start date in YYYY-MM-DD format.

        Returns:
            list: List of daily counts.
        """
        try:
            sql = f"SELECT date(created_at) as date, COUNT(*) as count FROM {table} WHERE date(created_at) >= ? GROUP BY date(created_at) ORDER BY date"
            cursor = self.execute(sql, (date_from,))

            if cursor:
                # Convert rows to dictionaries with proper column names
                result = []
                for row in cursor.fetchall():
                    result.append({"date": row[0], "count": row[1]})
                return result
            return []
        except Exception as e:
            logger.error(f"Failed to get daily counts for {table}: {e}")
            return []

    # Import/Export methods
    def import_profiles_from_file(self, file_path):
        """
        Import profiles from a file.

        Args:
            file_path (str): Path to the file.

        Returns:
            int: Number of profiles imported.
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                profiles = [line.strip() for line in f.readlines() if line.strip()]

            count = 0
            for profile_id in profiles:
                if self.save_profile(profile_id):
                    count += 1

            logger.info(f"Imported {count} profiles from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Failed to import profiles from {file_path}: {e}")
            return 0

    def export_profiles_to_file(self, file_path, status="active"):
        """
        Export profiles to a file.

        Args:
            file_path (str): Path to the file.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of profiles exported.
        """
        try:
            profiles = self.get_profiles(status)

            with open(file_path, "w", encoding="utf-8") as f:
                for profile in profiles:
                    f.write(f"{profile['profile_id']}\n")

            logger.info(f"Exported {len(profiles)} profiles to {file_path}")
            return len(profiles)
        except Exception as e:
            logger.error(f"Failed to export profiles to {file_path}: {e}")
            return 0

    # Backup and Restore methods
    def backup_database(self, backup_path=None):
        """
        Create a backup of the database.

        Args:
            backup_path (str, optional): Path to save the backup file.

        Returns:
            str: Path to the backup file, or None if failed.
        """
        try:
            import shutil
            import datetime
            import os

            # Close current connection to ensure all data is written
            if self.connection:
                self.connection.close()
                self.connection = None

            # Generate backup filename if not provided
            if not backup_path:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
                os.makedirs(backup_dir, exist_ok=True)
                backup_path = os.path.join(backup_dir, f"zpammer_backup_{timestamp}.db")

            # Copy database file to backup location
            shutil.copy2(self.db_path, backup_path)

            # Reconnect to database
            self.connect()

            logger.info(f"Database backup created at {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Failed to create database backup: {e}")
            # Ensure we're reconnected even if backup fails
            self.connect()
            return None

    def restore_database(self, backup_path):
        """
        Restore database from a backup file.

        Args:
            backup_path (str): Path to the backup file.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            import shutil
            import os

            # Verify backup file exists
            if not os.path.exists(backup_path):
                logger.error(f"Backup file not found: {backup_path}")
                return False

            # Close current connection
            if self.connection:
                self.connection.close()
                self.connection = None

            # Create a backup of current database before restoring
            current_backup = self.backup_database()
            logger.info(f"Created safety backup before restore: {current_backup}")

            # Copy backup file to database location
            shutil.copy2(backup_path, self.db_path)

            # Reconnect to database
            self.connect()

            logger.info(f"Database restored from {backup_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to restore database: {e}")
            # Ensure we're reconnected even if restore fails
            self.connect()
            return False

    def list_backups(self):
        """
        List all available database backups.

        Returns:
            list: List of backup file paths.
        """
        try:
            import os
            import glob

            backup_dir = os.path.join(os.path.dirname(self.db_path), "backups")
            if not os.path.exists(backup_dir):
                return []

            # Get all .db files in the backups directory
            backup_files = glob.glob(os.path.join(backup_dir, "zpammer_backup_*.db"))

            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            return backup_files
        except Exception as e:
            logger.error(f"Failed to list database backups: {e}")
            return []

    def get_backup_info(self, backup_path):
        """
        Get information about a backup file.

        Args:
            backup_path (str): Path to the backup file.

        Returns:
            dict: Dictionary with backup information.
        """
        try:
            import os
            import datetime
            import sqlite3

            # Get file information
            file_size = os.path.getsize(backup_path)
            mod_time = os.path.getmtime(backup_path)
            mod_date = datetime.datetime.fromtimestamp(mod_time)

            # Connect to backup database to get table counts
            conn = sqlite3.connect(backup_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # Get table counts
            tables = ["profiles", "posts", "comments", "accounts", "groups", "members"]
            counts = {}

            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    row = cursor.fetchone()
                    counts[table] = row["count"] if row else 0
                except sqlite3.OperationalError:
                    # Table might not exist in this backup
                    counts[table] = 0

            conn.close()

            # Create info dictionary
            info = {
                "path": backup_path,
                "filename": os.path.basename(backup_path),
                "size": file_size,
                "size_formatted": self.format_size(file_size),
                "modified": mod_date.strftime("%Y-%m-%d %H:%M:%S"),
                "counts": counts
            }

            return info
        except Exception as e:
            logger.error(f"Failed to get backup info for {backup_path}: {e}")
            return {
                "path": backup_path,
                "filename": os.path.basename(backup_path),
                "error": str(e)
            }

    def format_size(self, size_bytes):
        """
        Format file size in human-readable format.

        Args:
            size_bytes (int): Size in bytes.

        Returns:
            str: Formatted size string.
        """
        import math
        if size_bytes == 0:
            return "0B"
        size_names = ("B", "KB", "MB", "GB", "TB")
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def import_posts_from_file(self, file_path):
        """
        Import posts from a file.

        Args:
            file_path (str): Path to the file.

        Returns:
            int: Number of posts imported.
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                posts = [line.strip() for line in f.readlines() if line.strip()]

            count = 0
            for post_url in posts:
                if self.save_post(post_url):
                    count += 1

            logger.info(f"Imported {count} posts from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Failed to import posts from {file_path}: {e}")
            return 0

    def export_posts_to_file(self, file_path, status="active"):
        """
        Export posts to a file.

        Args:
            file_path (str): Path to the file.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of posts exported.
        """
        try:
            posts = self.get_posts(status)

            with open(file_path, "w", encoding="utf-8") as f:
                for post in posts:
                    f.write(f"{post['post_url']}\n")

            logger.info(f"Exported {len(posts)} posts to {file_path}")
            return len(posts)
        except Exception as e:
            logger.error(f"Failed to export posts to {file_path}: {e}")
            return 0

    def import_comments_from_file(self, file_path):
        """
        Import comments from a file.

        Args:
            file_path (str): Path to the file.

        Returns:
            int: Number of comments imported.
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                comments = [line.strip() for line in f.readlines() if line.strip()]

            count = 0
            for comment in comments:
                # Generate a unique comment ID
                comment_id = f"comment_{hash(comment)}"
                if self.save_comment(comment_id, comment):
                    count += 1

            logger.info(f"Imported {count} comments from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Failed to import comments from {file_path}: {e}")
            return 0

    def export_comments_to_file(self, file_path, status="active"):
        """
        Export comments to a file.

        Args:
            file_path (str): Path to the file.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of comments exported.
        """
        try:
            comments = self.get_comments(status)

            with open(file_path, "w", encoding="utf-8") as f:
                for comment in comments:
                    f.write(f"{comment['content']}\n")

            logger.info(f"Exported {len(comments)} comments to {file_path}")
            return len(comments)
        except Exception as e:
            logger.error(f"Failed to export comments to {file_path}: {e}")
            return 0

    def import_groups_from_file(self, file_path):
        """
        Import groups from a file.

        Args:
            file_path (str): Path to the file.

        Returns:
            int: Number of groups imported.
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                groups = [line.strip() for line in f.readlines() if line.strip()]

            count = 0
            for group_url in groups:
                if self.save_group(group_url):
                    count += 1

            logger.info(f"Imported {count} groups from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Failed to import groups from {file_path}: {e}")
            return 0

    def export_groups_to_file(self, file_path, status="active"):
        """
        Export groups to a file.

        Args:
            file_path (str): Path to the file.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of groups exported.
        """
        try:
            groups = self.get_groups(status)

            with open(file_path, "w", encoding="utf-8") as f:
                for group in groups:
                    f.write(f"{group['group_url']}\n")

            logger.info(f"Exported {len(groups)} groups to {file_path}")
            return len(groups)
        except Exception as e:
            logger.error(f"Failed to export groups to {file_path}: {e}")
            return 0

    def import_members_from_file(self, file_path, group_id=None):
        """
        Import members from a file.

        Args:
            file_path (str): Path to the file.
            group_id (int, optional): Group ID.

        Returns:
            int: Number of members imported.
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                members = [line.strip() for line in f.readlines() if line.strip()]

            count = 0
            for member_id in members:
                if self.save_member(member_id, group_id=group_id):
                    count += 1

            logger.info(f"Imported {count} members from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Failed to import members from {file_path}: {e}")
            return 0

    def export_members_to_file(self, file_path, group_id=None, status="active"):
        """
        Export members to a file.

        Args:
            file_path (str): Path to the file.
            group_id (int, optional): Group ID filter.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of members exported.
        """
        try:
            members = self.get_members(group_id, status)

            with open(file_path, "w", encoding="utf-8") as f:
                for member in members:
                    f.write(f"{member['member_id']}\n")

            logger.info(f"Exported {len(members)} members to {file_path}")
            return len(members)
        except Exception as e:
            logger.error(f"Failed to export members to {file_path}: {e}")
            return 0

    def import_accounts_from_csv(self, file_path):
        """
        Import accounts from a CSV file.

        Args:
            file_path (str): Path to the CSV file.

        Returns:
            int: Number of accounts imported.
        """
        try:
            import csv

            count = 0
            with open(file_path, "r", encoding="utf-8", newline="") as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # Map CSV columns to database fields
                    fb_id = row.get("FB", "")
                    profile_id = row.get("Profile ID", "")
                    browser_id = row.get("Browser ID", "")
                    username = row.get("User", "")
                    password = row.get("Pass", "")
                    status = row.get("Statue", "active")

                    if self.save_account(fb_id, profile_id, browser_id, username, password, status):
                        count += 1

            logger.info(f"Imported {count} accounts from {file_path}")
            return count
        except Exception as e:
            logger.error(f"Failed to import accounts from {file_path}: {e}")
            return 0

    def export_accounts_to_csv(self, file_path, status="active"):
        """
        Export accounts to a CSV file.

        Args:
            file_path (str): Path to the CSV file.
            status (str, optional): Status filter. Use None for all statuses.

        Returns:
            int: Number of accounts exported.
        """
        try:
            import csv

            accounts = self.get_accounts(status)

            with open(file_path, "w", encoding="utf-8", newline="") as f:
                fieldnames = ["FB", "Profile ID", "Browser ID", "User", "Pass", "Statue"]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for account in accounts:
                    writer.writerow({
                        "FB": account.get("fb_id", ""),
                        "Profile ID": account.get("profile_id", ""),
                        "Browser ID": account.get("browser_id", ""),
                        "User": account.get("username", ""),
                        "Pass": account.get("password", ""),
                        "Statue": account.get("status", "active")
                    })

            logger.info(f"Exported {len(accounts)} accounts to {file_path}")
            return len(accounts)
        except Exception as e:
            logger.error(f"Failed to export accounts to {file_path}: {e}")
            return 0
